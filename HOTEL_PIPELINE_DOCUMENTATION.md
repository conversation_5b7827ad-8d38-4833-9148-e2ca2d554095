# Hotel Pipeline Documentation

This document describes the Hotel Pipeline, which migrates hotel data from Salesforce to PostgreSQL following the same architecture pattern as the existing Resort pipeline.

## Overview

The Hotel Pipeline is a comprehensive ETL solution that extracts hotel data from Salesforce `Hotel__c` and `Resort__c` objects and loads it into PostgreSQL with proper relational integrity. It handles complex multi-table relationships and foreign key lookups.

## Architecture

### System Components

1. **Source Data**: Salesforce objects (`Resort__c` and `Hotel__c`)
2. **ETL Pipeline**: Salesforce to PostgreSQL strategy
3. **MinIO Storage**: Raw and processed data persistence
4. **Target Database**: PostgreSQL with three related tables

### Data Flow

```
Salesforce (Hotel__c + Resort__c) → Extract → MinIO (Raw) → Transform → MinIO (Processed) → PostgreSQL (3 tables)
```

1. **Extract**: Fetch Hotel__c records with Resort__c relationships from Salesforce
2. **Transform**: Apply field mappings and prepare for multi-table loading
3. **Load**: Insert into destination, hotel, and product_category tables with proper foreign keys

## Database Schema

### Updated destination Table

```sql
CREATE TABLE destination (
    id SERIAL PRIMARY KEY,
    migrated_id VARCHAR(255),  -- NEW: Salesforce Resort__c.Id
    country VARCHAR(255),
    name VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    currency VARCHAR(10),
    description TEXT,
    pipeline_run_id INTEGER REFERENCES etl_pipeline_runs(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- New index
CREATE INDEX idx_destination_migrated_id ON destination(migrated_id);
```

### hotel Table

```sql
CREATE TABLE hotel (
    id SERIAL PRIMARY KEY,
    migrated_id VARCHAR(255),  -- Salesforce Hotel__c.Id
    name VARCHAR(255),
    destination_id INTEGER REFERENCES destination(id),
    category_id INTEGER REFERENCES product_category(id),
    pipeline_run_id INTEGER REFERENCES etl_pipeline_runs(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_hotel_migrated_id ON hotel(migrated_id);
CREATE INDEX idx_hotel_name ON hotel(name);
CREATE INDEX idx_hotel_destination_id ON hotel(destination_id);
CREATE INDEX idx_hotel_category_id ON hotel(category_id);
```

### Product Category Table

```sql
CREATE TABLE product_category (
    id SERIAL PRIMARY KEY,
    migrated_id VARCHAR(255),  -- Salesforce Hotel__c.Id (same as hotel)
    name VARCHAR(255),         -- Hotel name (same as hotel name)
    pipeline_run_id INTEGER REFERENCES etl_pipeline_runs(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_product_category_migrated_id ON product_category(migrated_id);
CREATE INDEX idx_product_category_name ON product_category(name);
```

## Data Mapping

### Source Data (Salesforce)

#### Hotel__c Object Fields
- `Id`: Unique hotel identifier
- `Name`: Hotel name
- `Resort__c`: Foreign key reference to Resort__c object

#### Resort__c Object (Existing)
- Already mapped to destination table via Resort Pipeline
- `Id` stored in `destination.migrated_id` field

### Target Mapping

#### 1. destination Table (Reused)
- **Source**: Resort__c object (via existing Resort Pipeline)
- **Key Field**: `migrated_id` = Resort__c.Id
- **Purpose**: Provides destination lookup for hotel

#### 2. hotel Table
| Target Field | Source Field | Description |
|--------------|--------------|-------------|
| `migrated_id` | Hotel__c.Id | Salesforce hotel identifier |
| `name` | Hotel__c.Name | Hotel name |
| `destination_id` | Lookup via Hotel__c.Resort__c | Foreign key to destination table |
| `category_id` | Auto-generated | Foreign key to product_category table |

#### 3. Product Category Table
| Target Field | Source Field | Description |
|--------------|--------------|-------------|
| `migrated_id` | Hotel__c.Id | Same as hotel migrated_id |
| `name` | Hotel__c.Name | Same as hotel name |

## Pipeline Configuration

### Source Configuration

```json
{
  "source_type": "salesforce",
  "source_object": "Hotel__c",
  "source_fields": [
    "Id",
    "Name",
    "Resort__c"
  ]
}
```

### Destination Configuration

```json
{
  "destination_type": "postgresql",
  "destination_table": "hotel",
  "destination_fields": {
    "migrated_id": "Id",
    "name": "Name",
    "resort_migrated_id": "Resort__c"
  }
}
```

### Transformation Configuration

```json
{
  "clean_nulls": false,
  "trim_whitespace": true,
  "field_transformations": {}
}
```

## Data Relationships

### Foreign Key Relationships

1. **Hotel → Destination**
   - `hotel.destination_id` → `destination.id`
   - Lookup: `Hotel__c.Resort__c` → `destination.migrated_id`

2. **Hotel → Product Category**
   - `hotel.category_id` → `product_category.id`
   - Same hotel data creates both records

3. **Pipeline Tracking**
   - All tables reference `etl_pipeline_runs.id` for audit trail

### Data Consistency Rules

- Each Hotel__c record creates exactly one hotel record
- Each Hotel__c record creates exactly one product_category record
- Hotel and product_category records share the same migrated_id
- Destination lookup is required for referential integrity
- Missing Resort__c relationships result in NULL destination_id

## Setup Instructions

### Prerequisites

1. **Existing Resort Pipeline**: Must be set up and have populated destination table
2. **Database Access**: PostgreSQL with appropriate permissions
3. **Salesforce Access**: Valid credentials with access to Hotel__c and Resort__c objects

### Installation Steps

#### 1. Automated Setup (Recommended)

```bash
cd backend
python setup_hotel_pipeline.py
```

This script will:
- Run database migrations
- Create pipeline configuration
- Verify complete setup
- Provide detailed status reporting

#### 2. Manual Setup

```bash
# Step 1: Run database migration
cd backend
alembic upgrade head

# Step 2: Create pipeline configuration
python create_hotel_pipeline.py

# Step 3: Verify setup
python -c "from create_hotel_pipeline import verify_hotel_pipeline; verify_hotel_pipeline()"
```

### Verification

After setup, verify the installation:

```sql
-- Check table structures
SELECT table_name, column_name, data_type 
FROM information_schema.columns 
WHERE table_name IN ('destination', 'hotel', 'product_category')
ORDER BY table_name, ordinal_position;

-- Verify pipeline configuration
SELECT name, source_object, destination_table, is_active 
FROM etl_pipelines 
WHERE name = 'Hotel Pipeline';
```

## Pipeline Execution

### Prerequisites for Execution

1. **Resort Data**: destination table must contain Resort__c data with migrated_id values
2. **Salesforce Authentication**: Valid credentials configured
3. **Hotel Data**: Hotel__c records with valid Resort__c references

### Execution Process

1. **Data Extraction**: Fetches Hotel__c records with Resort__c relationships
2. **Transformation**: Maps fields and prepares for multi-table loading
3. **Data Loading**:
   - Creates product_category record first
   - Looks up destination by Resort__c.Id
   - Creates hotel record with foreign key references
4. **Validation**: Ensures referential integrity across all tables

### Monitoring

- **Pipeline Runs**: Track execution via etl_pipeline_runs table
- **Data Validation**: Verify foreign key relationships
- **Error Handling**: Check logs for missing Resort__c references

## Troubleshooting

### Common Issues

1. **Missing destination**: Ensure Resort Pipeline has run and populated destination table
2. **Foreign Key Errors**: Verify Resort__c references exist in destination.migrated_id
3. **Duplicate Data**: Check for existing hotel records before re-running pipeline

### Debug Queries

```sql
-- Check destination lookup coverage
SELECT 
    COUNT(*) as total_hotel,
    COUNT(destination_id) as hotel_with_destination,
    COUNT(*) - COUNT(destination_id) as orphaned_hotel
FROM hotel;

-- Find hotel without destination
SELECT h.name, h.migrated_id 
FROM hotel h 
WHERE h.destination_id IS NULL;

-- Verify data consistency between hotel and product_category
SELECT 
    h.migrated_id,
    h.name as hotel_name,
    pc.name as category_name
FROM hotel h
JOIN product_category pc ON h.category_id = pc.id
WHERE h.migrated_id != pc.migrated_id OR h.name != pc.name;
```

## Performance Considerations

- **Indexes**: All foreign key and lookup fields are indexed
- **Batch Processing**: Pipeline processes records in batches
- **Foreign Key Lookups**: Optimized with indexed migrated_id fields
- **Data Storage**: Raw and processed data stored in MinIO for replay capability

## Important Notes

### Resort Pipeline Dependency

The Hotel Pipeline requires the Resort Pipeline to be updated to populate the `migrated_id` field in the destination table. This field is essential for the foreign key lookup between hotel and destination.

**Required Resort Pipeline Update:**
- Add `migrated_id` field mapping to Resort Pipeline configuration
- Map Salesforce `Resort__c.Id` to `destination.migrated_id`
- Re-run Resort Pipeline to populate existing destination with migrated_id values

### Data Migration Sequence

1. **First**: Update and run Resort Pipeline to populate destination.migrated_id
2. **Then**: Run Hotel Pipeline to create hotel with proper destination relationships

## Security and Compliance

- **Data Encryption**: All data encrypted in transit and at rest
- **Audit Trail**: Complete pipeline execution history maintained
- **Access Control**: Database and Salesforce access properly secured
- **Data Retention**: Configurable retention policies for raw/processed data
