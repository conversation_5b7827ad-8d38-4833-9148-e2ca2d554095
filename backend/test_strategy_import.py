#!/usr/bin/env python3
"""
Test importing the SalesforceToPostgreSQLStrategy to find the hanging import.
"""

def test_strategy_imports():
    """Test strategy imports step by step."""
    print("🔍 Testing strategy imports step by step...")
    
    try:
        print("1. Testing base strategy import...")
        from app.etl.pipelines.pipeline_strategies.base_strategy import BasePipelineStrategy
        print("1. ✅ Base strategy imported successfully")
        
        print("2. Testing individual pipeline strategies...")
        from app.etl.pipelines.pipeline_strategies.destination_pipeline import DestinationPipelineStrategy
        print("2a. ✅ DestinationPipelineStrategy imported")
        
        from app.etl.pipelines.pipeline_strategies.hotel_pipeline import HotelPipelineStrategy
        print("2b. ✅ HotelPipelineStrategy imported")
        
        from app.etl.pipelines.pipeline_strategies.room_type_pipeline import RoomTypePipelineStrategy
        print("2c. ✅ RoomTypePipelineStrategy imported")
        
        from app.etl.pipelines.pipeline_strategies.room_pipeline import RoomPipelineStrategy
        print("2d. ✅ RoomPipelineStrategy imported")
        
        print("3. Testing SalesforceExtractor import...")
        from app.etl.extractors.salesforce_extractor import SalesforceExtractor
        print("3. ✅ SalesforceExtractor imported successfully")
        
        print("4. Testing main SalesforceToPostgreSQLStrategy import...")
        from app.etl.pipelines.pipeline_strategies.salesforce_to_postgresql_strategy import SalesforceToPostgreSQLStrategy
        print("4. ✅ SalesforceToPostgreSQLStrategy imported successfully")
        
        print("5. Testing PipelineFactory import...")
        from app.etl.pipelines.pipeline_factory import PipelineFactory
        print("5. ✅ PipelineFactory imported successfully")
        
        print("✅ All strategy imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Error during strategy import: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_strategy_imports()
