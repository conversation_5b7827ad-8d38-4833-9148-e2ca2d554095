#!/usr/bin/env python3
"""
Test individual endpoint imports to find the hanging import.
"""

def test_endpoint_imports():
    """Test each endpoint import individually."""
    print("🔍 Testing endpoint imports individually...")
    
    endpoints = [
        ('migration', 'app.api.api_v1.endpoints.migration'),
        ('staged_data', 'app.api.api_v1.endpoints.staged_data'),
        ('target_schemas', 'app.api.api_v1.endpoints.target_schemas'),
        ('field_mappings', 'app.api.api_v1.endpoints.field_mappings'),
        ('load', 'app.api.api_v1.endpoints.load'),
        ('etl_pipelines', 'app.api.api_v1.endpoints.etl_pipelines'),
        ('websocket', 'app.api.api_v1.endpoints.websocket')
    ]
    
    for name, module in endpoints:
        try:
            print(f"Testing {name}...")
            __import__(module)
            print(f"✅ {name} imported successfully")
        except Exception as e:
            print(f"❌ {name} failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    print("✅ All endpoint imports successful!")
    return True

if __name__ == "__main__":
    test_endpoint_imports()
