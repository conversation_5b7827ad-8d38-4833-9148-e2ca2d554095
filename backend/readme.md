# Database Migration Guide

This guide provides the steps to run database migrations for the project.

## Prerequisites

- Ensure you have access to the project's source code.
- Make sure the `backend/.env` file is properly configured with the correct database credentials.

## Steps to Run Migrations

1. **Navigate to the Backend Directory**:
   Open your terminal and change the directory to the `backend` folder of the project.
   ```sh
   cd backend
   ```

2. **Install Dependencies**:
   If you haven't already, install the required Python packages.
   ```sh
   pip install -r requirements.txt
   ```

3. **Run the Migration Command**:
   Execute the following command to apply all pending migrations to the database.
   ```sh
   alembic upgrade head
   ```

```
   alembic revision --autogenerate -m "Update ProductCategory model"
   ```

This command will update the database schema to the latest version based on the migration scripts located in the `alembic/versions` directory.