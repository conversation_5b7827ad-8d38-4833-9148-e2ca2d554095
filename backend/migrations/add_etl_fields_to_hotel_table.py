#!/usr/bin/env python3
"""
Migration: Add ETL fields to existing hotel table

This migration adds the necessary ETL tracking fields to the existing hotel table
to support the Hotel Pipeline functionality.
"""
import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.database import SessionLocal
from sqlalchemy import text
from datetime import datetime


def run_migration():
    """Add ETL fields to hotel table"""
    print("🔧 Adding ETL fields to hotel table...")
    
    db = SessionLocal()
    
    try:
        # List of columns to add
        columns_to_add = [
            # ETL tracking fields
            ("migrated_id", "VARCHAR(255)", "NULL", "Salesforce Hotel__c.Id"),
            ("pipeline_run_id", "INTEGER", "NULL", "FK to etl_pipeline_runs.id"),
            
            # Source system tracking
            ("source_created_at", "TIMESTAMP", "NULL", "Original creation date in source system"),
            ("source_updated_at", "TIMESTAMP", "NULL", "Last update date in source system"),
            ("extracted_at", "TIMESTAMP", "NULL", "When this record was extracted"),
            ("source_system", "VARCHAR(255)", "NULL", "Source system name (e.g., salesforce)"),
            ("external_id", "VARCHAR(255)", "NULL", "External system ID"),
            
            # Hotel-specific fields from Salesforce
            ("currency", "VARCHAR(255)", "NULL", "Currency code from Salesforce"),
            ("resort_name", "VARCHAR(255)", "NULL", "Associated resort name"),
            ("hotel_name", "VARCHAR(255)", "NULL", "Hotel name (duplicate of name for compatibility)"),
            ("currency_iso_code", "VARCHAR(255)", "NULL", "ISO currency code"),
            ("current_status", "VARCHAR(255)", "NULL", "Current status from Salesforce"),
            ("resort_id", "VARCHAR(255)", "NULL", "Resort ID from Salesforce"),
        ]
        
        # Check which columns already exist
        existing_columns_result = db.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'hotel'
        """))
        existing_columns = {row[0] for row in existing_columns_result}
        
        print(f"   Found {len(existing_columns)} existing columns in hotel table")
        
        # Add missing columns
        added_count = 0
        for column_name, column_type, nullable, description in columns_to_add:
            if column_name not in existing_columns:
                print(f"   Adding column: {column_name} ({column_type})")
                
                sql = f"""
                    ALTER TABLE hotel 
                    ADD COLUMN {column_name} {column_type} {nullable}
                """
                
                db.execute(text(sql))
                added_count += 1
            else:
                print(f"   Column {column_name} already exists, skipping")
        
        # Add indexes for performance
        indexes_to_add = [
            ("idx_hotel_migrated_id", "migrated_id"),
            ("idx_hotel_pipeline_run_id", "pipeline_run_id"),
            ("idx_hotel_external_id", "external_id"),
            ("idx_hotel_source_system", "source_system"),
        ]
        
        print(f"   Adding indexes...")
        for index_name, column_name in indexes_to_add:
            if column_name in existing_columns or column_name in [col[0] for col in columns_to_add]:
                try:
                    sql = f"""
                        CREATE INDEX IF NOT EXISTS {index_name} 
                        ON hotel ({column_name})
                    """
                    db.execute(text(sql))
                    print(f"   Created index: {index_name}")
                except Exception as e:
                    print(f"   Index {index_name} may already exist: {e}")
        
        # Add foreign key constraint for pipeline_run_id
        try:
            print(f"   Adding foreign key constraint for pipeline_run_id...")
            sql = """
                ALTER TABLE hotel 
                ADD CONSTRAINT fk_hotel_pipeline_run 
                FOREIGN KEY (pipeline_run_id) 
                REFERENCES etl_pipeline_runs(id)
            """
            db.execute(text(sql))
            print(f"   Added foreign key constraint: fk_hotel_pipeline_run")
        except Exception as e:
            print(f"   Foreign key constraint may already exist: {e}")
        
        # Commit all changes
        db.commit()
        
        print(f"✅ Migration completed successfully!")
        print(f"   Added {added_count} new columns to hotel table")
        print(f"   Added indexes for ETL performance")
        print(f"   Added foreign key constraints")
        
        # Verify the changes
        print(f"\n📊 Verifying migration...")
        result = db.execute(text("""
            SELECT column_name, data_type, is_nullable 
            FROM information_schema.columns 
            WHERE table_name = 'hotel' 
            AND column_name IN ('migrated_id', 'pipeline_run_id', 'source_system', 'external_id')
            ORDER BY column_name
        """))
        
        print(f"   ETL columns in hotel table:")
        for row in result:
            print(f"     {row[0]} ({row[1]}) - nullable: {row[2]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        db.rollback()
        return False
    
    finally:
        db.close()


def rollback_migration():
    """Remove ETL fields from hotel table (rollback)"""
    print("🔄 Rolling back ETL fields from hotel table...")
    
    db = SessionLocal()
    
    try:
        # List of columns to remove (in reverse order)
        columns_to_remove = [
            "resort_id",
            "current_status", 
            "currency_iso_code",
            "hotel_name",
            "resort_name",
            "currency",
            "external_id",
            "source_system",
            "extracted_at",
            "source_updated_at",
            "source_created_at",
            "pipeline_run_id",
            "migrated_id",
        ]
        
        # Remove foreign key constraint first
        try:
            db.execute(text("ALTER TABLE hotel DROP CONSTRAINT IF EXISTS fk_hotel_pipeline_run"))
            print("   Removed foreign key constraint: fk_hotel_pipeline_run")
        except Exception as e:
            print(f"   Error removing foreign key: {e}")
        
        # Remove indexes
        indexes_to_remove = [
            "idx_hotel_migrated_id",
            "idx_hotel_pipeline_run_id", 
            "idx_hotel_external_id",
            "idx_hotel_source_system",
        ]
        
        for index_name in indexes_to_remove:
            try:
                db.execute(text(f"DROP INDEX IF EXISTS {index_name}"))
                print(f"   Removed index: {index_name}")
            except Exception as e:
                print(f"   Error removing index {index_name}: {e}")
        
        # Remove columns
        removed_count = 0
        for column_name in columns_to_remove:
            try:
                sql = f"ALTER TABLE hotel DROP COLUMN IF EXISTS {column_name}"
                db.execute(text(sql))
                print(f"   Removed column: {column_name}")
                removed_count += 1
            except Exception as e:
                print(f"   Error removing column {column_name}: {e}")
        
        db.commit()
        print(f"✅ Rollback completed! Removed {removed_count} columns")
        return True
        
    except Exception as e:
        print(f"❌ Rollback failed: {e}")
        db.rollback()
        return False
    
    finally:
        db.close()


def main():
    """Run migration or rollback based on command line argument"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        success = rollback_migration()
    else:
        success = run_migration()
    
    if success:
        print("\n🎉 Operation completed successfully!")
    else:
        print("\n❌ Operation failed!")
    
    return success


if __name__ == "__main__":
    main()
