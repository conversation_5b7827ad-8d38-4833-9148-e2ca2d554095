"""
Pydantic schemas for migration jobs.
"""
from typing import Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field
from uuid import UUID

from app.models.migration_job import JobStatus


class StartMigrationRequest(BaseModel):
    """Request schema for starting a migration."""
    object_name: str = Field(..., description="Salesforce object name (e.g., 'Account', 'Product2')")
    job_name: Optional[str] = Field(None, description="Optional human-readable job name")
    source_config: Optional[Dict[str, Any]] = Field(None, description="Salesforce extraction configuration")
    target_config: Optional[Dict[str, Any]] = Field(None, description="Target system configuration")
    transformation_config: Optional[Dict[str, Any]] = Field(None, description="Transformation rules")

    class Config:
        json_schema_extra = {
            "example": {
                "object_name": "Product2",
                "job_name": "Product Migration - January 2024",
                "source_config": {
                    "fields": ["Id", "Name", "Description", "ProductCode", "IsActive"],
                    "where_clause": "IsActive = true",
                    "last_modified_days": 30
                },
                "target_config": {
                    "target_type": "medusa",
                    "object_mapping": "product"
                }
            }
        }


class StartMigrationResponse(BaseModel):
    """Response schema for starting a migration."""
    job_id: UUID = Field(..., description="Unique job identifier")
    message: str = Field(..., description="Success message")
    status: JobStatus = Field(..., description="Initial job status")

    class Config:
        json_schema_extra = {
            "example": {
                "job_id": "123e4567-e89b-12d3-a456-************",
                "message": "Migration job started successfully",
                "status": "pending"
            }
        }


class MigrationJobCreate(BaseModel):
    """Schema for creating a migration job."""
    object_name: str
    job_name: Optional[str] = None
    source_config: Optional[Dict[str, Any]] = None
    target_config: Optional[Dict[str, Any]] = None
    transformation_config: Optional[Dict[str, Any]] = None


class MigrationJobResponse(BaseModel):
    """Response schema for migration job details."""
    id: UUID
    object_name: str
    job_name: Optional[str]
    status: JobStatus
    progress_percentage: int
    current_step: Optional[str]

    # Timestamps
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    updated_at: datetime

    # Statistics
    records_extracted: int
    records_transformed: int
    records_staged: int
    records_synced: int
    records_failed: int

    # Error information
    error_message: Optional[str]
    retry_count: int
    max_retries: int

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "object_name": "Product2",
                "job_name": "Product Migration - January 2024",
                "status": "extracting",
                "progress_percentage": 25,
                "current_step": "Extracting records from Salesforce",
                "created_at": "2024-01-15T10:00:00Z",
                "started_at": "2024-01-15T10:00:05Z",
                "completed_at": None,
                "updated_at": "2024-01-15T10:05:00Z",
                "records_extracted": 150,
                "records_transformed": 0,
                "records_staged": 0,
                "records_synced": 0,
                "records_failed": 0,
                "error_message": None,
                "retry_count": 0,
                "max_retries": 3
            }
        }


class MigrationJobStatus(BaseModel):
    """Simplified schema for job status checks."""
    id: UUID
    status: JobStatus
    progress_percentage: int
    current_step: Optional[str]
    error_message: Optional[str]

    # Quick stats
    records_extracted: int
    records_transformed: int
    records_staged: int
    records_synced: int
    records_failed: int

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "status": "syncing",
                "progress_percentage": 75,
                "current_step": "Syncing records to Medusa",
                "error_message": None,
                "records_extracted": 200,
                "records_transformed": 200,
                "records_staged": 200,
                "records_synced": 150,
                "records_failed": 0
            }
        }
