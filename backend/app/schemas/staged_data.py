"""
Pydantic schemas for staged data.
"""
from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field
from uuid import UUID


class StagedDataResponse(BaseModel):
    """Response schema for staged data records."""
    id: UUID
    migration_job_id: UUID
    object_name: str
    source_id: str
    target_id: Optional[str]

    # Data
    transformed_data: Dict[str, Any]
    user_modified: bool
    user_modifications: Optional[Dict[str, Any]]
    modified_by: Optional[str]
    modified_at: Optional[datetime]

    # Status
    sync_status: str
    sync_error: Optional[str]
    validation_status: str
    validation_errors: Optional[List[str]]

    # Timestamps
    created_at: datetime
    synced_at: Optional[datetime]

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "456e7890-e89b-12d3-a456-************",
                "migration_job_id": "123e4567-e89b-12d3-a456-************",
                "object_name": "product",
                "source_id": "01t5g000000ABC123",
                "target_id": "prod_01HQZX9Y8Z1A2B3C4D5E6F",
                "transformed_data": {
                    "title": "Premium Widget",
                    "description": "High-quality widget for professional use",
                    "handle": "premium-widget",
                    "status": "published"
                },
                "user_modified": True,
                "user_modifications": {
                    "title": "Premium Widget Pro"
                },
                "modified_by": "<EMAIL>",
                "modified_at": "2024-01-15T10:15:00Z",
                "sync_status": "pending",
                "sync_error": None,
                "validation_status": "valid",
                "validation_errors": None,
                "created_at": "2024-01-15T10:10:00Z",
                "synced_at": None
            }
        }


class StagedDataUpdate(BaseModel):
    """Schema for updating staged data records."""
    field_updates: Dict[str, Any] = Field(..., description="Fields to update with new values")
    user_id: Optional[str] = Field(None, description="ID of user making the changes")

    class Config:
        json_schema_extra = {
            "example": {
                "field_updates": {
                    "title": "Updated Product Title",
                    "description": "Updated product description",
                    "status": "draft"
                },
                "user_id": "<EMAIL>"
            }
        }


class StagedDataListResponse(BaseModel):
    """Response schema for paginated staged data lists."""
    items: List[StagedDataResponse]
    total: int
    page: int
    limit: int
    pages: int
    has_next: bool
    has_prev: bool

    class Config:
        json_schema_extra = {
            "example": {
                "items": [
                    {
                        "id": "456e7890-e89b-12d3-a456-************",
                        "migration_job_id": "123e4567-e89b-12d3-a456-************",
                        "object_name": "product",
                        "source_id": "01t5g000000ABC123",
                        "target_id": None,
                        "transformed_data": {
                            "title": "Premium Widget",
                            "description": "High-quality widget",
                            "handle": "premium-widget"
                        },
                        "user_modified": False,
                        "user_modifications": None,
                        "modified_by": None,
                        "modified_at": None,
                        "sync_status": "pending",
                        "sync_error": None,
                        "validation_status": "valid",
                        "validation_errors": None,
                        "created_at": "2024-01-15T10:10:00Z",
                        "synced_at": None
                    }
                ],
                "total": 150,
                "page": 1,
                "limit": 50,
                "pages": 3,
                "has_next": True,
                "has_prev": False
            }
        }


class StagedDataSummary(BaseModel):
    """Summary statistics for staged data."""
    total_records: int
    pending_sync: int
    synced: int
    failed: int
    user_modified: int
    validation_errors: int

    class Config:
        json_schema_extra = {
            "example": {
                "total_records": 150,
                "pending_sync": 120,
                "synced": 25,
                "failed": 5,
                "user_modified": 15,
                "validation_errors": 3
            }
        }
