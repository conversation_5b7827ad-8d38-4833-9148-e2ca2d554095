"""
ETL Pipeline Pydantic schemas for API validation
"""
from datetime import datetime
from typing import Dict, List, Optional, Any
import json
from pydantic import BaseModel, Field, field_validator


class ETLPipelineBase(BaseModel):
    """Base ETL Pipeline schema"""
    name: str = Field(..., description="Pipeline name")
    description: Optional[str] = Field(None, description="Pipeline description")
    source_type: str = Field(..., description="Source type (e.g., 'salesforce')")
    source_object: Optional[str] = Field(None, description="Source object name")
    source_fields: Optional[List[str]] = Field(None, description="List of source fields")
    destination_type: str = Field(..., description="Destination type (e.g., 'postgresql')")
    destination_table: Optional[str] = Field(None, description="Destination table name")
    destination_fields: Optional[Dict[str, Any]] = Field(None, description="Field mappings")
    transformation_config: Optional[Dict[str, Any]] = Field(None, description="Transformation configuration")
    schedule_config: Optional[Dict[str, Any]] = Field(None, description="Schedule configuration")
    is_active: bool = Field(True, description="Whether pipeline is active")


class ETLPipelineCreate(ETLPipelineBase):
    """Schema for creating ETL Pipeline"""
    pass


class ETLPipelineUpdate(BaseModel):
    """Schema for updating ETL Pipeline"""
    name: Optional[str] = None
    description: Optional[str] = None
    source_type: Optional[str] = None
    source_object: Optional[str] = None
    source_fields: Optional[List[str]] = None
    destination_type: Optional[str] = None
    destination_table: Optional[str] = None
    destination_fields: Optional[Dict[str, Any]] = None
    transformation_config: Optional[Dict[str, Any]] = None
    schedule_config: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None


class ETLPipelineResponse(ETLPipelineBase):
    """Schema for ETL Pipeline response"""
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ETLPipelineRunBase(BaseModel):
    """Base ETL Pipeline Run schema"""
    pipeline_id: int
    status: str = Field(..., description="Run status")
    records_extracted: Optional[int] = None
    records_transformed: Optional[int] = None
    records_loaded: Optional[int] = None
    error_message: Optional[str] = None
    logs: Optional[List[Dict[str, Any]]] = None
    raw_data_path: Optional[str] = None
    processed_data_path: Optional[str] = None


class ETLPipelineRunCreate(ETLPipelineRunBase):
    """Schema for creating ETL Pipeline Run"""
    pass


class ETLPipelineRunUpdate(BaseModel):
    """Schema for updating ETL Pipeline Run"""
    status: Optional[str] = None
    records_extracted: Optional[int] = None
    records_transformed: Optional[int] = None
    records_loaded: Optional[int] = None
    error_message: Optional[str] = None
    logs: Optional[List[Dict[str, Any]]] = None
    raw_data_path: Optional[str] = None
    processed_data_path: Optional[str] = None
    completed_at: Optional[datetime] = None


class ETLPipelineRunResponse(ETLPipelineRunBase):
    """Schema for ETL Pipeline Run response"""
    id: int
    started_at: datetime
    completed_at: Optional[datetime] = None
    duration_seconds: Optional[int] = None

    @field_validator('logs', mode='before')
    @classmethod
    def parse_logs(cls, v):
        """Parse logs field if it's a JSON string"""
        if isinstance(v, str):
            try:
                return json.loads(v)
            except (json.JSONDecodeError, TypeError):
                return []
        return v

    class Config:
        from_attributes = True


class ETLPipelineWithRuns(ETLPipelineResponse):
    """Schema for ETL Pipeline with recent runs"""
    recent_runs: List[ETLPipelineRunResponse] = Field(default_factory=list)
    last_run: Optional[ETLPipelineRunResponse] = None
    total_runs: int = 0


class PipelineExecutionRequest(BaseModel):
    """Schema for manual pipeline execution request"""
    pipeline_id: int
    force_run: bool = Field(False, description="Force run even if pipeline is not active")


class PipelineExecutionResponse(BaseModel):
    """Schema for pipeline execution response"""
    run_id: int
    pipeline_id: int
    status: str
    message: str


class DestinationBase(BaseModel):
    """Base Destination schema"""
    migrated_id: Optional[str] = None
    country: Optional[str] = None
    name: Optional[str] = None
    is_active: Optional[bool] = None
    currency: Optional[str] = None
    description: Optional[str] = None


class DestinationCreate(DestinationBase):
    """Schema for creating Destination"""
    pass


class DestinationResponse(DestinationBase):
    """Schema for Destination response"""
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ProductCategoryBase(BaseModel):
    """Base ProductCategory schema"""
    migrated_id: Optional[str] = None
    name: Optional[str] = None


class ProductCategoryCreate(ProductCategoryBase):
    """Schema for creating ProductCategory"""
    pass


class ProductCategoryResponse(ProductCategoryBase):
    """Schema for ProductCategory response"""
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class HotelBase(BaseModel):
    """Base Hotel schema"""
    name: Optional[str] = None
    destination_id: Optional[str] = None
    category_id: Optional[str] = None


class HotelCreate(HotelBase):
    """Schema for creating Hotel"""
    pass


class HotelResponse(HotelBase):
    """Schema for Hotel response"""
    id: str
    created_at: datetime
    updated_at: datetime
    destination: Optional[DestinationResponse] = None
    category: Optional[ProductCategoryResponse] = None

    class Config:
        from_attributes = True


class PipelineStatsResponse(BaseModel):
    """Schema for pipeline statistics"""
    total_pipelines: int
    active_pipelines: int
    total_runs: int
    successful_runs: int
    failed_runs: int
    running_pipelines: int


class PipelineLogEntry(BaseModel):
    """Schema for pipeline log entry"""
    timestamp: datetime
    level: str  # 'INFO', 'WARNING', 'ERROR'
    stage: str  # 'extract', 'transform', 'load'
    message: str
    details: Optional[Dict[str, Any]] = None
