"""
Pydantic schemas for field mapping operations.
"""
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from uuid import UUID


class TransformationRule(BaseModel):
    """Schema for field transformation rules."""
    function: str = Field(..., description="Transformation function name")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Function parameters")
    
    class Config:
        json_schema_extra = {
            "example": {
                "function": "uppercase",
                "parameters": {}
            }
        }


class FieldMappingRule(BaseModel):
    """Schema for individual field mapping rule."""
    source_field: str = Field(..., description="Source field name from Salesforce")
    target_field: str = Field(..., description="Target field name in schema")
    transformation: Optional[TransformationRule] = Field(None, description="Optional transformation rule")
    default_value: Optional[Union[str, int, float, bool]] = Field(None, description="Default value if source is null")
    required: bool = Field(default=False, description="Whether this mapping is required")
    
    class Config:
        json_schema_extra = {
            "example": {
                "source_field": "Name",
                "target_field": "title",
                "transformation": {
                    "function": "strip",
                    "parameters": {}
                },
                "default_value": None,
                "required": True
            }
        }


class FieldMappingBase(BaseModel):
    """Base schema for field mappings."""
    mapping_name: str = Field(..., description="Human-readable name for this mapping")
    description: Optional[str] = Field(None, description="Description of this mapping")
    source_object: str = Field(..., description="Salesforce object name")
    field_mappings: List[FieldMappingRule] = Field(..., description="List of field mapping rules")
    remove_unmapped_fields: bool = Field(default=True, description="Remove fields without mappings")


class FieldMappingCreate(FieldMappingBase):
    """Schema for creating field mappings."""
    target_schema_id: str = Field(..., description="Target schema ID")
    created_by: Optional[str] = Field(None, description="User who created this mapping")


class FieldMappingUpdate(BaseModel):
    """Schema for updating field mappings."""
    mapping_name: Optional[str] = None
    description: Optional[str] = None
    field_mappings: Optional[List[FieldMappingRule]] = None
    remove_unmapped_fields: Optional[bool] = None


class FieldMappingResponse(FieldMappingBase):
    """Schema for field mapping responses."""
    id: UUID
    target_schema_id: str
    created_by: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "mapping_name": "Account to Customer Mapping",
                "description": "Maps Salesforce Account fields to Customer schema",
                "source_object": "Account",
                "target_schema_id": "customer_schema",
                "field_mappings": [
                    {
                        "source_field": "Name",
                        "target_field": "company_name",
                        "transformation": {
                            "function": "strip",
                            "parameters": {}
                        },
                        "required": True
                    }
                ],
                "remove_unmapped_fields": True,
                "created_by": "<EMAIL>",
                "created_at": "2024-01-15T10:00:00Z",
                "updated_at": "2024-01-15T10:00:00Z"
            }
        }


class FieldMappingListResponse(BaseModel):
    """Schema for paginated field mapping list responses."""
    mappings: List[FieldMappingResponse]
    total: int
    page: int
    limit: int
    
    class Config:
        json_schema_extra = {
            "example": {
                "mappings": [],
                "total": 0,
                "page": 1,
                "limit": 50
            }
        }


class ApplyMappingRequest(BaseModel):
    """Schema for applying field mappings to data."""
    mapping_id: UUID = Field(..., description="Field mapping ID to apply")
    data: List[Dict[str, Any]] = Field(..., description="Source data to transform")
    
    class Config:
        json_schema_extra = {
            "example": {
                "mapping_id": "123e4567-e89b-12d3-a456-************",
                "data": [
                    {
                        "Id": "001XX000004TmiQQAS",
                        "Name": "Acme Corporation",
                        "Phone": "(*************"
                    }
                ]
            }
        }


class ApplyMappingResponse(BaseModel):
    """Schema for field mapping application results."""
    transformed_data: List[Dict[str, Any]]
    mapping_summary: Dict[str, Any]
    errors: List[str]
    
    class Config:
        json_schema_extra = {
            "example": {
                "transformed_data": [
                    {
                        "company_name": "Acme Corporation",
                        "phone": "(*************"
                    }
                ],
                "mapping_summary": {
                    "total_records": 1,
                    "successfully_mapped": 1,
                    "fields_mapped": 2,
                    "fields_removed": 1
                },
                "errors": []
            }
        }
