from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum


class TargetSystemEnum(str, Enum):
    """Target system enumeration for API."""
    OPS = "ops"  # Ops (Medusa)
    CRM = "crm"  # CRM system

class TargetFieldValidation(BaseModel):
    minLength: Optional[int] = None
    maxLength: Optional[int] = None
    pattern: Optional[str] = None

class TargetField(BaseModel):
    name: str = Field(..., description="Field name (API name)")
    label: str = Field(..., description="Human-readable field label")
    type: str = Field(..., description="Field type (string, number, boolean, date, email, url, text, json)")
    required: bool = Field(default=False, description="Whether the field is required")
    description: Optional[str] = Field(None, description="Field description")
    defaultValue: Optional[str] = Field(None, description="Default value for the field")
    validation: Optional[TargetFieldValidation] = Field(None, description="Field validation rules")

class TargetSchemaBase(BaseModel):
    name: str = Field(..., description="Schema name (used as identifier)")
    label: str = Field(..., description="Human-readable schema label")
    description: str = Field(..., description="Schema description")
    target_system: TargetSystemEnum = Field(..., description="Target system (ops or crm)")
    fields: List[TargetField] = Field(default=[], description="List of fields in the schema")

class TargetSchemaCreate(TargetSchemaBase):
    id: str = Field(..., description="Unique schema identifier")

class TargetSchemaUpdate(BaseModel):
    name: Optional[str] = None
    label: Optional[str] = None
    description: Optional[str] = None
    target_system: Optional[TargetSystemEnum] = None
    fields: Optional[List[TargetField]] = None

class TargetSchema(TargetSchemaBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

    @classmethod
    def model_validate(cls, obj):
        """Custom validation to handle JSON fields parsing"""
        import json

        if hasattr(obj, 'fields') and isinstance(obj.fields, str):
            # Parse the fields JSON string
            try:
                fields_data = json.loads(obj.fields)
                obj.fields = [TargetField(**field) for field in fields_data]
            except (json.JSONDecodeError, TypeError):
                obj.fields = []

        return super().model_validate(obj)
