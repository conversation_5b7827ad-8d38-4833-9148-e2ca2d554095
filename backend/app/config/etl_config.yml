# ETL Configuration for Flinkk Transfer Hub
# This file contains ETL-specific configurations including pipeline settings,
# transformation rules, and data source configurations.

# ETL Pipeline Configuration
pipelines:
  # Default pipeline settings
  default:
    batch_size: 1000
    max_retries: 3
    retry_delay_seconds: 30
    timeout_seconds: 3600
    enable_monitoring: true
    enable_data_validation: true
    
  # Salesforce to PostgreSQL pipelines
  salesforce_to_postgresql:
    extraction:
      default_limit: null  # No limit by default
      batch_size: 1000
      timeout_seconds: 1800
      
    transformation:
      enable_field_validation: true
      enable_data_cleaning: true
      null_value_handling: "skip"  # Options: skip, default, error
      
    loading:
      batch_size: 500
      upsert_strategy: "merge"  # Options: insert, update, merge
      enable_audit_logging: true

# Data Source Configurations
data_sources:
  salesforce:
    api_version: "58.0"
    timeout_seconds: 300
    max_records_per_query: 2000
    
    # Object-specific configurations
    objects:
      Resort__c:
        fields: ["Id", "Name", "Description__c", "Location__c", "Created_Date__c"]
        relationships: []
        
      Hotel__c:
        fields: ["Id", "Name", "Description__c", "Resort__c", "Address__c", "Phone__c", "Email__c"]
        relationships: ["Resort__c"]
        
      Room_Type__c:
        fields: ["Id", "Name", "Description__c", "Hotel__c", "Max_Occupancy__c", "Base_Price__c"]
        relationships: ["Hotel__c"]
        
      Room__c:
        fields: ["Id", "Name", "Room_Number__c", "Room_Type__c", "Floor__c", "Status__c"]
        relationships: ["Room_Type__c"]

# Target System Configurations
target_systems:
  postgresql:
    connection_pool_size: 10
    max_overflow: 20
    pool_timeout: 30
    
    # Table-specific configurations
    tables:
      destination:
        primary_key: "id"
        audit_fields: ["created_at", "updated_at"]
        
      hotel:
        primary_key: "id"
        foreign_keys: ["destination_id"]
        audit_fields: ["created_at", "updated_at"]
        
      product_category:
        primary_key: "id"
        audit_fields: ["created_at", "updated_at"]
        
      product:
        primary_key: "id"
        foreign_keys: ["category_id"]
        audit_fields: ["created_at", "updated_at"]
        
      product_variant:
        primary_key: "id"
        foreign_keys: ["product_id"]
        audit_fields: ["created_at", "updated_at"]

# Transformation Rules
transformations:
  # Field mapping rules
  field_mappings:
    Resort__c_to_destination:
      Id: "salesforce_id"
      Name: "name"
      Description__c: "description"
      Location__c: "location"
      
    Hotel__c_to_hotel:
      Id: "salesforce_id"
      Name: "name"
      Description__c: "description"
      Resort__c: "destination_id"
      Address__c: "address"
      Phone__c: "phone"
      Email__c: "email"
      
    Room_Type__c_to_product:
      Id: "salesforce_id"
      Name: "title"
      Description__c: "description"
      Hotel__c: "hotel_id"
      Max_Occupancy__c: "max_occupancy"
      Base_Price__c: "base_price"
      
    Room__c_to_product_variant:
      Id: "salesforce_id"
      Name: "title"
      Room_Number__c: "room_number"
      Room_Type__c: "product_id"
      Floor__c: "floor"
      Status__c: "status"

  # Data validation rules
  validation_rules:
    required_fields:
      - "salesforce_id"
      - "name"
    
    field_formats:
      email: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
      phone: "^\\+?[1-9]\\d{1,14}$"
    
    value_constraints:
      max_occupancy:
        min: 1
        max: 20
      base_price:
        min: 0

# Monitoring and Logging
monitoring:
  enable_metrics: true
  enable_alerts: true
  log_level: "INFO"
  
  # Metrics collection
  metrics:
    - "pipeline_execution_time"
    - "records_processed"
    - "error_count"
    - "success_rate"
  
  # Alert thresholds
  alerts:
    error_rate_threshold: 0.05  # 5%
    execution_time_threshold_minutes: 60
    
# Storage Configuration
storage:
  # Raw data storage settings
  raw_data:
    retention_days: 30
    compression: "gzip"
    
  # Processed data storage settings
  processed_data:
    retention_days: 90
    compression: "gzip"
    
  # Audit log storage settings
  audit_logs:
    retention_days: 365
    compression: "gzip"
