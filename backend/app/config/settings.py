"""
Core configuration settings for the ETL Migration System.
"""
import os
import yaml
from typing import Optional, Dict, Any
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""

    # Application
    app_name: str = "Flinkk Transfer Hub"
    version: str = "1.0.0"
    environment: str = Field(alias="ENVIRONMENT")
    secret_key: str = Field(alias="SECRET_KEY")

    # Database - Azure PostgreSQL
    database_url: str = Field(alias="DATABASE_URL")

    # Salesforce Configuration
    salesforce_client_id: str = Field(default="", alias="SALESFORCE_CLIENT_ID")
    salesforce_client_secret: str = Field(default="", alias="SALESFORCE_CLIENT_SECRET")
    salesforce_username: str = Field(default="", alias="SALESFORCE_USERNAME")
    salesforce_password: str = Field(default="", alias="SALESFORCE_PASSWORD")
    salesforce_security_token: str = Field(default="", alias="SALESFORCE_SECURITY_TOKEN")

    # OPS Configuration
    ops_url: str = Field(default="", alias="OPS_URL")
    ops_api_key: str = Field(default="", alias="OPS_API_KEY")

    # Object Storage Configuration (MinIO for local, Azure Blob for production)
    storage_type: str = Field(alias="STORAGE_TYPE")  # "minio" or "azure_blob"

    # MinIO Configuration (Local Development)
    minio_endpoint: str = Field(default="", alias="MINIO_ENDPOINT")
    minio_access_key: str = Field(default="", alias="MINIO_ACCESS_KEY")
    minio_secret_key: str = Field(default="", alias="MINIO_SECRET_KEY")
    minio_bucket_name: str = Field(default="", alias="MINIO_BUCKET_NAME")
    minio_secure: bool = Field(default=False, alias="MINIO_SECURE")

    # Azure Blob Storage Configuration (Production)
    azure_storage_account_name: str = Field(default="", alias="AZURE_STORAGE_ACCOUNT_NAME")
    azure_storage_account_key: str = Field(default="", alias="AZURE_STORAGE_ACCOUNT_KEY")
    azure_storage_container_name: str = Field(default="", alias="AZURE_STORAGE_CONTAINER_NAME")

    # Azure Resource Configuration
    azure_credentials: str = Field(default="", alias="AZURE_CREDENTIALS")
    azure_subscription_id: str = Field(default="", alias="AZURE_SUBSCRIPTION_ID")
    azure_resource_group: str = Field(default="", alias="AZURE_RESOURCE_GROUP")

    # API Configuration
    api_v1_prefix: str = "/api/v1"
    cors_origins: str = Field(alias="CORS_ORIGINS")

    @property
    def cors_origins_list(self) -> list[str]:
        """Convert comma-separated CORS origins to list"""
        return [origin.strip() for origin in self.cors_origins.split(",")]

    # Background Tasks Configuration
    # Phase 1: Using FastAPI BackgroundTasks
    # Phase 2: Will use Celery + Redis
    max_background_tasks: int = 10
    task_timeout_seconds: int = 3600  # 1 hour

    # Pagination
    default_page_size: int = 50
    max_page_size: int = 1000

    # Logging
    log_level: str = "WARNING"

    class Config:
        env_file = ".env"
        case_sensitive = False


class ETLConfig:
    """ETL-specific configuration loaded from YAML file."""

    def __init__(self, config_path: str = None):
        if config_path is None:
            config_path = os.path.join(os.path.dirname(__file__), "etl_config.yml")

        self.config_path = config_path
        self._config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """Load ETL configuration from YAML file."""
        try:
            with open(self.config_path, 'r') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            print(f"Warning: ETL config file not found at {self.config_path}")
            return {}
        except yaml.YAMLError as e:
            print(f"Error parsing ETL config file: {e}")
            return {}

    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by dot notation key."""
        keys = key.split('.')
        value = self._config

        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default

        return value

    def get_pipeline_config(self, pipeline_type: str = "default") -> Dict[str, Any]:
        """Get pipeline-specific configuration."""
        return self.get(f"pipelines.{pipeline_type}", {})

    def get_data_source_config(self, source: str) -> Dict[str, Any]:
        """Get data source configuration."""
        return self.get(f"data_sources.{source}", {})

    def get_target_system_config(self, target: str) -> Dict[str, Any]:
        """Get target system configuration."""
        return self.get(f"target_systems.{target}", {})

    def get_transformation_rules(self, mapping_name: str = None) -> Dict[str, Any]:
        """Get transformation rules."""
        if mapping_name:
            return self.get(f"transformations.field_mappings.{mapping_name}", {})
        return self.get("transformations", {})


# Global settings instances
settings = Settings()
etl_config = ETLConfig()
