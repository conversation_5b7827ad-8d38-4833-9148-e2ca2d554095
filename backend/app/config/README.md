# Configuration Management

This module handles all configuration management for the Flinkk Transfer Hub application, including both general application settings and ETL-specific configurations.

## Files Overview

### `settings.py`
Main application configuration using Pydantic Settings:
- Database connections
- API configurations
- Authentication settings
- Storage configurations (MinIO, Azure Blob)
- CORS and security settings

### `etl_config.yml`
ETL-specific configuration in YAML format:
- Pipeline default settings
- Data source configurations
- Target system settings
- Transformation rules
- Monitoring and logging settings

## Configuration Structure

### Application Settings (`settings.py`)

```python
from app.config.settings import settings, etl_config

# Access application settings
database_url = settings.database_url
salesforce_client_id = settings.salesforce_client_id

# Access ETL configuration
pipeline_config = etl_config.get_pipeline_config("salesforce_to_postgresql")
salesforce_config = etl_config.get_data_source_config("salesforce")
```

### ETL Configuration (`etl_config.yml`)

The ETL configuration is organized into sections:

#### Pipeline Settings
```yaml
pipelines:
  default:
    batch_size: 1000
    max_retries: 3
    timeout_seconds: 3600
```

#### Data Sources
```yaml
data_sources:
  salesforce:
    api_version: "58.0"
    timeout_seconds: 300
    objects:
      Account:
        fields: ["Id", "Name", "Type"]
```

#### Target Systems
```yaml
target_systems:
  postgresql:
    connection_pool_size: 10
    tables:
      destination:
        primary_key: "id"
        audit_fields: ["created_at", "updated_at"]
```

#### Transformation Rules
```yaml
transformations:
  field_mappings:
    Account_to_destination:
      Id: "salesforce_id"
      Name: "name"
      Type: "type"
```

## Usage Examples

### Accessing Pipeline Configuration
```python
from app.config.settings import etl_config

# Get default pipeline settings
default_config = etl_config.get_pipeline_config()

# Get specific pipeline type settings
sf_config = etl_config.get_pipeline_config("salesforce_to_postgresql")

# Access specific settings
batch_size = sf_config.get("extraction", {}).get("batch_size", 1000)
```

### Accessing Data Source Configuration
```python
# Get Salesforce configuration
sf_config = etl_config.get_data_source_config("salesforce")
api_version = sf_config.get("api_version", "58.0")

# Get object-specific configuration
account_config = sf_config.get("objects", {}).get("Account", {})
fields = account_config.get("fields", [])
```

### Accessing Transformation Rules
```python
# Get all transformation rules
transformations = etl_config.get_transformation_rules()

# Get specific field mapping
account_mapping = etl_config.get_transformation_rules("Account_to_destination")
```

## Environment Variables

The application uses environment variables for sensitive configuration:

### Required Variables
```bash
# Database
DATABASE_URL=postgresql://user:pass@host:port/db

# Salesforce
SALESFORCE_CLIENT_ID=your_client_id
SALESFORCE_CLIENT_SECRET=your_client_secret
SALESFORCE_USERNAME=your_username
SALESFORCE_PASSWORD=your_password
SALESFORCE_SECURITY_TOKEN=your_token

# Storage
STORAGE_TYPE=minio  # or azure_blob
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET_NAME=etl-data

# Application
SECRET_KEY=your_secret_key
ENVIRONMENT=development
CORS_ORIGINS=http://localhost:3000
```

### Optional Variables
```bash
# Azure (for production)
AZURE_STORAGE_ACCOUNT_NAME=your_account
AZURE_STORAGE_ACCOUNT_KEY=your_key
AZURE_STORAGE_CONTAINER_NAME=etl-data

# OPS Integration
OPS_URL=https://ops.example.com
OPS_API_KEY=your_ops_key
```

## Configuration Best Practices

1. **Environment Separation**: Use different `.env` files for different environments
2. **Sensitive Data**: Never commit sensitive data to version control
3. **Validation**: Use Pydantic for configuration validation
4. **Documentation**: Document all configuration options
5. **Defaults**: Provide sensible defaults for optional settings

## Adding New Configuration

### Application Settings
1. Add new field to `Settings` class in `settings.py`
2. Use appropriate Pydantic field type and validation
3. Add environment variable alias
4. Document the new setting

### ETL Configuration
1. Add new section to `etl_config.yml`
2. Update `ETLConfig` class if needed
3. Add helper methods for accessing the configuration
4. Document the new configuration section

## Configuration Validation

The application validates configuration on startup:
- Pydantic validates application settings
- YAML parser validates ETL configuration structure
- Custom validation can be added for business rules

## Troubleshooting

### Common Issues
1. **Missing Environment Variables**: Check `.env` file exists and contains required variables
2. **YAML Syntax Errors**: Validate YAML syntax in `etl_config.yml`
3. **Type Validation Errors**: Ensure environment variable types match Pydantic field types
4. **File Not Found**: Ensure configuration files are in the correct location

### Debug Configuration
```python
from app.config.settings import settings, etl_config

# Print current settings (be careful with sensitive data)
print(f"Environment: {settings.environment}")
print(f"Database URL: {settings.database_url[:20]}...")

# Check ETL configuration
print(f"ETL Config loaded: {etl_config._config is not None}")
```
