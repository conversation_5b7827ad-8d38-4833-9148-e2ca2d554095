"""
MinIO service for object storage in ETL pipelines
DEPRECATED: Use storage_service.py for unified storage interface
"""
import logging
from typing import Any, Dict, List

from app.services.storage_service import storage_service

logger = logging.getLogger(__name__)


class MinIOService:
    """
    Legacy MinIO service - now delegates to unified storage service
    This class is maintained for backward compatibility
    """

    def __init__(self):
        logger.warning("MinIOService is deprecated. Use storage_service directly.")
        self.storage = storage_service

    def store_raw_data(self, data: List[Dict[str, Any]], pipeline_name: str, run_id: int) -> str:
        """Store raw extracted data using unified storage service"""
        return self.storage.store_raw_data(data, pipeline_name, run_id)

    def store_processed_data(self, data: List[Dict[str, Any]], pipeline_name: str, run_id: int) -> str:
        """Store processed/transformed data using unified storage service"""
        return self.storage.store_processed_data(data, pipeline_name, run_id)

    def retrieve_data(self, object_path: str) -> List[Dict[str, Any]]:
        """Retrieve data using unified storage service"""
        return self.storage.retrieve_data(object_path)


# Global MinIO service instance (for backward compatibility)
minio_service = MinIOService()

