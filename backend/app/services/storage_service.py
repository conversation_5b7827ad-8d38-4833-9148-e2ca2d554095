"""
Unified storage service supporting both MinIO (local) and Azure Blob Storage (production)
"""
import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional
from io import BytesIO
from abc import ABC, abstractmethod

from app.config.settings import settings

logger = logging.getLogger(__name__)


class StorageServiceInterface(ABC):
    """Abstract interface for storage services"""
    
    @abstractmethod
    def store_raw_data(self, data: List[Dict[str, Any]], pipeline_name: str, run_id: int) -> str:
        """Store raw extracted data"""
        pass
    
    @abstractmethod
    def store_processed_data(self, data: List[Dict[str, Any]], pipeline_name: str, run_id: int) -> str:
        """Store processed/transformed data"""
        pass
    
    @abstractmethod
    def retrieve_data(self, object_path: str) -> List[Dict[str, Any]]:
        """Retrieve data from storage"""
        pass


class MinIOStorageService(StorageServiceInterface):
    """MinIO storage service for local development"""
    
    def __init__(self):
        try:
            from minio import Minio
            from minio.error import S3Error
            self.S3Error = S3Error
            
            self.client = Minio(
                endpoint=settings.minio_endpoint,
                access_key=settings.minio_access_key,
                secret_key=settings.minio_secret_key,
                secure=settings.minio_secure
            )
            self.bucket_name = settings.minio_bucket_name
            self._ensure_bucket_exists()
            logger.info("MinIO storage service initialized")
        except ImportError:
            logger.error("MinIO package not installed. Install with: pip install minio")
            raise
    
    def _ensure_bucket_exists(self) -> None:
        """Ensure the bucket exists, create if it doesn't"""
        try:
            if not self.client.bucket_exists(self.bucket_name):
                self.client.make_bucket(self.bucket_name)
                logger.info(f"Created MinIO bucket: {self.bucket_name}")
        except self.S3Error as e:
            logger.error(f"Error ensuring bucket exists: {e}")
            raise
    
    def store_raw_data(self, data: List[Dict[str, Any]], pipeline_name: str, run_id: int) -> str:
        """Store raw extracted data in MinIO"""
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        object_path = f"raw/{pipeline_name}/{run_id}/{timestamp}_raw_data.json"
        
        try:
            json_data = json.dumps(data, indent=2, default=str)
            data_bytes = json_data.encode('utf-8')
            
            self.client.put_object(
                bucket_name=self.bucket_name,
                object_name=object_path,
                data=BytesIO(data_bytes),
                length=len(data_bytes),
                content_type='application/json'
            )
            
            logger.info(f"Stored raw data at: {object_path}")
            return object_path
            
        except Exception as e:
            logger.error(f"Error storing raw data: {e}")
            raise
    
    def store_processed_data(self, data: List[Dict[str, Any]], pipeline_name: str, run_id: int) -> str:
        """Store processed/transformed data in MinIO"""
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        object_path = f"processed/{pipeline_name}/{run_id}/{timestamp}_processed_data.json"
        
        try:
            json_data = json.dumps(data, indent=2, default=str)
            data_bytes = json_data.encode('utf-8')
            
            self.client.put_object(
                bucket_name=self.bucket_name,
                object_name=object_path,
                data=BytesIO(data_bytes),
                length=len(data_bytes),
                content_type='application/json'
            )
            
            logger.info(f"Stored processed data at: {object_path}")
            return object_path
            
        except Exception as e:
            logger.error(f"Error storing processed data: {e}")
            raise
    
    def retrieve_data(self, object_path: str) -> List[Dict[str, Any]]:
        """Retrieve data from MinIO"""
        try:
            response = self.client.get_object(self.bucket_name, object_path)
            data = json.loads(response.read().decode('utf-8'))
            logger.info(f"Retrieved {len(data)} records from {object_path}")
            return data
        except Exception as e:
            logger.error(f"Error retrieving data from {object_path}: {e}")
            raise


class AzureBlobStorageService(StorageServiceInterface):
    """Azure Blob Storage service for production"""
    
    def __init__(self):
        try:
            from azure.storage.blob import BlobServiceClient
            
            # Create connection string
            connection_string = (
                f"DefaultEndpointsProtocol=https;"
                f"AccountName={settings.azure_storage_account_name};"
                f"AccountKey={settings.azure_storage_account_key};"
                f"EndpointSuffix=core.windows.net"
            )
            
            self.client = BlobServiceClient.from_connection_string(connection_string)
            self.container_name = settings.azure_storage_container_name
            self._ensure_container_exists()
            logger.info("Azure Blob Storage service initialized")
        except ImportError:
            logger.error("Azure Storage package not installed. Install with: pip install azure-storage-blob")
            raise
    
    def _ensure_container_exists(self) -> None:
        """Ensure the container exists, create if it doesn't"""
        try:
            container_client = self.client.get_container_client(self.container_name)
            if not container_client.exists():
                self.client.create_container(self.container_name)
                logger.info(f"Created Azure Blob container: {self.container_name}")
        except Exception as e:
            logger.error(f"Error ensuring container exists: {e}")
            raise
    
    def store_raw_data(self, data: List[Dict[str, Any]], pipeline_name: str, run_id: int) -> str:
        """Store raw extracted data in Azure Blob Storage"""
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        blob_path = f"raw/{pipeline_name}/{run_id}/{timestamp}_raw_data.json"
        
        try:
            json_data = json.dumps(data, indent=2, default=str)
            
            blob_client = self.client.get_blob_client(
                container=self.container_name,
                blob=blob_path
            )
            
            blob_client.upload_blob(
                json_data,
                content_type='application/json',
                overwrite=True
            )
            
            logger.info(f"Stored raw data at: {blob_path}")
            return blob_path
            
        except Exception as e:
            logger.error(f"Error storing raw data: {e}")
            raise
    
    def store_processed_data(self, data: List[Dict[str, Any]], pipeline_name: str, run_id: int) -> str:
        """Store processed/transformed data in Azure Blob Storage"""
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        blob_path = f"processed/{pipeline_name}/{run_id}/{timestamp}_processed_data.json"
        
        try:
            json_data = json.dumps(data, indent=2, default=str)
            
            blob_client = self.client.get_blob_client(
                container=self.container_name,
                blob=blob_path
            )
            
            blob_client.upload_blob(
                json_data,
                content_type='application/json',
                overwrite=True
            )
            
            logger.info(f"Stored processed data at: {blob_path}")
            return blob_path
            
        except Exception as e:
            logger.error(f"Error storing processed data: {e}")
            raise
    
    def retrieve_data(self, blob_path: str) -> List[Dict[str, Any]]:
        """Retrieve data from Azure Blob Storage"""
        try:
            blob_client = self.client.get_blob_client(
                container=self.container_name,
                blob=blob_path
            )
            
            blob_data = blob_client.download_blob().readall()
            data = json.loads(blob_data.decode('utf-8'))
            logger.info(f"Retrieved {len(data)} records from {blob_path}")
            return data
        except Exception as e:
            logger.error(f"Error retrieving data from {blob_path}: {e}")
            raise


def get_storage_service() -> StorageServiceInterface:
    """Factory function to get the appropriate storage service based on configuration"""
    if settings.storage_type.lower() == "azure_blob":
        return AzureBlobStorageService()
    else:
        return MinIOStorageService()


# Global storage service instance (lazy initialization)
_storage_service = None

def get_storage_service_instance() -> StorageServiceInterface:
    """Get the global storage service instance with lazy initialization"""
    global _storage_service
    if _storage_service is None:
        _storage_service = get_storage_service()
    return _storage_service

# For backward compatibility, provide the storage_service as a property
class StorageServiceProxy:
    """Proxy to provide lazy access to storage service"""
    def __getattr__(self, name):
        return getattr(get_storage_service_instance(), name)

storage_service = StorageServiceProxy()
