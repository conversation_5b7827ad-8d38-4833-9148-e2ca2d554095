"""
WebSocket manager for real-time pipeline updates
"""
import json
import logging
from typing import Dict, List, Set
from fastapi import WebSocket, WebSocketDisconnect
import asyncio

logger = logging.getLogger(__name__)


class WebSocketManager:
    """Manages WebSocket connections for real-time pipeline updates"""
    
    def __init__(self):
        # Store active connections by client ID
        self.active_connections: Dict[str, WebSocket] = {}
        # Store pipeline subscriptions by pipeline ID
        self.pipeline_subscriptions: Dict[int, Set[str]] = {}
        
    async def connect(self, websocket: WebSocket, client_id: str):
        """Accept a new WebSocket connection"""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        logger.info(f"WebSocket client {client_id} connected")
        
    def disconnect(self, client_id: str):
        """Remove a WebSocket connection"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            
        # Remove from all pipeline subscriptions
        for pipeline_id, subscribers in self.pipeline_subscriptions.items():
            subscribers.discard(client_id)
            
        logger.info(f"WebSocket client {client_id} disconnected")
        
    async def subscribe_to_pipeline(self, client_id: str, pipeline_id: int):
        """Subscribe a client to pipeline updates"""
        if pipeline_id not in self.pipeline_subscriptions:
            self.pipeline_subscriptions[pipeline_id] = set()
        self.pipeline_subscriptions[pipeline_id].add(client_id)
        logger.info(f"Client {client_id} subscribed to pipeline {pipeline_id}")
        
    async def unsubscribe_from_pipeline(self, client_id: str, pipeline_id: int):
        """Unsubscribe a client from pipeline updates"""
        if pipeline_id in self.pipeline_subscriptions:
            self.pipeline_subscriptions[pipeline_id].discard(client_id)
        logger.info(f"Client {client_id} unsubscribed from pipeline {pipeline_id}")
        
    async def send_pipeline_update(self, pipeline_id: int, update_data: dict):
        """Send update to all clients subscribed to a pipeline"""
        if pipeline_id not in self.pipeline_subscriptions:
            return
            
        subscribers = self.pipeline_subscriptions[pipeline_id].copy()
        message = {
            "type": "pipeline_update",
            "pipeline_id": pipeline_id,
            "data": update_data
        }
        
        # Send to all subscribers
        disconnected_clients = []
        for client_id in subscribers:
            if client_id in self.active_connections:
                try:
                    await self.active_connections[client_id].send_text(json.dumps(message))
                except Exception as e:
                    logger.error(f"Error sending message to client {client_id}: {e}")
                    disconnected_clients.append(client_id)
                    
        # Clean up disconnected clients
        for client_id in disconnected_clients:
            self.disconnect(client_id)
            
    async def send_pipeline_progress(self, pipeline_id: int, step: str, status: str, progress: int = None, message: str = None):
        """Send pipeline progress update"""
        update_data = {
            "step": step,
            "status": status,
            "progress": progress,
            "message": message,
            "timestamp": asyncio.get_event_loop().time()
        }
        await self.send_pipeline_update(pipeline_id, update_data)
        
    async def send_pipeline_completion(self, pipeline_id: int, status: str, run_data: dict):
        """Send pipeline completion notification"""
        update_data = {
            "type": "completion",
            "status": status,
            "run_data": run_data,
            "timestamp": asyncio.get_event_loop().time()
        }
        await self.send_pipeline_update(pipeline_id, update_data)
        
    async def broadcast_stats_update(self, stats_data: dict):
        """Broadcast statistics update to all connected clients"""
        message = {
            "type": "stats_update",
            "data": stats_data
        }
        
        disconnected_clients = []
        for client_id, websocket in self.active_connections.items():
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error broadcasting stats to client {client_id}: {e}")
                disconnected_clients.append(client_id)
                
        # Clean up disconnected clients
        for client_id in disconnected_clients:
            self.disconnect(client_id)
            
    async def handle_client_message(self, client_id: str, message: dict):
        """Handle incoming message from client"""
        message_type = message.get("type")
        
        if message_type == "subscribe":
            pipeline_id = message.get("pipeline_id")
            if pipeline_id:
                await self.subscribe_to_pipeline(client_id, pipeline_id)
                
        elif message_type == "unsubscribe":
            pipeline_id = message.get("pipeline_id")
            if pipeline_id:
                await self.unsubscribe_from_pipeline(client_id, pipeline_id)
                
        elif message_type == "ping":
            # Respond to ping with pong
            if client_id in self.active_connections:
                try:
                    await self.active_connections[client_id].send_text(json.dumps({"type": "pong"}))
                except Exception as e:
                    logger.error(f"Error sending pong to client {client_id}: {e}")
                    self.disconnect(client_id)


# Global WebSocket manager instance
websocket_manager = WebSocketManager()
