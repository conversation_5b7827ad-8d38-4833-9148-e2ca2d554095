"""
ETL Pipeline models for pipeline configuration and execution tracking.
"""
from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .base import Base


class ETLPipeline(Base):
    """ETL Pipeline configuration model"""
    __tablename__ = "etl_pipelines"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # Source configuration
    source_type = Column(String(100), nullable=False)  # 'salesforce', 'api', etc.
    source_object = Column(String(255), nullable=True)  # Salesforce object name
    source_fields = Column(JSON, nullable=True)  # List of source fields
    
    # Destination configuration
    destination_type = Column(String(100), nullable=False)  # 'postgresql', 'minio', etc.
    destination_table = Column(String(255), nullable=True)  # Target table name
    destination_fields = Column(JSON, nullable=True)  # Field mappings
    
    # Pipeline configuration
    transformation_config = Column(JSON, nullable=True)  # Transformation rules
    schedule_config = Column(JSON, nullable=True)  # Scheduling configuration
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    runs = relationship("ETLPipelineRun", back_populates="pipeline", cascade="all, delete-orphan")

    def to_dict(self) -> Dict[str, Any]:
        """Convert model to dictionary"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "source_type": self.source_type,
            "source_object": self.source_object,
            "source_fields": self.source_fields,
            "destination_type": self.destination_type,
            "destination_table": self.destination_table,
            "destination_fields": self.destination_fields,
            "transformation_config": self.transformation_config,
            "schedule_config": self.schedule_config,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class ETLPipelineRun(Base):
    """ETL Pipeline run execution model"""
    __tablename__ = "etl_pipeline_runs"

    id = Column(Integer, primary_key=True, index=True)
    pipeline_id = Column(Integer, ForeignKey("etl_pipelines.id", ondelete="CASCADE"), nullable=False)
    
    # Execution status
    status = Column(String(50), nullable=False, index=True)  # 'running', 'completed', 'failed', 'cancelled'
    started_at = Column(DateTime, default=func.now(), nullable=False, index=True)
    completed_at = Column(DateTime, nullable=True)
    
    # Execution metrics
    records_extracted = Column(Integer, nullable=True)
    records_transformed = Column(Integer, nullable=True)
    records_loaded = Column(Integer, nullable=True)
    
    # Error handling
    error_message = Column(Text, nullable=True)
    logs = Column(JSON, nullable=True)  # Detailed execution logs
    
    # Data storage paths
    raw_data_path = Column(String(500), nullable=True)  # MinIO path to raw data
    processed_data_path = Column(String(500), nullable=True)  # MinIO path to processed data
    
    # Relationships
    pipeline = relationship("ETLPipeline", back_populates="runs")

    def to_dict(self) -> Dict[str, Any]:
        """Convert model to dictionary"""
        return {
            "id": self.id,
            "pipeline_id": self.pipeline_id,
            "status": self.status,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "records_extracted": self.records_extracted,
            "records_transformed": self.records_transformed,
            "records_loaded": self.records_loaded,
            "error_message": self.error_message,
            "logs": self.logs,
            "raw_data_path": self.raw_data_path,
            "processed_data_path": self.processed_data_path,
        }

    @property
    def duration_seconds(self) -> Optional[int]:
        """Calculate run duration in seconds"""
        if self.started_at and self.completed_at:
            return int((self.completed_at - self.started_at).total_seconds())
        return None


__all__ = ["ETLPipeline", "ETLPipelineRun"]
