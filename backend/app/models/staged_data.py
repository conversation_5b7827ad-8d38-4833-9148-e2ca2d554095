"""
Staged Data model for storing transformed data ready for sync.
"""
from sqlalchemy import Column, String, DateTime, Text, ForeignKey, Boolean
from sqlalchemy.dialects.postgresql import UUID, JSON
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

from app.core.database import Base


class StagedData(Base):
    """
    Model for storing transformed data ready for synchronization.

    This table stores data that has been transformed and is ready
    to be synced to the target system (Medusa, etc.).
    Users can preview and edit this data before final sync.
    """
    __tablename__ = "staged_data"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Foreign keys
    migration_job_id = Column(UUID(as_uuid=True), ForeignKey("migration_jobs.id"), nullable=False, index=True)
    raw_data_id = Column(UUID(as_uuid=True), ForeignKey("raw_data.id"), nullable=True, index=True)

    # Data identification
    object_name = Column(String(100), nullable=False, index=True)  # e.g., "inventory", "customers"
    source_id = Column(String(50), nullable=False, index=True)  # Original Salesforce ID
    target_id = Column(String(50), nullable=True, index=True)  # Target system ID (after sync)

    # Transformed data
    transformed_data = Column(JSON, nullable=False)  # Data ready for target system
    original_data = Column(JSON, nullable=True)  # Backup of original transformed data

    # User modifications
    user_modified = Column(Boolean, default=False, nullable=False)  # Has user edited this record?
    user_modifications = Column(JSON, nullable=True)  # Track what fields were modified
    modified_by = Column(String(100), nullable=True)  # User who made modifications
    modified_at = Column(DateTime, nullable=True)  # When modifications were made

    # Processing status
    sync_status = Column(String(20), default="pending", nullable=False, index=True)  # pending, synced, failed, skipped
    sync_error = Column(Text, nullable=True)
    sync_attempts = Column(String(10), default="0", nullable=False)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    synced_at = Column(DateTime, nullable=True)

    # Validation
    validation_status = Column(String(20), default="valid", nullable=False)  # valid, invalid, warning
    validation_errors = Column(JSON, nullable=True)  # List of validation issues

    # Relationships
    migration_job = relationship("MigrationJob", back_populates="staged_data_records")
    raw_data = relationship("RawData", back_populates="staged_data_record")

    def __repr__(self):
        return f"<StagedData(id={self.id}, object_name='{self.object_name}', source_id='{self.source_id}')>"

    @property
    def is_ready_for_sync(self) -> bool:
        """Check if record is ready for synchronization."""
        return (
            self.sync_status == "pending" and
            self.validation_status in ["valid", "warning"]
        )

    @property
    def current_data(self) -> dict:
        """Get the current data (with user modifications if any)."""
        if self.user_modified and self.user_modifications:
            # Apply user modifications to transformed data
            data = self.transformed_data.copy()
            data.update(self.user_modifications)
            return data
        return self.transformed_data

    def apply_user_edit(self, field_name: str, new_value, user_id: str = None):
        """Apply a user edit to a specific field."""
        import logging
        logger = logging.getLogger(__name__)

        try:
            logger.info(f"Applying user edit: field={field_name}, value={new_value}, user={user_id}")
            logger.info(f"Current transformed_data type: {type(self.transformed_data)}")
            logger.info(f"Current transformed_data: {self.transformed_data}")

            # Ensure transformed_data is a dict (handle JSON serialization issues)
            if isinstance(self.transformed_data, str):
                import json
                logger.info("Converting transformed_data from string to dict")
                self.transformed_data = json.loads(self.transformed_data)
            elif not self.transformed_data:
                logger.info("Initializing empty transformed_data dict")
                self.transformed_data = {}

            # Make a copy to avoid mutation issues
            transformed_data_copy = dict(self.transformed_data)
            logger.info(f"Working with transformed_data copy: {transformed_data_copy}")

            # Ensure user_modifications is a dict
            if not self.user_modifications:
                self.user_modifications = {}
            elif isinstance(self.user_modifications, str):
                import json
                self.user_modifications = json.loads(self.user_modifications)

            # Track the change for audit purposes
            if field_name not in self.user_modifications:
                # Store what the user changed (could be used for audit/history)
                self.user_modifications[field_name] = {
                    "old_value": transformed_data_copy.get(field_name),
                    "new_value": new_value
                }
            else:
                # Update the new value in tracking
                self.user_modifications[field_name]["new_value"] = new_value

            # Update the transformed_data directly - this is the current state
            transformed_data_copy[field_name] = new_value
            self.transformed_data = transformed_data_copy

            logger.info(f"Updated transformed_data: {self.transformed_data}")

            # Mark as user modified
            self.user_modified = True
            self.modified_by = user_id
            self.modified_at = datetime.utcnow()

            logger.info("User edit applied successfully")

        except Exception as e:
            logger.error(f"Error in apply_user_edit: {str(e)}")
            logger.error(f"Exception type: {type(e)}")
            raise

    def get_field_value(self, field_name: str, default=None):
        """Get a field value from current data (including user modifications)."""
        return self.current_data.get(field_name, default)
