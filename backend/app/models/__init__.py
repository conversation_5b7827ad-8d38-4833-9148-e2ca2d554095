"""
SQLAlchemy models for the ETL Migration System.
"""
from .migration_job import MigrationJob
from .raw_data import RawData
from .staged_data import StagedData
from .target_schema import TargetSchema
from .field_mapping import FieldMapping

# ETL Pipeline models
from .etl_pipeline import ETLPipeline, ETLPipelineRun
from .destination import Destination
from .hotel import Hotel
from .product_category import ProductCategory
from .product import Product
from .product_category_product import ProductCategoryProduct
from .product_variant import ProductVariant

# ID generators
from .id_generators import (
    generate_destination_id,
    generate_hotel_id,
    generate_product_category_id,
    generate_product_id,
    generate_product_variant_id
)

__all__ = [
    "MigrationJob", "RawData", "StagedData", "TargetSchema", "FieldMapping",
    "ETLPipeline", "ETLPipelineRun", "Destination", "Hotel", "ProductCategory",
    "Product", "ProductCategoryProduct", "ProductVariant",
    "generate_destination_id", "generate_hotel_id", "generate_product_category_id",
    "generate_product_id", "generate_product_variant_id"
]
