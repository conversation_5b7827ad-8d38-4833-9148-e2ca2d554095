"""
Raw Data model for storing extracted data from Salesforce.
"""
from sqlalchemy import Column, String, DateTime, Text, ForeignKey
from sqlalchemy.dialects.postgresql import UUID, JSON
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

from app.core.database import Base


class RawData(Base):
    """
    Model for storing raw data extracted from Salesforce.

    This table stores the original data as extracted from Salesforce
    before any transformation is applied.
    """
    __tablename__ = "raw_data"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Foreign key to migration job
    migration_job_id = Column(UUID(as_uuid=True), ForeignKey("migration_jobs.id"), nullable=False, index=True)

    # Data identification
    object_name = Column(String(100), nullable=False, index=True)  # e.g., "Account", "Product2"
    salesforce_id = Column(String(18), nullable=False, index=True)  # Salesforce record ID

    # Raw data payload
    raw_payload = Column(JSON, nullable=False)  # Complete Salesforce record as JSON

    # Metadata
    extracted_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    salesforce_last_modified = Column(DateTime, nullable=True)  # LastModifiedDate from Salesforce

    # Processing status
    is_processed = Column(String(20), default="pending", nullable=False, index=True)  # pending, processed, failed
    processing_error = Column(Text, nullable=True)

    # Relationships
    migration_job = relationship("MigrationJob", back_populates="raw_data_records")
    staged_data_record = relationship("StagedData", back_populates="raw_data", uselist=False)

    def __repr__(self):
        return f"<RawData(id={self.id}, object_name='{self.object_name}', salesforce_id='{self.salesforce_id}')>"

    @property
    def record_type(self) -> str:
        """Get the Salesforce record type from raw payload."""
        return self.raw_payload.get("attributes", {}).get("type", self.object_name)

    def get_field_value(self, field_name: str, default=None):
        """Get a specific field value from the raw payload."""
        return self.raw_payload.get(field_name, default)
