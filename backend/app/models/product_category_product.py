"""
ProductCategoryProduct junction table model.
"""
from sqlalchemy import Column, String, ForeignKey
from sqlalchemy.orm import relationship

from .base import Base


class ProductCategoryProduct(Base):
    """Junction table for many-to-many relationship between product categories and product"""
    __tablename__ = "product_category_product"

    product_category_id = Column(String(255), ForeignKey("product_category.id"), primary_key=True, index=True)
    product_id = Column(String(26), ForeignKey("product.id"), primary_key=True, index=True)

    # Relationships
    product_category = relationship("ProductCategory", back_populates="product_relationships")
    product = relationship("Product", back_populates="category_relationships")

    def to_dict(self):
        """Convert model to dictionary"""
        return {
            "product_category_id": self.product_category_id,
            "product_id": self.product_id,
        }


__all__ = ["ProductCategoryProduct"]
