"""
Field Mapping model for storing transformation mappings between source and target fields.
"""
from sqlalchemy import Column, String, DateTime, Text, Boolean, ForeignKey
from sqlalchemy.dialects.postgresql import UUID, JSON
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid
import enum

from app.core.database import Base


class FieldMapping(Base):
    """
    Model for storing field mappings between source (Salesforce) and target schema fields.
    
    This table stores the configuration for how Salesforce fields should be mapped
    to target schema fields, including any transformation rules.
    """
    __tablename__ = "field_mappings"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Foreign keys
    target_schema_id = Column(String(100), ForeignKey("target_schemas.id"), nullable=False, index=True)

    # Mapping identification
    mapping_name = Column(String(200), nullable=False)  # Human-readable name for this mapping
    description = Column(Text, nullable=True)
    
    # Source configuration
    source_object = Column(String(100), nullable=False, index=True)  # Salesforce object name
    
    # Mapping configuration
    field_mappings = Column(JSON, nullable=False)  # Field-to-field mappings with transformations
    
    # Mapping rules
    remove_unmapped_fields = Column(<PERSON><PERSON><PERSON>, default=True, nullable=False)  # Remove fields without mappings
    
    # Metadata
    created_by = Column(String(100), nullable=True)  # User who created this mapping
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    target_schema = relationship("TargetSchema", back_populates="field_mappings")

    def __repr__(self):
        return f"<FieldMapping(id={self.id}, name='{self.mapping_name}', source_object='{self.source_object}')>"

    @property
    def field_mappings_parsed(self):
        """Parse the JSON field mappings string into a Python object"""
        try:
            return self.field_mappings if isinstance(self.field_mappings, dict) else {}
        except (TypeError, ValueError):
            return {}
