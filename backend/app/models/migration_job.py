"""
Migration Job model for tracking ETL migration processes.
"""
from sqlalchemy import Column, String, DateTime, Text, Integer, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import UUID, JSON
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid
import enum

from app.core.database import Base


class JobStatus(str, enum.Enum):
    """Migration job status enumeration."""
    PENDING = "pending"
    EXTRACTING = "extracting"
    TRANSFORMING = "transforming"
    STAGING = "staging"
    SYNCING = "syncing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class MigrationJob(Base):
    """
    Model for tracking migration jobs.

    This table stores information about each ETL migration job,
    including status, progress, and metadata.
    """
    __tablename__ = "migration_jobs"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Job identification
    object_name = Column(String(100), nullable=False, index=True)  # e.g., "inventory", "customers"
    job_name = Column(String(200), nullable=True)  # Optional human-readable name

    # Status and progress
    status = Column(SQLEnum(JobStatus), default=JobStatus.PENDING, nullable=False, index=True)
    progress_percentage = Column(Integer, default=0)  # 0-100
    current_step = Column(String(100), nullable=True)  # Current operation description

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Configuration and metadata
    source_config = Column(JSON, nullable=True)  # Salesforce extraction config
    target_config = Column(JSON, nullable=True)  # Target system config (Medusa, etc.)
    transformation_config = Column(JSON, nullable=True)  # Transformation rules

    # Results and statistics
    records_extracted = Column(Integer, default=0)
    records_transformed = Column(Integer, default=0)
    records_staged = Column(Integer, default=0)
    records_synced = Column(Integer, default=0)
    records_failed = Column(Integer, default=0)

    # Error handling
    error_message = Column(Text, nullable=True)
    error_details = Column(JSON, nullable=True)  # Detailed error information

    # Retry information
    retry_count = Column(Integer, default=0)
    max_retries = Column(Integer, default=3)

    # Relationships
    raw_data_records = relationship("RawData", back_populates="migration_job", cascade="all, delete-orphan")
    staged_data_records = relationship("StagedData", back_populates="migration_job", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<MigrationJob(id={self.id}, object_name='{self.object_name}', status='{self.status}')>"

    @property
    def is_running(self) -> bool:
        """Check if the job is currently running."""
        return self.status in [
            JobStatus.EXTRACTING,
            JobStatus.TRANSFORMING,
            JobStatus.STAGING,
            JobStatus.SYNCING
        ]

    @property
    def is_completed(self) -> bool:
        """Check if the job has completed (successfully or with failure)."""
        return self.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]

    def update_progress(self, percentage: int, step: str = None):
        """Update job progress."""
        self.progress_percentage = max(0, min(100, percentage))
        if step:
            self.current_step = step
        self.updated_at = datetime.utcnow()
