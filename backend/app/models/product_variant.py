"""
ProductVariant model for Room Pipeline.
"""
from sqlalchemy import Column, String, <PERSON>ole<PERSON>, DateTime, Integer, ForeignKey, Text, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .base import Base
from .id_generators import generate_product_variant_id


class ProductVariant(Base):
    """Product Variant model for Salesforce Room__c objects"""
    __tablename__ = "product_variant"

    # Primary key with pvar_ prefix
    id = Column(String(26), primary_key=True, index=True, default=generate_product_variant_id)

    # Salesforce Room__c mapping fields
    migrated_id = Column(String(255), nullable=False, unique=True, index=True)
    name = Column(String(255), nullable=False, index=True)

    # Foreign key to product table (Room_Type__c relationship)
    product_id = Column(String(26), ForeignKey("product.id"), nullable=True, index=True)

    # Product variant core fields
    title = Column(Text, nullable=True)
    sku = Column(Text, nullable=True)
    barcode = Column(Text, nullable=True)
    ean = Column(Text, nullable=True)
    upc = Column(Text, nullable=True)

    # Inventory management
    allow_backorder = Column(Boolean, nullable=True, default=False)
    manage_inventory = Column(Boolean, nullable=True, default=True)

    # Product classification
    hs_code = Column(Text, nullable=True)
    origin_country = Column(Text, nullable=True)
    mid_code = Column(Text, nullable=True)
    material = Column(Text, nullable=True)

    # Physical dimensions (in mm for length/width/height, grams for weight)
    weight = Column(Integer, nullable=True)
    length = Column(Integer, nullable=True)
    height = Column(Integer, nullable=True)
    width = Column(Integer, nullable=True)

    # Additional fields
    additional_metadata = Column(JSON, nullable=True)
    variant_rank = Column(Integer, nullable=True)

    # Room-specific fields from Room__c (legacy)
    room_config_name = Column(String(255), nullable=True)
    is_connected = Column(Boolean, nullable=True, default=False)

    # Standard ETL pipeline fields
    pipeline_run_id = Column(Integer, ForeignKey("etl_pipeline_runs.id"), nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    source_created_at = Column(DateTime(timezone=True), nullable=True)
    source_updated_at = Column(DateTime(timezone=True), nullable=True)
    extracted_at = Column(DateTime(timezone=True), nullable=False)
    source_system = Column(String(50), nullable=False, default='salesforce')
    external_id = Column(String(255), nullable=True, index=True)
    is_active = Column(Boolean, nullable=False, default=True)

    # Relationships
    product = relationship("Product", back_populates="variants")
    pipeline_run = relationship("ETLPipelineRun")

    def to_dict(self):
        """Convert model to dictionary"""
        return {
            "id": self.id,
            "migrated_id": self.migrated_id,
            "name": self.name,
            "product_id": self.product_id,
            "title": self.title,
            "sku": self.sku,
            "barcode": self.barcode,
            "ean": self.ean,
            "upc": self.upc,
            "allow_backorder": self.allow_backorder,
            "manage_inventory": self.manage_inventory,
            "hs_code": self.hs_code,
            "origin_country": self.origin_country,
            "mid_code": self.mid_code,
            "material": self.material,
            "weight": self.weight,
            "length": self.length,
            "height": self.height,
            "width": self.width,
            "additional_metadata": self.additional_metadata,
            "variant_rank": self.variant_rank,
            "room_config_name": self.room_config_name,
            "is_connected": self.is_connected,
            "pipeline_run_id": self.pipeline_run_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "source_created_at": self.source_created_at.isoformat() if self.source_created_at else None,
            "source_updated_at": self.source_updated_at.isoformat() if self.source_updated_at else None,
            "extracted_at": self.extracted_at.isoformat() if self.extracted_at else None,
            "source_system": self.source_system,
            "external_id": self.external_id,
            "is_active": self.is_active,
        }


__all__ = ["ProductVariant"]
