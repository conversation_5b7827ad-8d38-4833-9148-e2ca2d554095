from sqlalchemy import Column, String, Text, DateTime, Enum as SQLEnum
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import json
import enum

from app.core.database import Base


class TargetSystem(str, enum.Enum):
    """Target system enumeration."""
    OPS = "ops"  # Ops (Medusa)
    CRM = "crm"  # CRM system

class TargetSchema(Base):
    __tablename__ = "target_schemas"

    id = Column(String(100), primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    label = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    target_system = Column(SQLEnum(TargetSystem), nullable=False, index=True)  # Group by target system
    fields = Column(Text, nullable=False)  # JSON string of field definitions
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    field_mappings = relationship("FieldMapping", back_populates="target_schema", cascade="all, delete-orphan")

    @property
    def fields_parsed(self):
        """Parse the JSON fields string into a Python object"""
        try:
            return json.loads(self.fields) if self.fields else []
        except json.JSONDecodeError:
            return []

    def __repr__(self):
        return f"<TargetSchema(id='{self.id}', name='{self.name}', label='{self.label}')>"
