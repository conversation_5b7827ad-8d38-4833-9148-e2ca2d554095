"""
Product model matching actual Medusa e-commerce database schema.
"""
from sqlalchemy import Column, String, Text, Boolean, DateTime, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .base import Base


class Product(Base):
    """Product table with Medusa e-commerce schema"""
    __tablename__ = "product"

    # Core product fields
    id = Column(Text, primary_key=True, index=True)
    title = Column(Text, nullable=True)
    description = Column(Text, nullable=True)
    external_id = Column(String(255), nullable=True)

    # Product attributes
    subtitle = Column(Text, nullable=True)
    is_giftcard = Column(Boolean, nullable=True)
    status = Column(Text, nullable=True)
    thumbnail = Column(Text, nullable=True)

    # Physical attributes
    weight = Column(Text, nullable=True)
    length = Column(Text, nullable=True)
    height = Column(Text, nullable=True)
    width = Column(Text, nullable=True)

    # Classification
    origin_country = Column(Text, nullable=True)
    hs_code = Column(Text, nullable=True)
    mid_code = Column(Text, nullable=True)
    material = Column(Text, nullable=True)
    collection_id = Column(Text, nullable=True)
    type_id = Column(Text, nullable=True)

    # Business rules
    discountable = Column(Boolean, nullable=True)
    deleted_at = Column(DateTime, nullable=True)
    metadata_ = Column("metadata", JSON, nullable=True)

    # Timestamps
    created_at = Column(DateTime, server_default=func.now(), nullable=False)
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    category_relationships = relationship("ProductCategoryProduct", back_populates="product")
    variants = relationship("ProductVariant", back_populates="product")

    def to_dict(self):
        """Convert model to dictionary"""
        return {
            "id": self.id,
            "title": self.title,
            "description": self.description,
            "external_id": self.external_id,
            "subtitle": self.subtitle,
            "is_giftcard": self.is_giftcard,
            "status": self.status,
            "thumbnail": self.thumbnail,
            "weight": self.weight,
            "length": self.length,
            "height": self.height,
            "width": self.width,
            "origin_country": self.origin_country,
            "hs_code": self.hs_code,
            "mid_code": self.mid_code,
            "material": self.material,
            "collection_id": self.collection_id,
            "type_id": self.type_id,
            "discountable": self.discountable,
            "deleted_at": self.deleted_at.isoformat() if self.deleted_at else None,
            "metadata": self.metadata_,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


__all__ = ["Product"]
