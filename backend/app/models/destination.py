"""
Destination model for Resort Pipeline.
"""
from sqlalchemy import Column, Text, Boolean, DateTime, JSON, Numeric
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .base import Base
from .id_generators import generate_destination_id


class Destination(Base):
    """Destination table for Resort Pipeline - matches powderbyrne database schema"""
    __tablename__ = "destination"

    # Core fields matching powderbyrne schema
    id = Column(Text, primary_key=True, index=True, default=generate_destination_id)
    name = Column(Text, nullable=False)
    handle = Column(Text, nullable=False)
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, nullable=False, default=True)
    country = Column(Text, nullable=False)
    location = Column(Text, nullable=True)
    tags = Column(JSON, nullable=True)
    website = Column(Text, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), nullable=False, default=func.now())
    updated_at = Column(DateTime(timezone=True), nullable=False, default=func.now())
    deleted_at = Column(DateTime(timezone=True), nullable=True)

    # Additional powderbyrne fields
    is_featured = Column(Boolean, nullable=False, default=False)
    ai_content = Column(Text, nullable=True)
    metadata_ = Column("metadata", JSON, nullable=True)  # Use column name mapping to avoid conflict
    internal_web_link = Column(Text, nullable=True)
    external_web_link = Column(Text, nullable=True)
    currency = Column(Text, nullable=True)
    margin = Column(Numeric, nullable=True)

    # ETL tracking fields (we'll store these in metadata JSON)
    # pipeline_run_id, migrated_id, source_system, etc. will be stored in metadata

    # Relationships
    hotel = relationship("Hotel", back_populates="destination")


__all__ = ["Destination"]
