"""
Product Category model for Hotel Pipeline.
"""
from sqlalchemy import Column, Text, Boolean, DateTime, Integer, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .base import Base
from .id_generators import generate_product_category_id


class ProductCategory(Base):
    """Product Category table for Hotel Pipeline"""
    __tablename__ = "product_category"

    id = Column(Text, primary_key=True, index=True, default=generate_product_category_id)
    name = Column(Text, nullable=False)
    description = Column(Text, nullable=True)
    handle = Column(Text, nullable=True, unique=True)
    mpath = Column(Text, nullable=True, index=True)
    is_active = Column(Boolean, nullable=False, default=True)
    is_internal = Column(Boolean, nullable=False, default=False)
    rank = Column(Integer, nullable=False, default=0)
    parent_category_id = Column(Text, ForeignKey("product_category.id"), nullable=True)
    created_at = Column(DateTime(timezone=True), nullable=False, default=func.now())
    updated_at = Column(DateTime(timezone=True), nullable=False, default=func.now(), onupdate=func.now())
    deleted_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    parent_category = relationship("ProductCategory", remote_side=[id], backref="child_categories")
    hotel = relationship("Hotel", back_populates="category")
    product_relationships = relationship("ProductCategoryProduct", back_populates="product_category")


__all__ = ["ProductCategory"]
