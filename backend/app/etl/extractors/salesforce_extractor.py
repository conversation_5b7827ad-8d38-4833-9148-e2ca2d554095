"""
Salesforce data extraction module using both REST API and Bulk API.
"""
from typing import List, Dict, Any, Optional, Iterator
from datetime import datetime, timedelta
import logging
import csv
import io
from simple_salesforce import Salesforce
from simple_salesforce.exceptions import SalesforceError
from salesforce_bulk import SalesforceBulk

from app.config.settings import settings

logger = logging.getLogger(__name__)


class SalesforceExtractor:
    """
    Handles data extraction from Salesforce using both REST API and Bulk API.

    - Uses REST API for small datasets (< 10,000 records)
    - Uses Bulk API for large datasets (>= 10,000 records)
    - Automatically determines which API to use based on record count
    """

    # Threshold for switching to Bulk API
    BULK_API_THRESHOLD = 10000

    def __init__(self, bearer_token: Optional[str] = None):
        """Initialize the Salesforce extractor."""
        self.sf = None
        self.bulk = None
        self._authenticated = False
        self.bearer_token = bearer_token

    def authenticate(self) -> bool:
        """
        Authenticate with Salesforce for both REST and Bulk APIs.
        Supports both Bearer token and credential-based authentication.

        Returns:
            bool: True if authentication successful, False otherwise
        """
        try:
            if self.bearer_token:
                # Use Bearer token authentication (temporary approach)
                logger.info("Using Bearer token authentication")
                logger.info(f"Bearer token (first 20 chars): {self.bearer_token[:20]}...")

                # For Bearer token, we need to determine the instance URL
                # Based on the userinfo endpoint, we know the correct instance URL
                instance_urls_to_try = [
                    'https://powderbyrne.my.salesforce.com',  # Correct instance from userinfo
                    'https://flinkk-dev-ed.develop.my.salesforce.com',
                    'https://flinkk-dev-ed.lightning.force.com',
                    'https://flinkk-dev-ed.my.salesforce.com'
                ]

                authenticated = False
                for instance_url in instance_urls_to_try:
                    try:
                        logger.info(f"Trying instance URL: {instance_url}")

                        self.sf = Salesforce(
                            session_id=self.bearer_token,
                            instance_url=instance_url
                        )

                        # Test the connection by making a simple API call
                        self.sf.query("SELECT Id FROM User LIMIT 1")

                        # If we get here, authentication worked
                        authenticated = True
                        logger.info(f"Successfully authenticated with instance URL: {instance_url}")

                        # Initialize Bulk API using the same session
                        host = instance_url.replace('https://', '').replace('http://', '')
                        self.bulk = SalesforceBulk(
                            sessionId=self.bearer_token,
                            host=host
                        )

                        break

                    except Exception as e:
                        logger.warning(f"Failed to authenticate with {instance_url}: {e}")
                        continue

                if not authenticated:
                    raise Exception("Failed to authenticate with any instance URL. Please check your Bearer token and instance URL.")

                logger.info("Successfully authenticated with Salesforce using Bearer token")
            else:
                # Use credential-based authentication (original approach)
                logger.info("Using credential-based authentication")

                # Log configuration (without sensitive data)
                logger.info(f"Salesforce Username: {settings.salesforce_username}")
                logger.info(f"Salesforce Client ID: {settings.salesforce_client_id}")
                logger.info(f"Salesforce Client Secret (first 10 chars): {settings.salesforce_client_secret[:10]}...")
                logger.info(f"Salesforce Security Token (first 10 chars): {settings.salesforce_security_token[:10]}...")
                logger.info(f"Password length: {len(settings.salesforce_password)} characters")

                # Try OAuth 2.0 first (no security token needed)
                try:
                    logger.info("Attempting OAuth 2.0 authentication (no security token required)")
                    logger.info("Creating Salesforce connection with OAuth parameters...")
                    logger.info(f"Using custom domain: powderbyrne.my")
                    logger.info(f"OAuth URL will be: https://powderbyrne.my.salesforce.com/services/oauth2/token")

                    self.sf = Salesforce(
                        username=settings.salesforce_username,
                        password=settings.salesforce_password,
                        consumer_key=settings.salesforce_client_id,
                        consumer_secret=settings.salesforce_client_secret,
                        domain='powderbyrne.my'  # Use just the subdomain part for My Domain
                    )
                    logger.info("OAuth 2.0 authentication successful!")

                except Exception as oauth_error:
                    logger.warning(f"OAuth 2.0 failed with custom domain: {oauth_error}")
                    logger.info("Trying with standard 'login' domain as fallback...")

                    # Try with standard login domain as fallback
                    try:
                        logger.info("Attempting OAuth 2.0 with standard login domain...")
                        self.sf = Salesforce(
                            username=settings.salesforce_username,
                            password=settings.salesforce_password,
                            consumer_key=settings.salesforce_client_id,
                            consumer_secret=settings.salesforce_client_secret,
                            domain='login'  # Standard domain fallback
                        )
                        logger.info("OAuth 2.0 authentication successful with standard domain!")

                    except Exception as standard_error:
                        logger.warning(f"OAuth 2.0 with standard domain also failed: {standard_error}")
                        logger.info("Falling back to username/password with security token")

                    try:
                        logger.info("Creating Salesforce connection with security token...")
                        logger.info("Trying security token with custom domain first...")
                        # Fallback to security token method with custom domain
                        self.sf = Salesforce(
                            username=settings.salesforce_username,
                            password=settings.salesforce_password,
                            security_token=settings.salesforce_security_token,
                            consumer_key=settings.salesforce_client_id,
                            consumer_secret=settings.salesforce_client_secret,
                            domain='powderbyrne.my'  # Use just the subdomain part for My Domain
                        )
                        logger.info("Security token authentication successful with custom domain!")

                    except Exception as token_error:
                        logger.warning(f"Security token authentication failed with custom domain: {token_error}")
                        logger.info("Trying security token with standard login domain...")

                        try:
                            # Try security token with standard domain
                            self.sf = Salesforce(
                                username=settings.salesforce_username,
                                password=settings.salesforce_password,
                                security_token=settings.salesforce_security_token,
                                consumer_key=settings.salesforce_client_id,
                                consumer_secret=settings.salesforce_client_secret,
                                domain='login'  # Standard domain
                            )
                            logger.info("Security token authentication successful with standard domain!")

                        except Exception as final_error:
                            logger.error(f"All authentication methods failed. Final error: {final_error}")
                            raise final_error

                # Initialize Bulk API using the same session
                logger.info("Initializing Salesforce Bulk API...")
                self.bulk = SalesforceBulk(
                    sessionId=self.sf.session_id,
                    host=self.sf.sf_instance
                )
                logger.info(f"Bulk API initialized with host: {self.sf.sf_instance}")

                logger.info("Successfully authenticated with Salesforce using credentials")

            self._authenticated = True
            return True

        except SalesforceError as e:
            logger.error(f"Salesforce authentication failed: {e}")
            logger.error(f"Error type: {type(e).__name__}")
            if hasattr(e, 'content'):
                logger.error(f"Error content: {e.content}")
            if hasattr(e, 'status'):
                logger.error(f"Error status: {e.status}")
            self._authenticated = False
            return False
        except Exception as e:
            logger.error(f"Unexpected error during Salesforce authentication: {e}")
            logger.error(f"Error type: {type(e).__name__}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            self._authenticated = False
            return False

    def is_authenticated(self) -> bool:
        """Check if currently authenticated with Salesforce."""
        return self._authenticated and self.sf is not None and self.bulk is not None

    def get_object_fields(self, object_name: str) -> List[str]:
        """
        Get all field names for a Salesforce object.

        Args:
            object_name: Name of the Salesforce object (e.g., 'Account', 'Product2')

        Returns:
            List of field names
        """
        if not self.is_authenticated():
            raise ValueError("Not authenticated with Salesforce")

        try:
            # Get object description
            obj_desc = getattr(self.sf, object_name).describe()
            fields = [field['name'] for field in obj_desc['fields']]
            logger.info(f"Retrieved {len(fields)} fields for {object_name}")
            return fields

        except Exception as e:
            logger.error(f"Error getting fields for {object_name}: {e}")
            raise

    def count_records(
        self,
        object_name: str,
        where_clause: Optional[str] = None,
        last_modified_after: Optional[datetime] = None
    ) -> int:
        """
        Count records that match the extraction criteria.

        Args:
            object_name: Name of the Salesforce object
            where_clause: Optional WHERE clause for filtering
            last_modified_after: Only count records modified after this date

        Returns:
            Number of records that match the criteria
        """
        if not self.is_authenticated():
            raise ValueError("Not authenticated with Salesforce")

        try:
            # Build count query
            soql = f"SELECT COUNT() FROM {object_name}"

            # Add WHERE conditions
            where_conditions = []

            if last_modified_after:
                iso_date = last_modified_after.isoformat()
                where_conditions.append(f"LastModifiedDate > {iso_date}")

            if where_clause:
                where_conditions.append(where_clause)

            if where_conditions:
                soql += f" WHERE {' AND '.join(where_conditions)}"

            logger.info(f"Counting records with: {soql}")

            result = self.sf.query(soql)
            count = result['totalSize']

            logger.info(f"Found {count} records in {object_name}")
            return count

        except Exception as e:
            logger.error(f"Error counting records in {object_name}: {e}")
            raise

    def extract_records_rest_api(
        self,
        object_name: str,
        fields: List[str],
        where_clause: Optional[str] = None,
        limit: Optional[int] = None,
        last_modified_after: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """
        Extract records using Salesforce REST API (for smaller datasets).

        Args:
            object_name: Name of the Salesforce object
            fields: List of fields to extract
            where_clause: Optional WHERE clause for filtering
            limit: Maximum number of records to extract
            last_modified_after: Only get records modified after this date

        Returns:
            List of record dictionaries
        """
        try:
            # Build SOQL query
            soql = f"SELECT {', '.join(fields)} FROM {object_name}"

            # Add WHERE conditions
            where_conditions = []

            if last_modified_after:
                iso_date = last_modified_after.isoformat()
                where_conditions.append(f"LastModifiedDate > {iso_date}")

            if where_clause:
                where_conditions.append(where_clause)

            if where_conditions:
                soql += f" WHERE {' AND '.join(where_conditions)}"

            # Add ORDER BY for consistent results
            soql += " ORDER BY LastModifiedDate ASC"

            # Add LIMIT if specified
            if limit:
                soql += f" LIMIT {limit}"

            logger.info(f"Executing REST API SOQL: {soql}")

            # Execute query
            result = self.sf.query_all(soql)
            records = result['records']

            # Remove Salesforce metadata from records
            cleaned_records = []
            for record in records:
                # Remove 'attributes' field that Salesforce adds
                cleaned_record = {k: v for k, v in record.items() if k != 'attributes'}
                cleaned_records.append(cleaned_record)

            logger.info(f"Extracted {len(cleaned_records)} records from {object_name} via REST API")
            return cleaned_records

        except Exception as e:
            logger.error(f"Error extracting records from {object_name} via REST API: {e}")
            raise

    def extract_records_bulk_api(
        self,
        object_name: str,
        fields: List[str],
        where_clause: Optional[str] = None,
        last_modified_after: Optional[datetime] = None
    ) -> Iterator[Dict[str, Any]]:
        """
        Extract records using Salesforce Bulk API (for larger datasets).

        Args:
            object_name: Name of the Salesforce object
            fields: List of fields to extract
            where_clause: Optional WHERE clause for filtering
            last_modified_after: Only get records modified after this date

        Yields:
            Record dictionaries one at a time
        """
        try:
            # Build SOQL query
            soql = f"SELECT {', '.join(fields)} FROM {object_name}"

            # Add WHERE conditions
            where_conditions = []

            if last_modified_after:
                iso_date = last_modified_after.isoformat()
                where_conditions.append(f"LastModifiedDate > {iso_date}")

            if where_clause:
                where_conditions.append(where_clause)

            if where_conditions:
                soql += f" WHERE {' AND '.join(where_conditions)}"

            # Add ORDER BY for consistent results
            soql += " ORDER BY LastModifiedDate ASC"

            logger.info(f"Executing Bulk API SOQL: {soql}")

            # Create bulk job
            job = self.bulk.create_query_job(object_name, contentType='CSV')

            # Add batch to job
            batch = self.bulk.query(job, soql)

            # Wait for batch to complete
            self.bulk.wait_for_batch(job, batch)

            # Get results
            result = self.bulk.get_all_results_for_query_batch(batch)

            # Process CSV results
            for result_set in result:
                csv_reader = csv.DictReader(io.StringIO(result_set))
                for row in csv_reader:
                    # Convert empty strings to None for consistency
                    cleaned_row = {k: (v if v != '' else None) for k, v in row.items()}
                    yield cleaned_row

            # Close the job
            self.bulk.close_job(job)

            logger.info(f"Completed Bulk API extraction from {object_name}")

        except Exception as e:
            logger.error(f"Error extracting records from {object_name} via Bulk API: {e}")
            raise

    def extract_records(
        self,
        object_name: str,
        fields: Optional[List[str]] = None,
        where_clause: Optional[str] = None,
        limit: Optional[int] = None,
        last_modified_after: Optional[datetime] = None,
        force_bulk_api: bool = False
    ) -> List[Dict[str, Any]]:
        """
        Extract records from a Salesforce object.
        Automatically chooses between REST API and Bulk API based on data size.

        Args:
            object_name: Name of the Salesforce object
            fields: List of fields to extract (if None, gets all fields)
            where_clause: Optional WHERE clause for filtering
            limit: Maximum number of records to extract
            last_modified_after: Only get records modified after this date
            force_bulk_api: Force use of Bulk API regardless of record count

        Returns:
            List of record dictionaries
        """
        if not self.is_authenticated():
            raise ValueError("Not authenticated with Salesforce")

        try:
            # Get fields if not provided
            if fields is None:
                fields = self.get_object_fields(object_name)

            # Count records to determine which API to use
            if not force_bulk_api and limit and limit < self.BULK_API_THRESHOLD:
                # Use REST API if limit is small
                use_bulk_api = False
            elif not force_bulk_api:
                # Count records to decide
                record_count = self.count_records(object_name, where_clause, last_modified_after)
                use_bulk_api = record_count >= self.BULK_API_THRESHOLD
            else:
                use_bulk_api = True

            if use_bulk_api:
                logger.info(f"Using Bulk API for {object_name} extraction")
                # Convert iterator to list for Bulk API
                records = list(self.extract_records_bulk_api(
                    object_name, fields, where_clause, last_modified_after
                ))

                # Apply limit if specified (Bulk API doesn't support LIMIT in SOQL)
                if limit:
                    records = records[:limit]

                return records
            else:
                logger.info(f"Using REST API for {object_name} extraction")
                return self.extract_records_rest_api(
                    object_name, fields, where_clause, limit, last_modified_after
                )

        except Exception as e:
            logger.error(f"Error extracting records from {object_name}: {e}")
            raise

    def get_all_objects(self) -> List[Dict[str, Any]]:
        """
        Get all available Salesforce objects in the org.

        Returns:
            List of object metadata dictionaries
        """
        if not self.is_authenticated():
            raise ValueError("Not authenticated with Salesforce")

        try:
            # Get global describe to list all objects
            global_describe = self.sf.describe()

            objects = []
            for obj in global_describe['sobjects']:
                # Filter to include only queryable objects that are commonly used
                if (obj['queryable'] and
                    not obj['name'].endswith('__History') and
                    not obj['name'].endswith('__Share') and
                    not obj['name'].endswith('__Feed') and
                    not obj['name'].startswith('SBQQ__') and  # Exclude CPQ objects
                    not obj['name'].startswith('blng__') and  # Exclude billing objects
                    obj['name'] not in ['User', 'Profile', 'PermissionSet', 'Organization']):  # Exclude system objects

                    objects.append({
                        'name': obj['name'],
                        'label': obj['label'],
                        'labelPlural': obj['labelPlural'],
                        'queryable': obj['queryable'],
                        'createable': obj['createable'],
                        'updateable': obj['updateable'],
                        'deletable': obj['deletable'],
                        'custom': obj['custom'],
                        'keyPrefix': obj['keyPrefix']
                    })

            # Sort by label for better UX
            objects.sort(key=lambda x: x['label'])

            logger.info(f"Retrieved {len(objects)} queryable Salesforce objects")
            return objects

        except Exception as e:
            logger.error(f"Error getting Salesforce objects: {e}")
            raise

    def get_object_info(self, object_name: str) -> Dict[str, Any]:
        """
        Get detailed information about a Salesforce object.

        Args:
            object_name: Name of the Salesforce object

        Returns:
            Dictionary with object metadata
        """
        if not self.is_authenticated():
            raise ValueError("Not authenticated with Salesforce")

        try:
            obj_desc = getattr(self.sf, object_name).describe()

            return {
                'name': obj_desc['name'],
                'label': obj_desc['label'],
                'labelPlural': obj_desc['labelPlural'],
                'keyPrefix': obj_desc['keyPrefix'],
                'createable': obj_desc['createable'],
                'updateable': obj_desc['updateable'],
                'deletable': obj_desc['deletable'],
                'queryable': obj_desc['queryable'],
                'field_count': len(obj_desc['fields']),
                'fields': [
                    {
                        'name': field['name'],
                        'label': field['label'],
                        'type': field['type'],
                        'length': field.get('length'),
                        'required': not field['nillable'],
                        'updateable': field['updateable']
                    }
                    for field in obj_desc['fields']
                ]
            }

        except Exception as e:
            logger.error(f"Error getting object info for {object_name}: {e}")
            raise
