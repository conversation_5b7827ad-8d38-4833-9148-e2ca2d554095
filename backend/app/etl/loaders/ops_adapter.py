"""
OPS adapter for syncing data to OPS (Medusa) system.
"""
from typing import Dict, Any, List, Optional
import httpx
import logging
from .base_adapter import BaseTargetAdapter, SyncOperation, SyncResult

logger = logging.getLogger(__name__)


class OpsAdapter(BaseTargetAdapter):
    """
    Adapter for syncing data to OPS (Medusa) system.

    Supports syncing:
    - destination (bulk import)
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize OPS adapter.

        Expected config:
        {
            "api_url": "http://localhost:9000",
            "api_key": "your_ops_api_key",
            "timeout": 30
        }
        """
        super().__init__(config)
        self.api_url = config.get("api_url", "http://localhost:9000").rstrip("/")
        self.api_key = config.get("api_key", "")
        self.timeout = config.get("timeout", 30)
        self.client = None

        # Support any object type - let OPS API handle validation
        self.supported_objects = []  # Empty means all types supported

    def authenticate(self) -> bool:
        """
        Authenticate with OPS system.
        For now, OPS doesn't require authentication.
        """
        try:
            headers = {"Content-Type": "application/json"}
            if self.api_key:
                headers["x-publishable-api-key"] = self.api_key

            self.client = httpx.Client(
                base_url=self.api_url,
                timeout=self.timeout,
                headers=headers
            )
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize OPS client: {e}")
            return False

    def is_authenticated(self) -> bool:
        """Check if authenticated with OPS."""
        return self.client is not None

    def test_connection(self) -> bool:
        """Test connection to OPS system."""
        if not self.is_authenticated():
            return False

        try:
            # Try to make a simple request to test connectivity
            response = self.client.get("/health")
            return response.status_code == 200
        except Exception as e:
            self.logger.error(f"OPS connection test failed: {e}")
            return False

    def get_supported_objects(self) -> List[str]:
        """Get list of supported object types. Empty list means all types supported."""
        return ["destination"]  # Return known types for UI, but adapter supports any type

    def validate_record(self, object_type: str, data: Dict[str, Any]) -> tuple[bool, List[str]]:
        """
        Basic validation for OPS system - just check if data exists.
        Let OPS API handle detailed validation.

        Args:
            object_type: Type of object
            data: Record data to validate

        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        if not data:
            return False, ["No data provided"]

        if not isinstance(data, dict):
            return False, ["Data must be a dictionary"]

        return True, []

    def transform_record(self, object_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Pass-through transformation - no field mapping or transformation.
        Data should already be transformed by the frontend before reaching here.

        Args:
            object_type: Type of object (not used)
            data: Already transformed record data

        Returns:
            The same data (pass-through)
        """
        # Pass-through transformation - no field mapping or transformation.
        # Data should already be transformed by the frontend before reaching here.
        return data

    def sync_record(
        self,
        object_type: str,
        data: Dict[str, Any],
        operation: SyncOperation = SyncOperation.UPSERT,
        external_id_field: Optional[str] = None
    ) -> SyncResult:
        """Sync a single record to OPS."""
        if not self.is_authenticated():
            return SyncResult(False, error="Not authenticated with OPS")

        try:
            # Basic validation
            is_valid, errors = self.validate_record(object_type, data)
            if not is_valid:
                return SyncResult(False, error=f"Validation failed: {', '.join(errors)}")

            # Use bulk endpoint with single item - let OPS handle object type routing
            results = self._sync_bulk([data], object_type)
            return results[0] if results else SyncResult(False, error="No result returned")

        except Exception as e:
            self.logger.error(f"Error syncing {object_type} record: {e}")
            return SyncResult(False, error=str(e))

    def sync_batch(
        self,
        object_type: str,
        records: List[Dict[str, Any]],
        operation: SyncOperation = SyncOperation.UPSERT,
        external_id_field: Optional[str] = None
    ) -> List[SyncResult]:
        """Sync a batch of records to OPS."""
        if not self.is_authenticated():
            return [SyncResult(False, error="Not authenticated with OPS") for _ in records]

        try:
            # Basic validation for all records
            for record in records:
                is_valid, errors = self.validate_record(object_type, record)
                if not is_valid:
                    return [SyncResult(False, error=f"Validation failed: {', '.join(errors)}") for _ in records]

            # Use bulk sync - let OPS handle object type routing
            return self._sync_bulk(records, object_type)

        except Exception as e:
            self.logger.error(f"Error syncing {object_type} batch: {e}")
            return [SyncResult(False, error=str(e)) for _ in records]

    def get_record(self, object_type: str, record_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve a record from OPS."""
        if not self.is_authenticated():
            return None

        try:
            if object_type == "destination":
                # OPS doesn't have a specific get endpoint for destination
                # This would need to be implemented based on OPS API
                return None
            else:
                return None

        except Exception as e:
            self.logger.error(f"Error retrieving {object_type} {record_id}: {e}")
            return None

    def delete_record(self, object_type: str, record_id: str) -> SyncResult:
        """Delete a record from OPS."""
        if not self.is_authenticated():
            return SyncResult(False, error="Not authenticated with OPS")

        try:
            if object_type == "destination":
                # OPS doesn't have a specific delete endpoint for destination
                # This would need to be implemented based on OPS API
                return SyncResult(False, error="Delete not implemented for destination")
            else:
                return SyncResult(False, error=f"Delete not supported for {object_type}")

        except Exception as e:
            self.logger.error(f"Error deleting {object_type} {record_id}: {e}")
            return SyncResult(False, error=str(e))

    def _sync_bulk(self, records: List[Dict[str, Any]], object_type: str) -> List[SyncResult]:
        """
        Sync records using the appropriate bulk endpoint.

        Args:
            records: List of records to sync
            object_type: Type of object being synced

        Returns:
            List of SyncResult objects
        """
        try:
            # Determine endpoint and payload structure based on object type
            if object_type == "destination":
                endpoint = "/store/etl/destination/bulk"
                payload = {"destination": records}
            else:
                # For other object types, try a generic approach
                endpoint = f"/store/etl/{object_type}s/bulk"
                payload = {f"{object_type}s": records}

            # Make the bulk request
            response = self.client.post(endpoint, json=payload)

            if response.status_code in [200, 201]:
                # Success - return success result for each record
                return [SyncResult(True, operation=SyncOperation.CREATE) for _ in records]
            else:
                # Error - return error result for each record
                error_msg = f"Bulk sync failed: {response.status_code} - {response.text}"
                return [SyncResult(False, error=error_msg) for _ in records]

        except Exception as e:
            error_msg = f"Bulk sync error: {str(e)}"
            self.logger.error(error_msg)
            return [SyncResult(False, error=error_msg) for _ in records]
