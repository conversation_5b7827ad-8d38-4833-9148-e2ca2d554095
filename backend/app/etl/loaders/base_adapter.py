"""
Base adapter interface for target CRM systems.
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class SyncOperation(str, Enum):
    """Types of sync operations."""
    CREATE = "create"
    UPDATE = "update"
    UPSERT = "upsert"
    DELETE = "delete"


class SyncResult:
    """Result of a sync operation."""

    def __init__(
        self,
        success: bool,
        target_id: Optional[str] = None,
        error: Optional[str] = None,
        operation: Optional[SyncOperation] = None
    ):
        self.success = success
        self.target_id = target_id
        self.error = error
        self.operation = operation

    def __repr__(self):
        return f"SyncResult(success={self.success}, target_id={self.target_id}, operation={self.operation})"


class BaseTargetAdapter(ABC):
    """
    Abstract base class for all target system adapters.

    This interface ensures consistency across different CRM systems
    and makes it easy to add new target systems in the future.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the adapter with configuration.

        Args:
            config: Configuration dictionary containing connection details
        """
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self._authenticated = False

    @abstractmethod
    def authenticate(self) -> bool:
        """
        Authenticate with the target system.

        Returns:
            True if authentication successful, False otherwise
        """
        pass

    @abstractmethod
    def test_connection(self) -> bool:
        """
        Test the connection to the target system.

        Returns:
            True if connection is working, False otherwise
        """
        pass

    @abstractmethod
    def get_supported_objects(self) -> List[str]:
        """
        Get list of supported object types for this adapter.

        Returns:
            List of supported object type names
        """
        pass

    @abstractmethod
    def sync_record(
        self,
        object_type: str,
        data: Dict[str, Any],
        operation: SyncOperation = SyncOperation.UPSERT,
        external_id_field: Optional[str] = None
    ) -> SyncResult:
        """
        Sync a single record to the target system.

        Args:
            object_type: Type of object (e.g., "product", "customer")
            data: Record data to sync
            operation: Type of sync operation
            external_id_field: Field to use for external ID matching

        Returns:
            SyncResult with operation details
        """
        pass

    @abstractmethod
    def sync_batch(
        self,
        object_type: str,
        records: List[Dict[str, Any]],
        operation: SyncOperation = SyncOperation.UPSERT,
        external_id_field: Optional[str] = None
    ) -> List[SyncResult]:
        """
        Sync a batch of records to the target system.

        Args:
            object_type: Type of object
            records: List of records to sync
            operation: Type of sync operation
            external_id_field: Field to use for external ID matching

        Returns:
            List of SyncResult objects
        """
        pass

    @abstractmethod
    def get_record(self, object_type: str, record_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve a record from the target system.

        Args:
            object_type: Type of object
            record_id: ID of the record to retrieve

        Returns:
            Record data or None if not found
        """
        pass

    @abstractmethod
    def delete_record(self, object_type: str, record_id: str) -> SyncResult:
        """
        Delete a record from the target system.

        Args:
            object_type: Type of object
            record_id: ID of the record to delete

        Returns:
            SyncResult with operation details
        """
        pass

    def is_authenticated(self) -> bool:
        """Check if adapter is authenticated."""
        return self._authenticated

    def validate_record(self, object_type: str, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate a record before syncing.

        Args:
            object_type: Type of object
            data: Record data to validate

        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        # Base implementation - override in subclasses for specific validation
        errors = []

        if not data:
            errors.append("Record data is empty")

        return len(errors) == 0, errors

    def get_field_mapping(self, object_type: str) -> Dict[str, str]:
        """
        Get field mapping for an object type.

        Args:
            object_type: Type of object

        Returns:
            Dictionary mapping source fields to target fields
        """
        # Base implementation - override in subclasses
        return {}

    def transform_record(self, object_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform a record for the target system.

        Args:
            object_type: Type of object
            data: Source record data

        Returns:
            Transformed record data
        """
        # Apply field mapping
        field_mapping = self.get_field_mapping(object_type)
        if not field_mapping:
            return data

        transformed = {}
        for source_field, target_field in field_mapping.items():
            if source_field in data:
                transformed[target_field] = data[source_field]

        return transformed
