"""
Medusa adapter for syncing data to Medusa e-commerce platform.
"""
from typing import Dict, Any, List, Optional
import httpx
import logging
from .base_adapter import BaseTargetAdapter, SyncOperation, SyncResult

logger = logging.getLogger(__name__)


class MedusaAdapter(BaseTargetAdapter):
    """
    Adapter for syncing data to Medusa e-commerce platform.

    Supports syncing:
    - product (from Salesforce Product2)
    - Customers (from Salesforce Account/Contact)
    - Orders (from Salesforce Opportunity)
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize Medusa adapter.

        Expected config:
        {
            "api_url": "https://your-medusa-store.com",
            "api_key": "your_api_key",
            "timeout": 30
        }
        """
        super().__init__(config)
        self.api_url = config.get("api_url", "").rstrip("/")
        self.api_key = config.get("api_key", "")
        self.timeout = config.get("timeout", 30)
        self.client = None

        # Object type mappings
        self.supported_objects = ["product", "customer", "order"]

        # Field mappings for different object types
        self.field_mappings = {
            "product": {
                "Name": "title",
                "Description": "description",
                "ProductCode": "handle",
                "IsActive": "status",
                "Family": "type_id",
                "StockKeepingUnit": "variants.0.sku",
                "UnitPrice": "variants.0.prices.0.amount",
            },
            "customer": {
                "Name": "first_name",
                "BillingStreet": "billing_address.address_1",
                "BillingCity": "billing_address.city",
                "BillingState": "billing_address.province",
                "BillingPostalCode": "billing_address.postal_code",
                "BillingCountry": "billing_address.country_code",
                "Phone": "phone",
                "PersonEmail": "email",
            }
        }

    def authenticate(self) -> bool:
        """Authenticate with Medusa API."""
        try:
            self.client = httpx.Client(
                base_url=self.api_url,
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                },
                timeout=self.timeout
            )

            # Test authentication by getting store info
            response = self.client.get("/admin/store")
            if response.status_code == 200:
                self._authenticated = True
                self.logger.info("Successfully authenticated with Medusa")
                return True
            else:
                self.logger.error(f"Medusa authentication failed: {response.status_code}")
                return False

        except Exception as e:
            self.logger.error(f"Error authenticating with Medusa: {e}")
            return False

    def test_connection(self) -> bool:
        """Test connection to Medusa."""
        if not self.is_authenticated():
            return self.authenticate()

        try:
            response = self.client.get("/admin/store")
            return response.status_code == 200
        except Exception as e:
            self.logger.error(f"Medusa connection test failed: {e}")
            return False

    def get_supported_objects(self) -> List[str]:
        """Get supported object types."""
        return self.supported_objects.copy()

    def sync_record(
        self,
        object_type: str,
        data: Dict[str, Any],
        operation: SyncOperation = SyncOperation.UPSERT,
        external_id_field: Optional[str] = None
    ) -> SyncResult:
        """Sync a single record to Medusa."""
        if not self.is_authenticated():
            return SyncResult(False, error="Not authenticated with Medusa")

        if object_type not in self.supported_objects:
            return SyncResult(False, error=f"Unsupported object type: {object_type}")

        try:
            # Transform the record
            transformed_data = self.transform_record(object_type, data)

            # Validate the record
            is_valid, errors = self.validate_record(object_type, transformed_data)
            if not is_valid:
                return SyncResult(False, error=f"Validation failed: {', '.join(errors)}")

            # Perform the sync operation
            if object_type == "product":
                return self._sync_product(transformed_data, operation, external_id_field)
            elif object_type == "customer":
                return self._sync_customer(transformed_data, operation, external_id_field)
            elif object_type == "order":
                return self._sync_order(transformed_data, operation, external_id_field)
            else:
                return SyncResult(False, error=f"Sync not implemented for {object_type}")

        except Exception as e:
            self.logger.error(f"Error syncing {object_type} record: {e}")
            return SyncResult(False, error=str(e))

    def sync_batch(
        self,
        object_type: str,
        records: List[Dict[str, Any]],
        operation: SyncOperation = SyncOperation.UPSERT,
        external_id_field: Optional[str] = None
    ) -> List[SyncResult]:
        """Sync a batch of records to Medusa."""
        results = []

        for record in records:
            result = self.sync_record(object_type, record, operation, external_id_field)
            results.append(result)

        return results

    def get_record(self, object_type: str, record_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve a record from Medusa."""
        if not self.is_authenticated():
            return None

        try:
            if object_type == "product":
                response = self.client.get(f"/admin/product/{record_id}")
            elif object_type == "customer":
                response = self.client.get(f"/admin/customers/{record_id}")
            elif object_type == "order":
                response = self.client.get(f"/admin/orders/{record_id}")
            else:
                return None

            if response.status_code == 200:
                return response.json()
            else:
                return None

        except Exception as e:
            self.logger.error(f"Error retrieving {object_type} {record_id}: {e}")
            return None

    def delete_record(self, object_type: str, record_id: str) -> SyncResult:
        """Delete a record from Medusa."""
        if not self.is_authenticated():
            return SyncResult(False, error="Not authenticated with Medusa")

        try:
            if object_type == "product":
                response = self.client.delete(f"/admin/product/{record_id}")
            elif object_type == "customer":
                response = self.client.delete(f"/admin/customers/{record_id}")
            elif object_type == "order":
                # Orders typically can't be deleted, only cancelled
                response = self.client.post(f"/admin/orders/{record_id}/cancel")
            else:
                return SyncResult(False, error=f"Delete not supported for {object_type}")

            if response.status_code in [200, 204]:
                return SyncResult(True, operation=SyncOperation.DELETE)
            else:
                return SyncResult(False, error=f"Delete failed: {response.status_code}")

        except Exception as e:
            self.logger.error(f"Error deleting {object_type} {record_id}: {e}")
            return SyncResult(False, error=str(e))

    def get_field_mapping(self, object_type: str) -> Dict[str, str]:
        """Get field mapping for an object type."""
        return self.field_mappings.get(object_type, {})

    def validate_record(self, object_type: str, data: Dict[str, Any]) -> tuple[bool, List[str]]:
        """Validate a record for Medusa."""
        errors = []

        if object_type == "product":
            if not data.get("title"):
                errors.append("Product title is required")
            if not data.get("handle"):
                errors.append("Product handle is required")

        elif object_type == "customer":
            if not data.get("email"):
                errors.append("Customer email is required")
            if not data.get("first_name"):
                errors.append("Customer first name is required")

        elif object_type == "order":
            if not data.get("email"):
                errors.append("Order email is required")
            if not data.get("items") or len(data["items"]) == 0:
                errors.append("Order must have at least one item")

        return len(errors) == 0, errors

    def _sync_product(
        self,
        data: Dict[str, Any],
        operation: SyncOperation,
        external_id_field: Optional[str] = None
    ) -> SyncResult:
        """Sync a product to Medusa."""
        try:
            # Check if product exists by handle or external ID
            existing_product = None
            if external_id_field and data.get(external_id_field):
                # Search by external ID (e.g., Salesforce ID)
                search_response = self.client.get(
                    f"/admin/product",
                    params={"handle": data.get("handle")}
                )
                if search_response.status_code == 200:
                    product = search_response.json().get("product", [])
                    existing_product = product[0] if product else None

            if operation == SyncOperation.CREATE or not existing_product:
                # Create new product
                response = self.client.post("/admin/product", json=data)
                if response.status_code == 200:
                    product = response.json().get("product", {})
                    return SyncResult(True, target_id=product.get("id"), operation=SyncOperation.CREATE)
                else:
                    return SyncResult(False, error=f"Create failed: {response.status_code}")

            elif operation in [SyncOperation.UPDATE, SyncOperation.UPSERT]:
                # Update existing product
                product_id = existing_product["id"]
                response = self.client.post(f"/admin/product/{product_id}", json=data)
                if response.status_code == 200:
                    return SyncResult(True, target_id=product_id, operation=SyncOperation.UPDATE)
                else:
                    return SyncResult(False, error=f"Update failed: {response.status_code}")

            else:
                return SyncResult(False, error=f"Unsupported operation: {operation}")

        except Exception as e:
            return SyncResult(False, error=str(e))

    def _sync_customer(
        self,
        data: Dict[str, Any],
        operation: SyncOperation,
        external_id_field: Optional[str] = None
    ) -> SyncResult:
        """Sync a customer to Medusa."""
        try:
            # Check if customer exists by email
            existing_customer = None
            if data.get("email"):
                search_response = self.client.get(
                    f"/admin/customers",
                    params={"email": data["email"]}
                )
                if search_response.status_code == 200:
                    customers = search_response.json().get("customers", [])
                    existing_customer = customers[0] if customers else None

            if operation == SyncOperation.CREATE or not existing_customer:
                # Create new customer
                response = self.client.post("/admin/customers", json=data)
                if response.status_code == 200:
                    customer = response.json().get("customer", {})
                    return SyncResult(True, target_id=customer.get("id"), operation=SyncOperation.CREATE)
                else:
                    return SyncResult(False, error=f"Create failed: {response.status_code}")

            elif operation in [SyncOperation.UPDATE, SyncOperation.UPSERT]:
                # Update existing customer
                customer_id = existing_customer["id"]
                response = self.client.post(f"/admin/customers/{customer_id}", json=data)
                if response.status_code == 200:
                    return SyncResult(True, target_id=customer_id, operation=SyncOperation.UPDATE)
                else:
                    return SyncResult(False, error=f"Update failed: {response.status_code}")

            else:
                return SyncResult(False, error=f"Unsupported operation: {operation}")

        except Exception as e:
            return SyncResult(False, error=str(e))

    def _sync_order(
        self,
        data: Dict[str, Any],
        operation: SyncOperation,
        external_id_field: Optional[str] = None
    ) -> SyncResult:
        """Sync an order to Medusa."""
        try:
            # Orders are typically only created, not updated
            if operation == SyncOperation.CREATE:
                response = self.client.post("/admin/orders", json=data)
                if response.status_code == 200:
                    order = response.json().get("order", {})
                    return SyncResult(True, target_id=order.get("id"), operation=SyncOperation.CREATE)
                else:
                    return SyncResult(False, error=f"Create failed: {response.status_code}")
            else:
                return SyncResult(False, error="Orders can only be created, not updated")

        except Exception as e:
            return SyncResult(False, error=str(e))
