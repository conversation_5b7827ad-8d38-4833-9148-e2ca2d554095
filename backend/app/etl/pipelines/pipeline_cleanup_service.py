"""
Pipeline cleanup service for handling stuck and orphaned pipeline runs
"""
import logging
from datetime import datetime, timedelta
from typing import List
from sqlalchemy.orm import Session

from app.models.etl_pipeline import ETLPipelineRun, ETLPipeline

logger = logging.getLogger(__name__)


class PipelineCleanupService:
    """Service for cleaning up stuck and orphaned pipeline runs"""
    
    def __init__(self, db: Session):
        self.db = db
        self.logger = logger
    
    def cleanup_stuck_runs(self, max_runtime_hours: int = 2) -> int:
        """
        Clean up pipeline runs that have been stuck in 'running' status
        
        Args:
            max_runtime_hours: Maximum hours a pipeline should run before being considered stuck
            
        Returns:
            Number of runs cleaned up
        """
        cutoff_time = datetime.utcnow() - timedelta(hours=max_runtime_hours)
        
        # Find all stuck pipeline runs
        stuck_runs = self.db.query(ETLPipelineRun).filter(
            ETLPipelineRun.status == 'running'
        ).all()
        
        if not stuck_runs:
            self.logger.info("No stuck pipeline runs found")
            return 0
        
        cleanup_count = 0
        self.logger.info(f"Found {len(stuck_runs)} pipeline runs in 'running' status, analyzing...")
        
        for run in stuck_runs:
            pipeline = self.db.query(ETLPipeline).filter(ETLPipeline.id == run.pipeline_id).first()
            pipeline_name = pipeline.name if pipeline else f"Pipeline {run.pipeline_id}"
            
            runtime = datetime.utcnow() - run.started_at if run.started_at else timedelta(0)
            
            # Determine appropriate action based on run state
            if runtime > timedelta(hours=max_runtime_hours):
                # Run has been going too long
                run.status = "failed"
                run.error_message = f"Pipeline run terminated due to exceeding maximum runtime of {max_runtime_hours} hours (actual: {runtime})"
                run.completed_at = datetime.utcnow()
                cleanup_count += 1
                self.logger.info(f"Marked {pipeline_name} run {run.id} as failed (exceeded max runtime: {runtime})")
                
            elif run.records_loaded and run.records_loaded > 0:
                # Pipeline appears to have completed successfully but status wasn't updated
                run.status = "completed"
                run.completed_at = datetime.utcnow()
                cleanup_count += 1
                self.logger.info(f"Marked {pipeline_name} run {run.id} as completed (had {run.records_loaded} loaded records)")
                
            elif run.records_extracted and run.records_extracted > 0 and runtime > timedelta(minutes=10):
                # Pipeline extracted data but got stuck during processing
                run.status = "failed"
                run.error_message = f"Pipeline run stuck during processing for {runtime} - auto-cleaned up"
                run.completed_at = datetime.utcnow()
                cleanup_count += 1
                self.logger.info(f"Marked {pipeline_name} run {run.id} as failed (stuck during processing: {runtime})")
                
            elif not run.records_extracted and runtime > timedelta(minutes=5):
                # Pipeline has been stuck at startup
                run.status = "failed"
                run.error_message = f"Pipeline run stuck at startup for {runtime} - auto-cleaned up"
                run.completed_at = datetime.utcnow()
                cleanup_count += 1
                self.logger.info(f"Marked {pipeline_name} run {run.id} as failed (stuck at startup: {runtime})")
                
            else:
                # Run is recent and might still be legitimately running
                self.logger.info(f"Keeping {pipeline_name} run {run.id} (runtime: {runtime}, extracted: {run.records_extracted})")
        
        if cleanup_count > 0:
            self.db.commit()
            self.logger.info(f"Successfully cleaned up {cleanup_count} stuck pipeline runs")
        
        return cleanup_count
    
    def cleanup_on_startup(self) -> int:
        """
        Perform startup cleanup of all stuck pipeline runs
        This should be called when the server starts to clean up any runs
        that were left in 'running' state from previous server sessions
        
        Returns:
            Number of runs cleaned up
        """
        self.logger.info("Performing startup cleanup of stuck pipeline runs...")
        
        # On startup, we're more aggressive about cleanup since the server was restarted
        # Any run in 'running' status is likely orphaned
        stuck_runs = self.db.query(ETLPipelineRun).filter(
            ETLPipelineRun.status == 'running'
        ).all()
        
        if not stuck_runs:
            self.logger.info("No stuck pipeline runs found during startup cleanup")
            return 0
        
        cleanup_count = 0
        self.logger.info(f"Found {len(stuck_runs)} orphaned pipeline runs during startup cleanup")
        
        for run in stuck_runs:
            pipeline = self.db.query(ETLPipeline).filter(ETLPipeline.id == run.pipeline_id).first()
            pipeline_name = pipeline.name if pipeline else f"Pipeline {run.pipeline_id}"
            
            runtime = datetime.utcnow() - run.started_at if run.started_at else timedelta(0)
            
            # Check if pipeline appears to have completed successfully
            if run.records_loaded and run.records_loaded > 0:
                run.status = "completed"
                run.completed_at = datetime.utcnow()
                self.logger.info(f"Startup cleanup: Marked {pipeline_name} run {run.id} as completed")
            else:
                run.status = "failed"
                run.error_message = f"Pipeline run orphaned during server restart (runtime: {runtime})"
                run.completed_at = datetime.utcnow()
                self.logger.info(f"Startup cleanup: Marked {pipeline_name} run {run.id} as failed")
            
            cleanup_count += 1
        
        if cleanup_count > 0:
            self.db.commit()
            self.logger.info(f"Startup cleanup completed: {cleanup_count} orphaned runs cleaned up")
        
        return cleanup_count
    
    def get_running_pipelines_summary(self) -> List[dict]:
        """
        Get a summary of currently running pipelines for monitoring
        
        Returns:
            List of dictionaries with pipeline run information
        """
        running_runs = self.db.query(ETLPipelineRun).filter(
            ETLPipelineRun.status == 'running'
        ).all()
        
        summary = []
        for run in running_runs:
            pipeline = self.db.query(ETLPipeline).filter(ETLPipeline.id == run.pipeline_id).first()
            runtime = datetime.utcnow() - run.started_at if run.started_at else timedelta(0)
            
            summary.append({
                'run_id': run.id,
                'pipeline_id': run.pipeline_id,
                'pipeline_name': pipeline.name if pipeline else f"Pipeline {run.pipeline_id}",
                'started_at': run.started_at,
                'runtime': str(runtime),
                'records_extracted': run.records_extracted,
                'records_transformed': run.records_transformed,
                'records_loaded': run.records_loaded
            })
        
        return summary
