"""
Pipeline Factory for creating appropriate pipeline strategies based on configuration.
"""
import logging
from typing import Dict, List, Optional, Type
from sqlalchemy.orm import Session

from app.models.etl_pipeline import ETLPipeline
from app.etl.pipelines.pipeline_strategies.base_strategy import BasePipelineStrategy
from app.etl.pipelines.pipeline_strategies.salesforce_to_postgresql_strategy import SalesforceToPostgreSQLStrategy
from app.etl.pipelines.pipeline_strategies.api_to_postgresql_strategy import APIToPostgreSQLStrategy

# New split pipeline strategies
from app.etl.pipelines.pipeline_strategies.base_salesforce_strategy import BaseSalesforceStrategy
from app.etl.pipelines.pipeline_strategies.destination_pipeline import DestinationPipelineStrategy
from app.etl.pipelines.pipeline_strategies.hotel_pipeline import HotelPipelineStrategy
from app.etl.pipelines.pipeline_strategies.room_type_pipeline import RoomTypePipelineStrategy
from app.etl.pipelines.pipeline_strategies.room_pipeline import RoomPipelineStrategy


logger = logging.getLogger(__name__)


class PipelineTypeRegistry:
    """
    Registry for managing different pipeline strategy types.
    
    This registry allows for dynamic registration and discovery of pipeline strategies,
    making it easy to add new source/destination combinations without modifying core code.
    """
    
    def __init__(self):
        self._strategies: Dict[str, Type[BasePipelineStrategy]] = {}
        self._register_default_strategies()
    
    def _register_default_strategies(self):
        """Register the default built-in strategies"""
        # Legacy strategies
        self.register_strategy("salesforce_to_postgresql", SalesforceToPostgreSQLStrategy)
        self.register_strategy("api_to_postgresql", APIToPostgreSQLStrategy)

        # New split pipeline strategies
        self.register_strategy("destination_pipeline", DestinationPipelineStrategy)
        self.register_strategy("hotel_pipeline", HotelPipelineStrategy)
        self.register_strategy("room_type_pipeline", RoomTypePipelineStrategy)
        self.register_strategy("room_pipeline", RoomPipelineStrategy)
    
    def register_strategy(self, name: str, strategy_class: Type[BasePipelineStrategy]):
        """
        Register a new pipeline strategy.
        
        Args:
            name: Unique name for the strategy
            strategy_class: Strategy class that implements BasePipelineStrategy
        """
        if not issubclass(strategy_class, BasePipelineStrategy):
            raise ValueError(f"Strategy class must inherit from BasePipelineStrategy")
        
        self._strategies[name] = strategy_class
        logger.info(f"Registered pipeline strategy: {name}")
    
    def unregister_strategy(self, name: str):
        """
        Unregister a pipeline strategy.
        
        Args:
            name: Name of the strategy to unregister
        """
        if name in self._strategies:
            del self._strategies[name]
            logger.info(f"Unregistered pipeline strategy: {name}")
    
    def get_strategy_class(self, name: str) -> Optional[Type[BasePipelineStrategy]]:
        """
        Get a strategy class by name.
        
        Args:
            name: Name of the strategy
            
        Returns:
            Strategy class or None if not found
        """
        return self._strategies.get(name)
    
    def list_strategies(self) -> List[str]:
        """
        List all registered strategy names.
        
        Returns:
            List of strategy names
        """
        return list(self._strategies.keys())
    
    def find_compatible_strategies(self, source_type: str, destination_type: str, db_session: Session) -> List[str]:
        """
        Find strategies that support the given source and destination types.
        
        Args:
            source_type: Source system type
            destination_type: Destination system type
            db_session: Database session for creating strategy instances
            
        Returns:
            List of compatible strategy names
        """
        compatible = []
        
        for name, strategy_class in self._strategies.items():
            try:
                # Create temporary instance to check compatibility
                strategy = strategy_class(db_session)
                if (source_type in strategy.get_supported_source_types() and 
                    destination_type in strategy.get_supported_destination_types()):
                    compatible.append(name)
            except Exception as e:
                logger.warning(f"Error checking compatibility for strategy {name}: {e}")
        
        return compatible


# Global registry instance
pipeline_registry = PipelineTypeRegistry()


class PipelineFactory:
    """
    Factory for creating pipeline strategy instances.
    
    The factory uses the registry to find and instantiate the appropriate
    strategy for a given pipeline configuration.
    """
    
    def __init__(self, db_session: Session, registry: Optional[PipelineTypeRegistry] = None):
        """
        Initialize the pipeline factory.
        
        Args:
            db_session: Database session for strategy instances
            registry: Optional custom registry (uses global registry if None)
        """
        self.db_session = db_session
        self.registry = registry or pipeline_registry
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def create_strategy(self, pipeline: ETLPipeline) -> BasePipelineStrategy:
        """
        Create the appropriate strategy for the given pipeline.
        
        Args:
            pipeline: Pipeline configuration
            
        Returns:
            Strategy instance that can handle the pipeline
            
        Raises:
            ValueError: If no compatible strategy is found
        """
        # Find compatible strategies
        compatible_strategies = self.registry.find_compatible_strategies(
            pipeline.source_type,
            pipeline.destination_type,
            self.db_session
        )
        
        if not compatible_strategies:
            raise ValueError(
                f"No strategy found for source '{pipeline.source_type}' "
                f"to destination '{pipeline.destination_type}'"
            )
        
        # Use the first compatible strategy
        # In the future, this could be enhanced with priority/preference logic
        strategy_name = compatible_strategies[0]
        strategy_class = self.registry.get_strategy_class(strategy_name)
        
        if not strategy_class:
            raise ValueError(f"Strategy class not found for: {strategy_name}")
        
        # Create and validate strategy instance
        strategy = strategy_class(self.db_session)
        
        # Validate pipeline configuration with the strategy
        is_valid, errors = strategy.validate_configuration(pipeline)
        if not is_valid:
            raise ValueError(f"Pipeline configuration validation failed: {', '.join(errors)}")
        
        self.logger.info(f"Created strategy '{strategy_name}' for pipeline '{pipeline.name}'")
        return strategy
    
    def get_available_strategies(self) -> List[Dict[str, any]]:
        """
        Get information about all available strategies.
        
        Returns:
            List of strategy information dictionaries
        """
        strategies = []
        
        for name in self.registry.list_strategies():
            strategy_class = self.registry.get_strategy_class(name)
            if strategy_class:
                try:
                    # Create temporary instance to get metadata
                    temp_strategy = strategy_class(self.db_session)
                    strategies.append({
                        "name": name,
                        "strategy_name": temp_strategy.get_strategy_name(),
                        "supported_sources": temp_strategy.get_supported_source_types(),
                        "supported_destination": temp_strategy.get_supported_destination_types(),
                        "class_name": strategy_class.__name__
                    })
                except Exception as e:
                    self.logger.warning(f"Error getting metadata for strategy {name}: {e}")
        
        return strategies
    
    def validate_pipeline_configuration(self, pipeline: ETLPipeline) -> tuple[bool, List[str]]:
        """
        Validate a pipeline configuration without creating a strategy.
        
        Args:
            pipeline: Pipeline to validate
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        try:
            strategy = self.create_strategy(pipeline)
            return strategy.validate_configuration(pipeline)
        except ValueError as e:
            return False, [str(e)]
