"""
Base Salesforce to PostgreSQL pipeline strategy with common functionality.
"""
import json
import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional
from sqlalchemy.orm import Session

from .base_strategy import BasePipelineStrategy, PipelineExecutionResult
from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun
from app.etl.extractors.salesforce_extractor import SalesforceExtractor
from app.services.minio_service import minio_service
from app.services.websocket_manager import websocket_manager


class BaseSalesforceStrategy(BasePipelineStrategy):
    """
    Base strategy for Salesforce to PostgreSQL ETL pipelines.
    
    Contains common functionality for:
    - Salesforce authentication and data extraction
    - Raw data storage in MinIO
    - Data transformation based on field mappings
    - Processed data storage in MinIO
    - WebSocket progress notifications
    """
    
    def __init__(self, db_session: Session):
        super().__init__(db_session)
        self.salesforce_extractor = SalesforceExtractor()
    
    def get_strategy_name(self) -> str:
        return "base_salesforce"
    
    def get_supported_source_types(self) -> List[str]:
        return ["salesforce"]
    
    def get_supported_destination_types(self) -> List[str]:
        return ["postgresql"]
    
    def validate_configuration(self, pipeline: ETLPipeline) -> tuple[bool, List[str]]:
        """Validate Salesforce to PostgreSQL pipeline configuration"""
        errors = []
        
        # Validate source configuration
        if not pipeline.source_object:
            errors.append("Source object is required for Salesforce pipelines")
        
        if not pipeline.source_fields or len(pipeline.source_fields) == 0:
            errors.append("Source fields are required for Salesforce pipelines")
        
        # Validate destination configuration
        if not pipeline.destination_table:
            errors.append("Destination table is required for PostgreSQL pipelines")
        
        if not pipeline.destination_fields or len(pipeline.destination_fields) == 0:
            errors.append("Destination fields are required for PostgreSQL pipelines")
        
        return len(errors) == 0, errors
    
    def _safe_create_task(self, coro):
        """Safely create asyncio task without blocking"""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                asyncio.create_task(coro)
            else:
                loop.run_until_complete(coro)
        except Exception as e:
            self.logger.warning(f"Failed to send WebSocket message: {e}")
    
    def _log(self, run: ETLPipelineRun, level: str, stage: str, message: str):
        """Log message with consistent formatting"""
        timestamp = datetime.utcnow().isoformat()
        log_entry = {
            "timestamp": timestamp,
            "level": level,
            "stage": stage,
            "message": message
        }
        
        # Log to application logger
        if level == "ERROR":
            self.logger.error(f"[{stage}] {message}")
        elif level == "WARNING":
            self.logger.warning(f"[{stage}] {message}")
        else:
            self.logger.info(f"[{stage}] {message}")
        
        # Store in run logs if run is provided
        if run:
            if not run.logs:
                run.logs = []
            run.logs.append(log_entry)
    
    def _extract_data(self, pipeline: ETLPipeline, run: ETLPipelineRun) -> List[Dict[str, Any]]:
        """Extract data from Salesforce"""
        self._log(run, "INFO", "extract", f"Authenticating with Salesforce")
        
        # Authenticate with Salesforce
        self.salesforce_extractor.authenticate()
        
        # Extract data using the configured fields
        self._log(run, "INFO", "extract", f"Extracting {pipeline.source_object} records with fields: {pipeline.source_fields}")
        
        # Get limit from transformation config if specified
        limit = None
        if pipeline.transformation_config and isinstance(pipeline.transformation_config, dict):
            limit = pipeline.transformation_config.get("extraction_limit")
        
        raw_data = self.salesforce_extractor.extract_records(
            pipeline.source_object,
            pipeline.source_fields,
            limit=limit
        )
        
        self._log(run, "INFO", "extract", f"Successfully extracted {len(raw_data)} records from Salesforce")
        return raw_data
    
    def _store_raw_data(self, pipeline: ETLPipeline, run: ETLPipelineRun, data: List[Dict[str, Any]]) -> str:
        """Store raw data in MinIO"""
        self._log(run, "INFO", "store-raw", f"Storing {len(data)} raw records in MinIO")
        
        file_path = minio_service.store_raw_data(data, pipeline.name, run.id)
        run.raw_data_path = file_path
        self.db.commit()
        
        self._log(run, "INFO", "store-raw", f"Raw data stored at: {file_path}")
        return file_path
    
    def _transform_data(self, pipeline: ETLPipeline, run: ETLPipelineRun, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Transform data based on field mappings"""
        self._log(run, "INFO", "transform", f"Transforming {len(data)} records")
        
        transformed_data = []
        field_mappings = pipeline.destination_fields or {}
        transformation_config = pipeline.transformation_config or {}
        
        for record in data:
            transformed_record = {}
            
            # Apply field mappings
            for dest_field, source_field in field_mappings.items():
                if source_field in record:
                    transformed_record[dest_field] = record[source_field]
            
            # Apply field transformations if configured
            field_transformations = transformation_config.get("field_transformations", {})
            for field_name, transformation in field_transformations.items():
                if transformation.get("type") == "invert_boolean":
                    source_field = transformation.get("source_field")
                    if source_field in record:
                        transformed_record[field_name] = not record[source_field]
            
            # Add metadata
            transformed_record["extracted_at"] = datetime.utcnow().isoformat()
            transformed_record["pipeline_run_id"] = run.id
            
            transformed_data.append(transformed_record)
        
        self._log(run, "INFO", "transform", f"Successfully transformed {len(transformed_data)} records")
        return transformed_data
    
    def _store_processed_data(self, pipeline: ETLPipeline, run: ETLPipelineRun, data: List[Dict[str, Any]]) -> str:
        """Store processed data in MinIO"""
        self._log(run, "INFO", "store-processed", f"Storing {len(data)} processed records in MinIO")
        
        file_path = minio_service.store_processed_data(data, pipeline.name, run.id)
        run.processed_data_path = file_path
        self.db.commit()
        
        self._log(run, "INFO", "store-processed", f"Processed data stored at: {file_path}")
        return file_path
    
    def _execute_prerequisite_pipelines(self, pipeline: ETLPipeline, run: ETLPipelineRun) -> bool:
        """Execute prerequisite pipelines if needed"""
        # This can be overridden by specific pipeline strategies
        return True
    
    def execute(self, pipeline: ETLPipeline, run: ETLPipelineRun) -> PipelineExecutionResult:
        """Execute Salesforce to PostgreSQL ETL pipeline"""
        start_time = datetime.utcnow()
        result = PipelineExecutionResult(success=False, records_processed=0)
        
        try:
            self._log(run, "INFO", "pipeline", f"Starting Salesforce to PostgreSQL pipeline: {pipeline.name}")

            # Step 0: Execute prerequisite pipelines if needed
            if not self._execute_prerequisite_pipelines(pipeline, run):
                self._log(run, "ERROR", "pipeline", "Failed to execute prerequisite pipelines")
                result.error_message = "Failed to execute prerequisite pipelines"
                return result

            # Step 1: Extract data from Salesforce
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "extract", "running", 0, "Extracting data from Salesforce"
            ))
            
            raw_data = self._extract_data(pipeline, run)
            result.records_extracted = len(raw_data)
            
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "extract", "completed", 20, f"Extracted {len(raw_data)} records"
            ))
            
            # Step 2: Store raw data in MinIO
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "store-raw", "running", 20, "Storing raw data in MinIO"
            ))
            
            raw_data_path = self._store_raw_data(pipeline, run, raw_data)
            result.raw_data_path = raw_data_path
            
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "store-raw", "completed", 40, "Raw data stored successfully"
            ))
            
            # Step 3: Transform data
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "transform", "running", 40, "Applying transformation rules"
            ))
            
            transformed_data = self._transform_data(pipeline, run, raw_data)
            result.records_transformed = len(transformed_data)
            
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "transform", "completed", 60, f"Transformed {len(transformed_data)} records"
            ))
            
            # Step 4: Store processed data in MinIO
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "store-processed", "running", 60, "Storing processed data in MinIO"
            ))
            
            processed_data_path = self._store_processed_data(pipeline, run, transformed_data)
            result.processed_data_path = processed_data_path
            
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "store-processed", "completed", 80, "Processed data stored successfully"
            ))
            
            # Step 5: Load data to PostgreSQL (implemented by subclasses)
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "load", "running", 80, "Loading data to PostgreSQL"
            ))
            
            loaded_count = self._load_data_to_postgresql(pipeline, run, transformed_data)
            result.records_loaded = loaded_count
            result.records_processed = loaded_count
            
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "load", "completed", 100, f"Loaded {loaded_count} records to PostgreSQL"
            ))
            
            # Mark as successful
            result.success = True
            end_time = datetime.utcnow()
            result.execution_time = (end_time - start_time).total_seconds()
            
            self._log(run, "INFO", "pipeline", f"Pipeline completed successfully. Processed {result.records_processed} records")
            
            # Send completion notification
            self._safe_create_task(websocket_manager.send_pipeline_completion(
                pipeline.id, "completed", run.to_dict()
            ))
            
        except Exception as e:
            result.success = False
            result.error_message = str(e)
            end_time = datetime.utcnow()
            result.execution_time = (end_time - start_time).total_seconds()
            
            self.logger.error(f"Pipeline execution failed: {e}")
            self._log(run, "ERROR", "pipeline", f"Pipeline execution failed: {e}")
            
            # Send failure notification
            self._safe_create_task(websocket_manager.send_pipeline_completion(
                pipeline.id, "failed", run.to_dict()
            ))
        
        return result
    
    def _load_data_to_postgresql(self, pipeline: ETLPipeline, run: ETLPipelineRun, data: List[Dict[str, Any]]) -> int:
        """Load transformed data to PostgreSQL - to be implemented by subclasses"""
        raise NotImplementedError("Subclasses must implement _load_data_to_postgresql method")
