"""
Destination Pipeline Strategy - Resort Pipeline (01)
Handles extraction from Salesforce Resort__c and loading to destination table.
"""

import re
from datetime import datetime
from typing import Any, Dict, List
from sqlalchemy.orm import Session

from app.models.etl_pipeline import ETLPipelineRun
from app.models.destination import Destination
from app.models.id_generators import generate_destination_id
from .base_strategy import BasePipelineStrategy, PipelineExecutionResult


class DestinationPipelineStrategy(BasePipelineStrategy):
    """
    Strategy for Destination Pipeline that extracts from Salesforce Resort__c
    and loads to PostgreSQL destination table.

    This is the first pipeline in the sequence and creates destination records
    that other pipelines depend on.
    """

    def __init__(self, db_session: Session, logger=None):
        self.db = db_session
        self.logger = logger

    def get_strategy_name(self) -> str:
        """Return the name of this strategy"""
        return "destination_pipeline"

    def get_supported_source_types(self) -> List[str]:
        """Return supported source types"""
        return ["salesforce"]

    def get_supported_destination_types(self) -> List[str]:
        """Return supported destination types"""
        return ["postgresql"]

    def validate_configuration(self, pipeline) -> tuple[bool, List[str]]:
        """Validate pipeline configuration for destination pipeline"""
        errors = []

        # Check required fields
        if not pipeline.destination_table or pipeline.destination_table != "destination":
            errors.append("Destination table must be 'destination'")

        if not pipeline.source_object or pipeline.source_object != "Resort__c":
            errors.append("Source object must be 'Resort__c'")

        return len(errors) == 0, errors

    def _log(self, run: ETLPipelineRun, level: str, stage: str, message: str):
        """Log message with consistent formatting"""
        if self.logger:
            if level == "ERROR":
                self.logger.error(f"[{stage}] {message}")
            elif level == "WARNING":
                self.logger.warning(f"[{stage}] {message}")
            else:
                self.logger.info(f"[{stage}] {message}")

    def execute(self, run: ETLPipelineRun, data: List[Dict[str, Any]]) -> int:
        """Execute destination pipeline with ORM-based database operations"""
        self._log(
            run,
            "INFO",
            "destination-pipeline",
            f"Starting Destination Pipeline with {len(data)} records",
        )
        return self._load_to_destination_orm(run, data)

    def _load_to_destination_orm(
        self, run: ETLPipelineRun, data: List[Dict[str, Any]]
    ) -> int:
        """Load data to destination table"""
        loaded_count = 0

        self._log(
            run, "INFO", "load", f"Starting to load {len(data)} destination records"
        )

        for i, record in enumerate(data):
            try:
                # Log the record being processed for debugging
                self._log(
                    run,
                    "DEBUG",
                    "load",
                    f"Processing destination record {i+1}: {record}",
                )

                # Generate a unique handle from the name
                name = record.get("name", "")
                base_handle = (
                    name.lower().replace(" ", "-").replace("_", "-").replace("'", "")
                    if name
                    else f"destination-{i+1}"
                )

                # Create metadata for ETL tracking
                metadata = {
                    "migrated_id": record.get("migrated_id"),
                    "pipeline_run_id": run.id,
                    "source_system": record.get("source_system", "salesforce"),
                    "external_id": record.get("external_id"),
                    "source_created_at": record.get("source_created_at"),
                    "source_updated_at": record.get("source_updated_at"),
                    "extracted_at": record.get("extracted_at"),
                }

                # Check if destination with this ID already exists
                migrated_id = record.get("migrated_id")
                existing_destination = (
                    self.db.query(Destination)
                    .filter(Destination.id == migrated_id)
                    .first()
                )

                if existing_destination:
                    # Update existing record
                    country = record.get("country") or "Unknown"
                    existing_destination.name = name
                    existing_destination.description = record.get("description", "")
                    existing_destination.is_active = record.get("is_active", True)
                    existing_destination.country = country
                    existing_destination.currency = record.get("currency")
                    existing_destination.metadata_ = metadata

                    self._log(
                        run,
                        "INFO",
                        "load",
                        f"Updated existing destination: {name} (ID: {migrated_id})",
                    )

                else:
                    # Generate unique handle if needed
                    handle = base_handle
                    handle_counter = 1
                    while (
                        self.db.query(Destination)
                        .filter(Destination.handle == handle)
                        .first()
                    ):
                        handle = f"{base_handle}-{handle_counter}"
                        handle_counter += 1

                    # Create new destination record
                    # Ensure country is not null (required by powderbyrne schema)
                    country = record.get("country") or "Unknown"

                    destination = Destination(
                        id=migrated_id,
                        name=name,
                        handle=handle,
                        description=record.get("description", ""),
                        is_active=record.get("is_active", True),
                        country=country,
                        location=None,  # Not available in Salesforce data
                        tags=[],  # Empty array for now
                        website=None,  # Not available in Salesforce data
                        # is_featured=False,
                        # ai_content=None,
                        metadata_=metadata,  # Store ETL tracking info here
                        # internal_web_link=None,
                        # external_web_link=None,
                        # margin=None,
                        currency=record.get("currency", "GBP"),
                    )

                    self.db.add(destination)
                    self._log(
                        run,
                        "DEBUG",
                        "load",
                        f"Created new destination: {name} (Handle: {handle})",
                    )

                loaded_count += 1

                # Commit each record individually to avoid constraint violations
                try:
                    self.db.commit()
                except Exception as commit_error:
                    self._log(
                        run,
                        "ERROR",
                        "load",
                        f"Error committing record {i+1}: {commit_error}",
                    )
                    self.db.rollback()
                    continue

            except Exception as e:
                self._log(
                    run, "ERROR", "load", f"Error loading destination record {i+1}: {e}"
                )
                self._log(run, "ERROR", "load", f"Record data: {record}")
                # Rollback the current transaction and continue
                self.db.rollback()
                continue

        # All records committed individually
        self._log(
            run,
            "INFO",
            "load",
            f"Successfully processed {loaded_count} destination records (new and updated)",
        )

        return loaded_count
