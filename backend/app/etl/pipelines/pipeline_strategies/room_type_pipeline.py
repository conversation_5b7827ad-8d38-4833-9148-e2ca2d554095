"""
Room Type Pipeline Strategy - Room Type Pipeline (03)
Handles extraction from Salesforce Room_Type__c and loading to product table.
"""
import json
import re
from datetime import datetime
from typing import Any, Dict, List
from sqlalchemy.orm import Session
from sqlalchemy import text

from app.models.etl_pipeline import ETLPipelineRun
from app.models.hotel import Hotel
from .base_strategy import BasePipelineStrategy


class RoomTypePipelineStrategy(BasePipelineStrategy):
    """
    Strategy for Room Type Pipeline that extracts from Salesforce Room_Type__c
    and loads to PostgreSQL product table.

    This pipeline depends on the Hotel Pipeline and creates product records
    that represent room types.
    """

    def __init__(self, db_session: Session, logger=None):
        self.db = db_session
        self.logger = logger

    def get_strategy_name(self) -> str:
        """Return the name of this strategy"""
        return "room_type_pipeline"

    def get_supported_source_types(self) -> List[str]:
        """Return supported source types"""
        return ["salesforce"]

    def get_supported_destination_types(self) -> List[str]:
        """Return supported destination types"""
        return ["postgresql"]

    def validate_configuration(self, pipeline) -> tuple[bool, List[str]]:
        """Validate pipeline configuration for room type pipeline"""
        errors = []

        # Check required fields
        if not pipeline.destination_table or pipeline.destination_table != "product":
            errors.append("Destination table must be 'product'")

        if not pipeline.source_object or pipeline.source_object != "Room_Type__c":
            errors.append("Source object must be 'Room_Type__c'")

        return len(errors) == 0, errors

    def _log(self, run: ETLPipelineRun, level: str, stage: str, message: str):
        """Log message with consistent formatting"""
        if self.logger:
            if level == "ERROR":
                self.logger.error(f"[{stage}] {message}")
            elif level == "WARNING":
                self.logger.warning(f"[{stage}] {message}")
            else:
                self.logger.info(f"[{stage}] {message}")

    def _parse_datetime(self, datetime_str: str) -> datetime:
        """Parse datetime string safely"""
        if not datetime_str:
            return None
        try:
            from dateutil import parser
            return parser.parse(datetime_str)
        except Exception as e:
            self._log(None, "WARNING", "parse", f"Error parsing datetime '{datetime_str}': {e}")
            return None

    def _resolve_category_id(self, record: Dict[str, Any]) -> str:
        """
        Resolve Salesforce record data to product_category.id using multiple strategies

        This method maps Salesforce Room_Type__c data to existing product categories
        by looking up categories based on hotel information, handles, and other mapping criteria.
        """
        try:
            # Strategy 1: Direct category_id mapping if provided
            if record.get('category_id'):
                try:
                    # Verify the category exists in product_category table
                    result = self.db.execute(
                        text("SELECT id FROM product_category WHERE id = :category_id AND deleted_at IS NULL LIMIT 1"),
                        {"category_id": record['category_id']}
                    ).fetchone()
                    if result:
                        self._log(None, "DEBUG", "category-resolve", f"✅ Direct category mapping: {result[0]}")
                        return result[0]
                except Exception as e:
                    self._log(None, "WARNING", "category-resolve", f"⚠️ Strategy 1 failed (direct category_id): {e}")
                    # Rollback the transaction to clear the aborted state
                    self.db.rollback()

            # Strategy 2: Map by hotel_id to find hotel's assigned category
            hotel_id = record.get('hotel_id')
            if hotel_id:
                try:
                    result = self.db.execute(
                        text("""
                            SELECT pc.id, pc.handle, pc.name FROM product_category pc
                            JOIN hotel h ON h.category_id = pc.id
                            WHERE h.id = :hotel_id AND pc.deleted_at IS NULL
                            LIMIT 1
                        """),
                        {"hotel_id": hotel_id}
                    ).fetchone()
                    if result:
                        self._log(None, "DEBUG", "category-resolve", f"✅ Hotel category mapping: {hotel_id} -> {result[0]} (handle: {result[1]})")
                        return result[0]
                except Exception as e:
                    self._log(None, "WARNING", "category-resolve", f"⚠️ Strategy 2 failed (hotel_id): {e}")
                    # Rollback the transaction to clear the aborted state
                    self.db.rollback()

            # Strategy 3: Map by hotel_name to find hotel's assigned category
            hotel_name = record.get('hotel_name')
            if hotel_name:
                try:
                    result = self.db.execute(
                        text("""
                            SELECT pc.id, pc.handle, pc.name FROM product_category pc
                            JOIN hotel h ON h.category_id = pc.id
                            WHERE h.name = :hotel_name AND pc.deleted_at IS NULL
                            LIMIT 1
                        """),
                        {"hotel_name": hotel_name}
                    ).fetchone()
                    if result:
                        self._log(None, "DEBUG", "category-resolve", f"✅ Hotel name category mapping: '{hotel_name}' -> {result[0]} (handle: {result[1]})")
                        return result[0]
                except Exception as e:
                    self._log(None, "WARNING", "category-resolve", f"⚠️ Strategy 3 failed (hotel_name): {e}")
                    # Rollback the transaction to clear the aborted state
                    self.db.rollback()

            # Strategy 4: Generate handle from hotel name and match to category handle
            if hotel_name:
                try:
                    # Generate potential handles from hotel name
                    hotel_handle = self._generate_handle_from_text(hotel_name)

                    # Try exact handle match
                    result = self.db.execute(
                        text("""
                            SELECT id, handle, name FROM product_category
                            WHERE handle = :hotel_handle AND deleted_at IS NULL
                            LIMIT 1
                        """),
                        {"hotel_handle": hotel_handle}
                    ).fetchone()
                    if result:
                        self._log(None, "DEBUG", "category-resolve", f"✅ Handle exact match: '{hotel_handle}' -> {result[0]}")
                        return result[0]

                    # Try partial handle match
                    result = self.db.execute(
                        text("""
                            SELECT id, handle, name FROM product_category
                            WHERE handle LIKE :hotel_handle_pattern AND deleted_at IS NULL
                            ORDER BY LENGTH(handle) ASC
                            LIMIT 1
                        """),
                        {"hotel_handle_pattern": f"%{hotel_handle}%"}
                    ).fetchone()
                    if result:
                        self._log(None, "DEBUG", "category-resolve", f"✅ Handle partial match: '{hotel_handle}' -> {result[0]} (handle: {result[1]})")
                        return result[0]
                except Exception as e:
                    self._log(None, "WARNING", "category-resolve", f"⚠️ Strategy 4 failed (handle matching): {e}")
                    # Rollback the transaction to clear the aborted state
                    self.db.rollback()

            # Strategy 5: Look for room/accommodation categories by handle patterns
            try:
                result = self.db.execute(
                    text("""
                        SELECT id, handle, name FROM product_category
                        WHERE (handle LIKE '%room%' OR handle LIKE '%accommodation%' OR handle LIKE '%hotel%'
                               OR name ILIKE '%room%type%' OR name ILIKE '%accommodation%')
                        AND deleted_at IS NULL
                        ORDER BY rank ASC, created_at ASC
                        LIMIT 1
                    """)
                ).fetchone()
                if result:
                    self._log(None, "DEBUG", "category-resolve", f"✅ Default room category: {result[0]} (handle: {result[1]})")
                    return result[0]
            except Exception as e:
                self._log(None, "WARNING", "category-resolve", f"⚠️ Strategy 5 failed (room category patterns): {e}")
                # Rollback the transaction to clear the aborted state
                self.db.rollback()

            # Strategy 6: Use the highest-ranked active category as fallback
            try:
                result = self.db.execute(
                    text("""
                        SELECT id, handle, name FROM product_category
                        WHERE deleted_at IS NULL AND is_active = true
                        ORDER BY rank ASC, created_at ASC
                        LIMIT 1
                    """)
                ).fetchone()
                if result:
                    self._log(None, "WARNING", "category-resolve", f"⚠️ Fallback category: {result[0]} (handle: {result[1]})")
                    return result[0]
            except Exception as e:
                self._log(None, "WARNING", "category-resolve", f"⚠️ Strategy 6 failed (fallback category): {e}")
                # Rollback the transaction to clear the aborted state
                self.db.rollback()

            # No category found
            self._log(None, "ERROR", "category-resolve", f"❌ No category found for: hotel_id={hotel_id}, hotel_name='{hotel_name}'")
            return None

        except Exception as e:
            self._log(None, "ERROR", "category-resolve", f"❌ Error resolving category: {e}")
            # Rollback the transaction to clear any aborted state
            try:
                self.db.rollback()
            except Exception as rollback_error:
                self._log(None, "ERROR", "category-resolve", f"❌ Error during rollback: {rollback_error}")
            return None

    def _generate_handle_from_text(self, text: str) -> str:
        """Generate a URL-friendly handle from text"""
        if not text:
            return ""
        # Convert to lowercase, replace non-alphanumeric with hyphens, remove multiple hyphens
        handle = re.sub(r'[^a-z0-9]+', '-', text.lower().strip())
        handle = re.sub(r'-+', '-', handle).strip('-')
        return handle

    def execute(self, run: ETLPipelineRun, data: List[Dict[str, Any]]) -> int:
        """Execute room type pipeline with prerequisite checking"""
        self._log(run, "INFO", "room-type-pipeline", f"Starting Room Type Pipeline with {len(data)} records")

        # Check prerequisites
        hotel_count = self.db.query(Hotel).count()
        if hotel_count == 0:
            self._log(run, "ERROR", "prerequisites", "No hotel records found. Hotel Pipeline must run first.")
            return 0

        self._log(run, "INFO", "prerequisites", f"Found {hotel_count} hotel records. Prerequisites satisfied.")
        return self._load_to_product_orm(run, data)
    
    def _load_to_product_orm(self, run: ETLPipelineRun, data: List[Dict[str, Any]]) -> int:
        """Load data to ETL product table with correct schema"""
        loaded_count = 0
        error_count = 0
        category_resolved_count = 0
        category_missing_count = 0
        self._log(run, "INFO", "load", f"Starting to load {len(data)} records to product table")

        for i, record in enumerate(data):
            try:
                # Generate unique ID for product
                product_id = f"prod_{record.get('external_id', f'unknown_{i}')}"

                # Resolve category_id using the new resolution method
                resolved_category_id = self._resolve_category_id(record)

                # Generate handle from title for Medusa compatibility
                title = record.get('title', 'Untitled')
                handle = self._generate_handle_from_text(title)
                self._log(run, "DEBUG", "load", f"Generated handle: {handle} for title: {title}")
                handle = handle or f"prod-{product_id}" # Fallback if title is empty

                # Prepare metadata with all ETL-specific fields
                metadata = {
                    'migrated_id': record.get('external_id'),  # Salesforce Room_Type__c.Id
                    'hotel_id': record.get('hotel_id'),  # Original Salesforce hotel_id
                    'hotel_name': record.get('hotel_name'),  # Original Salesforce hotel_name
                    'category_id_original': record.get('category_id'),  # Original Salesforce category_id
                    'category_id_resolved': resolved_category_id,  # Resolved product_category.id
                    'pipeline_run_id': run.id,  # Track which pipeline run created this
                    'source_created_at': record.get('source_created_at'),  # Room_Type__c.CreatedDate
                    'source_updated_at': record.get('source_updated_at'),  # Room_Type__c.LastModifiedDate
                    'extracted_at': datetime.now().isoformat(),  # Extraction timestamp
                    'source_system': record.get('source_system', 'salesforce'),  # Source system
                    'is_active': record.get('is_active', True),  # !Room_Type__c.IsDeleted
                    'handle': handle  # Generated handle for reference
                }

                # Prepare the record for the actual Medusa product table schema
                product_record = {
                    'id': product_id,
                    'title': title,  # Room_Type__c.Name
                    'description': record.get('description'),  # Room_Type__c description field
                    'external_id': record.get('external_id'),  # Room_Type__c.Id
                    'subtitle': None,  # Not available from Salesforce
                    'is_giftcard': False,  # Default for room types
                    'status': 'published',  # Default status for new products
                    'thumbnail': None,  # Not available from Salesforce
                    'weight': None, 'length': None, 'height': None, 'width': None,  # Not available
                    'origin_country': None, 'hs_code': None, 'mid_code': None, 'material': None,  # Not available
                    'collection_id': None,  # Not directly mapped
                    'type_id': None,  # Not directly mapped
                    'discountable': True,  # Default for room types
                    'deleted_at': None,  # Active by default
                    'metadata': json.dumps(metadata),  # Store all ETL fields as JSON
                    'created_at': datetime.now(),
                    'updated_at': datetime.now(),
                    'handle': handle  # Add handle to the product_record
                }

                self._log(run, "DEBUG", "load", f"Product record to be inserted: {product_record}")

                # Insert into product table using Medusa schema
                insert_sql = text("""
                    INSERT INTO product (
                        id, title, description, external_id, subtitle, is_giftcard, status,
                        thumbnail, weight, length, height, width, origin_country,
                        hs_code, mid_code, material, collection_id, type_id,
                        discountable, deleted_at, metadata, created_at, updated_at, handle
                    )
                    VALUES (
                        :id, :title, :description, :external_id, :subtitle, :is_giftcard, :status,
                        :thumbnail, :weight, :length, :height, :width, :origin_country,
                        :hs_code, :mid_code, :material, :collection_id, :type_id,
                        :discountable, :deleted_at, :metadata, :created_at, :updated_at, :handle
                    )
                    ON CONFLICT (id) DO UPDATE SET
                        title = EXCLUDED.title,
                        description = EXCLUDED.description,
                        external_id = EXCLUDED.external_id,
                        subtitle = EXCLUDED.subtitle,
                        is_giftcard = EXCLUDED.is_giftcard,
                        status = EXCLUDED.status,
                        thumbnail = EXCLUDED.thumbnail,
                        weight = EXCLUDED.weight,
                        length = EXCLUDED.length,
                        height = EXCLUDED.height,
                        width = EXCLUDED.width,
                        origin_country = EXCLUDED.origin_country,
                        hs_code = EXCLUDED.hs_code,
                        mid_code = EXCLUDED.mid_code,
                        material = EXCLUDED.material,
                        collection_id = EXCLUDED.collection_id,
                        type_id = EXCLUDED.type_id,
                        discountable = EXCLUDED.discountable,
                        deleted_at = EXCLUDED.deleted_at,
                        metadata = EXCLUDED.metadata,
                        updated_at = EXCLUDED.updated_at,
                        handle = EXCLUDED.handle
                """)

                self.db.execute(insert_sql, product_record)

                # Create the many-to-many relationship in product_category_product junction table
                if resolved_category_id:
                    try:
                        junction_sql = text("""
                            INSERT INTO product_category_product (product_category_id, product_id)
                            VALUES (:product_category_id, :product_id)
                            ON CONFLICT (product_category_id, product_id) DO NOTHING
                        """)
                        self.db.execute(junction_sql, {
                            'product_category_id': resolved_category_id,
                            'product_id': product_id
                        })
                        category_resolved_count += 1
                        self._log(run, "DEBUG", "load", f"✅ Created product-category relationship: {product_id} -> {resolved_category_id}")
                    except Exception as e:
                        self._log(run, "ERROR", "load", f"❌ Failed to create product-category relationship for {product_id}: {e}")
                else:
                    category_missing_count += 1
                    self._log(run, "WARNING", "load", f"⚠️ No category resolved for product {product_id} (hotel: {record.get('hotel_name', 'unknown')})")

                loaded_count += 1

                # Commit every 50 records to avoid large transactions and prevent total data loss
                if loaded_count % 50 == 0:
                    self.db.commit()
                    self._log(run, "INFO", "load", f"Committed {loaded_count} product records")

                self._log(run, "DEBUG", "load", f"Loaded product record {i+1}: {product_id}")

            except Exception as e:
                error_count += 1
                self._log(run, "ERROR", "load", f"Failed to load product record {i+1}: {e}")
                self._log(run, "ERROR", "load", f"Record data: (id: {record.get('external_id')}, title: {record.get('title')})")
                # Continue processing without rolling back - individual record failure shouldn't affect others
                continue

        # Final commit for remaining records
        try:
            self.db.commit()
            self._log(run, "INFO", "load", f"Successfully loaded {loaded_count} records to product table")
            self._log(run, "INFO", "load", f"📊 Category Resolution Summary:")
            self._log(run, "INFO", "load", f"   ✅ Categories resolved: {category_resolved_count}")
            self._log(run, "INFO", "load", f"   ⚠️ Categories missing: {category_missing_count}")
            if error_count > 0:
                self._log(run, "WARNING", "load", f"Failed to load {error_count} records due to errors")
        except Exception as e:
            self._log(run, "ERROR", "load", f"Error committing final product records: {e}")
            self.db.rollback()

        return loaded_count
