"""
Hotel Pipeline Strategy - Hotel Pipeline (02)
Handles extraction from Salesforce Hotel__c and loading to hotel and product_category tables.
"""
import re
from datetime import datetime
from typing import Any, Dict, List
from sqlalchemy.orm import Session
from sqlalchemy import text

from app.models.etl_pipeline import ETLPipelineRun
from app.models.hotel import Hotel
from app.models.product_category import ProductCategory
from app.models.destination import Destination
from app.models.id_generators import generate_hotel_id, generate_product_category_id
from .base_strategy import BasePipelineStrategy, PipelineExecutionResult


class HotelPipelineStrategy(BasePipelineStrategy):
    """
    Strategy for Hotel Pipeline that extracts from Salesforce Hotel__c
    and loads to PostgreSQL hotel and product_category tables.

    This pipeline depends on the Destination Pipeline and creates hotel records
    with associated product categories.
    """

    def __init__(self, db_session: Session, logger=None):
        self.db = db_session
        self.logger = logger

    def get_strategy_name(self) -> str:
        """Return the name of this strategy"""
        return "hotel_pipeline"

    def get_supported_source_types(self) -> List[str]:
        """Return supported source types"""
        return ["salesforce"]

    def get_supported_destination_types(self) -> List[str]:
        """Return supported destination types"""
        return ["postgresql"]

    def validate_configuration(self, pipeline) -> tuple[bool, List[str]]:
        """Validate pipeline configuration for hotel pipeline"""
        errors = []

        # Check required fields
        if not pipeline.destination_table or pipeline.destination_table != "hotel":
            errors.append("Destination table must be 'hotel'")

        if not pipeline.source_object or pipeline.source_object != "Hotel__c":
            errors.append("Source object must be 'Hotel__c'")

        return len(errors) == 0, errors

    def _log(self, run: ETLPipelineRun, level: str, stage: str, message: str):
        """Log message with consistent formatting"""
        if self.logger:
            if level == "ERROR":
                self.logger.error(f"[{stage}] {message}")
            elif level == "WARNING":
                self.logger.warning(f"[{stage}] {message}")
            else:
                self.logger.info(f"[{stage}] {message}")

    def _parse_datetime(self, datetime_str: str) -> datetime:
        """Parse datetime string safely"""
        if not datetime_str:
            return None
        try:
            from dateutil import parser
            return parser.parse(datetime_str)
        except Exception as e:
            self._log(None, "WARNING", "parse", f"Error parsing datetime '{datetime_str}': {e}")
            return None

    def execute(self, run: ETLPipelineRun, data: List[Dict[str, Any]]) -> int:
        """Execute hotel pipeline with prerequisite checking"""
        self._log(run, "INFO", "hotel-pipeline", f"Starting Hotel Pipeline with {len(data)} records")

        # Check prerequisites
        destination_count = self.db.query(Destination).count()
        if destination_count == 0:
            self._log(run, "ERROR", "prerequisites", "No destination records found. Resort Pipeline must run first.")
            return 0

        self._log(run, "INFO", "prerequisites", f"Found {destination_count} destination records. Prerequisites satisfied.")
        return self._load_to_hotel_orm(run, data)
    
    def _load_to_hotel_orm(self, run: ETLPipelineRun, data: List[Dict[str, Any]]) -> int:
        """Load data to hotel table using dynamic schema detection"""
        loaded_count = 0
        skipped_count = 0
        updated_count = 0

        self._log(run, "INFO", "load", f"Starting to load {len(data)} hotel records with dynamic schema detection")

        if not data:
            self._log(run, "WARNING", "load", "No hotel data provided for loading")
            return 0

        # First, query the actual hotel table schema
        try:
            schema_result = self.db.execute(text("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_name = 'hotel'
                ORDER BY ordinal_position
            """))

            hotel_columns = {}
            for row in schema_result:
                hotel_columns[row[0]] = {
                    'type': row[1],
                    'nullable': row[2] == 'YES'
                }

            self._log(run, "INFO", "load", f"Detected hotel table columns: {list(hotel_columns.keys())}")

        except Exception as e:
            self._log(run, "ERROR", "load", f"Failed to query hotel table schema: {e}")
            return 0

        # Define field mapping from transformed data to database columns
        field_mapping = {
            'name': 'name',
            'description': 'description',
            'notes': 'description',  # Fallback mapping
            'is_active': 'is_active',
            'website': 'website',
            'email': 'email',
            'category_id': 'category_id',
            'destination_id': 'destination_id',
        }

        # Track all record processing for debugging
        processed_record_ids = []
        skipped_record_details = []

        for i, record in enumerate(data):
            try:
                # Extract name early for better logging
                name = record.get("name", "").strip() if record.get("name") else ""
                record_id = i + 1

                self._log(run, "INFO", "load", f"Starting processing hotel record {record_id}/{len(data)}: '{name}'")
                self._log(run, "DEBUG", "load", f"Record data: {record}")

                # Extract and validate required fields
                if not name:
                    skip_reason = "Missing hotel name"
                    self._log(run, "ERROR", "load", f"Hotel record {record_id} skipped - {skip_reason}")
                    skipped_record_details.append(f"Record {record_id}: {skip_reason}")
                    skipped_count += 1
                    continue

                # Generate handle if the column exists
                handle = None
                if 'handle' in hotel_columns:
                    base_handle = name.lower().replace(" ", "-").replace("_", "-").replace("'", "").replace("&", "and").replace(".", "")
                    import re
                    base_handle = re.sub(r'[^a-z0-9\-]', '', base_handle)
                    base_handle = re.sub(r'-+', '-', base_handle).strip('-')
                    handle = base_handle if base_handle else f"hotel-{i+1}"

                # Prepare data for insertion/update
                hotel_data = {}
                skipped_fields = []

                # Map transformed fields to database columns
                for source_field, target_column in field_mapping.items():
                    if target_column in hotel_columns:
                        value = record.get(source_field)
                        if value is not None:
                            hotel_data[target_column] = value
                    else:
                        if record.get(source_field) is not None:
                            skipped_fields.append(f"{source_field}->{target_column}")

                # Add handle if column exists
                if handle and 'handle' in hotel_columns:
                    hotel_data['handle'] = handle
                elif 'handle' in hotel_columns:
                    skipped_fields.append("handle")

                # Log skipped fields
                if skipped_fields:
                    self._log(run, "WARNING", "load", f"Skipped fields for hotel {name}: {skipped_fields}")

                # Handle destination relationship
                destination_id = None
                if 'destination_id' in hotel_columns:
                    resort_id = record.get("resort_id") or record.get("resort_migrated_id")
                    if resort_id:
                        destination = self.db.query(Destination).filter(Destination.id == resort_id).first()
                        if destination:
                            destination_id = destination.id
                            hotel_data['destination_id'] = destination_id
                            self._log(run, "DEBUG", "load", f"Found destination: {destination.name} for hotel: {name}")
                        else:
                            self._log(run, "WARNING", "load", f"No destination found for resort_id: {resort_id}")

                    # Check if destination_id is required
                    if not destination_id and not hotel_columns['destination_id']['nullable']:
                        # Find a default destination
                        default_destination = self.db.query(Destination).first()
                        if default_destination:
                            destination_id = default_destination.id
                            hotel_data['destination_id'] = destination_id
                            self._log(run, "WARNING", "load", f"Using default destination for hotel: {name}")
                        else:
                            self._log(run, "ERROR", "load", f"destination_id is required but no destinations available, skipping hotel: {name}")
                            skipped_count += 1
                            continue

                # Handle category relationship using raw SQL
                if 'category_id' in hotel_columns:
                    category_name = f"{name} Category"

                    # Check for existing category using raw SQL
                    existing_category_query = self.db.execute(text("SELECT id FROM product_category WHERE name = :name"), {"name": category_name})
                    existing_category_row = existing_category_query.fetchone()

                    if existing_category_row:
                        category_id = existing_category_row[0]
                        self._log(run, "DEBUG", "load", f"Using existing category: {category_name}")
                    else:
                        category_id = generate_product_category_id()
                        # Generate handle for category
                        base_category_handle = category_name.lower().replace(" ", "-").replace("_", "-").replace("'", "").replace("&", "and")
                        import re
                        base_category_handle = re.sub(r'[^a-z0-9\-]', '', base_category_handle)
                        base_category_handle = re.sub(r'-+', '-', base_category_handle).strip('-')

                        # Check for handle conflicts and generate unique handle
                        category_handle = base_category_handle
                        handle_counter = 1
                        while self.db.execute(text("SELECT 1 FROM product_category WHERE handle = :handle"), {"handle": category_handle}).fetchone():
                            category_handle = f"{base_category_handle}-{handle_counter}"
                            handle_counter += 1
                            if handle_counter > 100:  # Safety limit
                                self._log(run, "ERROR", "load", f"Unable to generate unique category handle after 100 attempts for: {category_name}")
                                break

                        if handle_counter <= 100:  # Only proceed if we have a unique handle
                            # Insert new category using raw SQL with handle and mpath
                            self.db.execute(text("INSERT INTO product_category (id, name, handle, mpath) VALUES (:id, :name, :handle, :mpath)"),
                                          {"id": category_id, "name": category_name, "handle": category_handle, "mpath": category_id})
                            self._log(run, "DEBUG", "load", f"Created new category: {category_name} (Handle: {category_handle})")
                        else:
                            # Fallback: use existing category with similar handle
                            existing_similar_category = self.db.execute(text("SELECT id FROM product_category WHERE handle LIKE :pattern LIMIT 1"),
                                                                      {"pattern": f"{base_category_handle}%"}).fetchone()
                            if existing_similar_category:
                                category_id = existing_similar_category[0]
                                self._log(run, "WARNING", "load", f"Using existing similar category for: {category_name}")
                            else:
                                self._log(run, "ERROR", "load", f"Failed to create or find category for: {category_name}")
                                continue

                    hotel_data['category_id'] = category_id

                # Set default values for required boolean fields
                if 'is_featured' in hotel_columns and not hotel_columns['is_featured']['nullable']:
                    hotel_data.setdefault('is_featured', False)
                if 'is_pets_allowed' in hotel_columns and not hotel_columns['is_pets_allowed']['nullable']:
                    hotel_data.setdefault('is_pets_allowed', False)

                # Check for existing hotel (upsert logic)
                existing_hotel_query = self.db.execute(text("SELECT id FROM hotel WHERE name = :name"), {"name": name})
                existing_hotel_row = existing_hotel_query.fetchone()

                # Track if we performed a database operation
                db_operation_performed = False
                operation_type = ""

                if existing_hotel_row:
                    # Update existing hotel using raw SQL
                    hotel_id = existing_hotel_row[0]
                    if hotel_data:
                        set_clauses = [f"{col} = :{col}" for col in hotel_data.keys()]
                        update_sql = f"UPDATE hotel SET {', '.join(set_clauses)} WHERE id = :hotel_id"
                        hotel_data['hotel_id'] = hotel_id
                        self.db.execute(text(update_sql), hotel_data)
                        updated_count += 1
                        db_operation_performed = True
                        operation_type = "UPDATE"
                        processed_record_ids.append(record_id)
                        self._log(run, "INFO", "load", f"Updated existing hotel: {name} (ID: {hotel_id})")
                    else:
                        self._log(run, "WARNING", "load", f"No data to update for existing hotel: {name}")
                        processed_record_ids.append(record_id)  # Still count as processed
                else:
                    # Generate unique hotel ID
                    hotel_id = generate_hotel_id()

                    # Check for ID conflicts and regenerate if needed
                    id_conflict_counter = 0
                    while self.db.execute(text("SELECT 1 FROM hotel WHERE id = :id"), {"id": hotel_id}).fetchone():
                        hotel_id = generate_hotel_id()
                        id_conflict_counter += 1
                        if id_conflict_counter > 10:  # Safety limit
                            skip_reason = f"Unable to generate unique hotel ID after 10 attempts"
                            self._log(run, "ERROR", "load", f"Hotel record {record_id} skipped - {skip_reason}: {name}")
                            skipped_record_details.append(f"Record {record_id} ({name}): {skip_reason}")
                            skipped_count += 1
                            continue  # Skip to next record

                    # Add generated ID to hotel data
                    hotel_data['id'] = hotel_id

                    # Insert new hotel using raw SQL with generated string ID
                    if hotel_data:
                        columns = list(hotel_data.keys())
                        placeholders = [f":{col}" for col in columns]
                        insert_sql = f"INSERT INTO hotel ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
                        self.db.execute(text(insert_sql), hotel_data)
                        loaded_count += 1
                        db_operation_performed = True
                        operation_type = "INSERT"
                        processed_record_ids.append(record_id)
                        self._log(run, "DEBUG", "load", f"Created new hotel: {name} (ID: {hotel_id})")
                    else:
                        skip_reason = "No data to insert for new hotel"
                        self._log(run, "WARNING", "load", f"Hotel record {record_id} skipped - {skip_reason}: {name}")
                        skipped_record_details.append(f"Record {record_id} ({name}): {skip_reason}")
                        skipped_count += 1
                        continue

                # Commit each record individually only if we performed a database operation
                if db_operation_performed:
                    try:
                        self.db.commit()
                        self._log(run, "DEBUG", "load", f"Successfully committed {operation_type} for hotel: {name}")
                    except Exception as commit_error:
                        skip_reason = f"Database commit failed: {commit_error}"
                        self._log(run, "ERROR", "load", f"Hotel record {record_id} skipped - {skip_reason} ({operation_type}) for {name}")
                        skipped_record_details.append(f"Record {record_id} ({name}): {skip_reason}")
                        self.db.rollback()
                        # Adjust counters since the operation failed
                        if operation_type == "INSERT":
                            loaded_count -= 1
                        elif operation_type == "UPDATE":
                            updated_count -= 1
                        # Remove from processed list if it was added
                        if record_id in processed_record_ids:
                            processed_record_ids.remove(record_id)
                        skipped_count += 1
                        continue

            except Exception as e:
                skip_reason = f"Processing error: {e}"
                self._log(run, "ERROR", "load", f"Hotel record {record_id} skipped - {skip_reason} (Name: {name})")
                skipped_record_details.append(f"Record {record_id} ({name}): {skip_reason}")
                self._log(run, "ERROR", "load", f"Record data: {record}")
                self.db.rollback()
                skipped_count += 1
                continue

        total_processed = loaded_count + updated_count

        # Detailed debugging information
        self._log(run, "INFO", "load", f"Hotel loading completed - New: {loaded_count}, Updated: {updated_count}, Skipped: {skipped_count}, Total Processed: {total_processed}")
        self._log(run, "INFO", "load", f"Processed record IDs: {sorted(processed_record_ids)}")

        # Identify missing records
        expected_record_ids = set(range(1, len(data) + 1))
        processed_record_ids_set = set(processed_record_ids)
        missing_record_ids = expected_record_ids - processed_record_ids_set

        if missing_record_ids:
            self._log(run, "ERROR", "load", f"Missing record IDs: {sorted(missing_record_ids)}")
            for missing_id in sorted(missing_record_ids):
                if missing_id <= len(data):
                    missing_record = data[missing_id - 1]
                    missing_name = missing_record.get("name", "Unknown")
                    self._log(run, "ERROR", "load", f"Missing record {missing_id}: {missing_name}")

        # Log all skipped records with reasons
        if skipped_record_details:
            self._log(run, "ERROR", "load", f"Skipped records details:")
            for detail in skipped_record_details:
                self._log(run, "ERROR", "load", f"  - {detail}")

        # Additional verification logging
        if total_processed != len(data):
            missing_count = len(data) - total_processed
            self._log(run, "WARNING", "load", f"Data loss detected: {missing_count} records were not processed (Expected: {len(data)}, Processed: {total_processed})")
        else:
            self._log(run, "INFO", "load", f"SUCCESS: All {len(data)} records processed successfully!")

        return total_processed
