"""
Room Pipeline Strategy - Room Pipeline (04)
Handles extraction from Salesforce Room__c and loading to product_variant table.
"""
from datetime import datetime
from typing import Any, Dict, List
from sqlalchemy.orm import Session
from sqlalchemy import text # Added import for 'text'

from app.models.etl_pipeline import ETLPipelineRun
from app.models.product import Product
from .base_strategy import BasePipelineStrategy


class RoomPipelineStrategy(BasePipelineStrategy):
    """
    Strategy for Room Pipeline that extracts from Salesforce Room__c
    and loads to PostgreSQL product_variant table.

    This pipeline depends on the Room Type Pipeline and creates product variant records
    that represent individual rooms.
    """

    def __init__(self, db_session: Session, logger=None):
        self.db = db_session
        self.logger = logger

    def get_strategy_name(self) -> str:
        """Return the name of this strategy"""
        return "room_pipeline"

    def get_supported_source_types(self) -> List[str]:
        """Return supported source types"""
        return ["salesforce"]

    def get_supported_destination_types(self) -> List[str]:
        """Return supported destination types"""
        return ["postgresql"]

    def validate_configuration(self, pipeline) -> tuple[bool, List[str]]:
        """Validate pipeline configuration for room pipeline"""
        errors = []

        # Check required fields
        if not pipeline.destination_table or pipeline.destination_table != "product_variant":
            errors.append("Destination table must be 'product_variant'")

        if not pipeline.source_object or pipeline.source_object != "Room__c":
            errors.append("Source object must be 'Room__c'")

        return len(errors) == 0, errors

    def _log(self, run: ETLPipelineRun, level: str, stage: str, message: str):
        """Log message with consistent formatting"""
        if self.logger:
            if level == "ERROR":
                self.logger.error(f"[{stage}] {message}")
            elif level == "WARNING":
                self.logger.warning(f"[{stage}] {message}")
            else:
                self.logger.info(f"[{stage}] {message}")

    def _parse_datetime(self, datetime_str: str) -> datetime:
        """Parse datetime string safely"""
        if not datetime_str:
            return None
        try:
            from dateutil import parser
            return parser.parse(datetime_str)
        except Exception as e:
            self._log(None, "WARNING", "parse", f"Error parsing datetime '{datetime_str}': {e}")
            return None


    def execute(self, run: ETLPipelineRun, data: List[Dict[str, Any]]) -> int:
        """Execute room pipeline with prerequisite checking"""
        self._log(run, "INFO", "room-pipeline", f"Starting Room Pipeline with {len(data)} records")

        # Check prerequisites
        product_count = self.db.query(Product).count()
        if product_count == 0:
            self._log(run, "ERROR", "prerequisites", "No product records found. Room Type Pipeline must run first.")
            return 0

        self._log(run, "INFO", "prerequisites", f"Found {product_count} product records. Prerequisites satisfied.")
        return self._load_to_product_variant_orm(run, data)
    
    def _load_to_product_variant_orm(self, run: ETLPipelineRun, data: List[Dict[str, Any]]) -> int:
        """Load data to ETL product_variant table with correct schema"""
        loaded_count = 0
        error_count = 0
        missing_product_count = 0
        self._log(run, "INFO", "load", f"Starting to load {len(data)} records to product_variant table")

        for i, record in enumerate(data):
            try:
                # Generate unique ID for product variant
                variant_id = f"pvar_{record.get('external_id', f'unknown_{i}')}"

                # Resolve product_id from Room_Type__c
                product_id = self._resolve_product_id_for_variant(record.get('product_id'))

                # Skip if product_id cannot be resolved
                if not product_id:
                    missing_product_count += 1
                    self._log(run, "WARNING", "load", f"Skipping variant {variant_id} - no product_id found for Room_Type__c: {record.get('product_id')}")
                    continue

                # Prepare the record for the actual product_variant table schema
                variant_record = {
                    'id': variant_id,
                    'migrated_id': record.get('external_id'),  # Salesforce Room__c.Id (required, unique)
                    'name': record.get('name', 'Untitled Room'),  # Room__c.Name (required)
                    'product_id': product_id,  # FK to product.id (Room_Type__c relationship)
                    'room_config_name': record.get('room_config_name'),  # Room-specific config
                    'is_connected': record.get('is_connected', False),  # Room connection status
                    'pipeline_run_id': run.id,  # Track which pipeline run created this (required)
                    'source_created_at': self._parse_datetime(record.get('source_created_at')),  # Room__c.CreatedDate
                    'source_updated_at': self._parse_datetime(record.get('source_updated_at')),  # Room__c.LastModifiedDate
                    'extracted_at': datetime.now(),  # Extraction timestamp (required)
                    'source_system': 'salesforce',  # Source system (required)
                    'external_id': record.get('external_id'),  # Room__c.Id
                    'is_active': record.get('is_active', True),  # !Room__c.IsDeleted (required)
                    'created_at': datetime.now(),
                    'updated_at': datetime.now()
                }

                # Insert into product_variant table using correct schema
                insert_sql = text("""
                    INSERT INTO product_variant (
                        id, migrated_id, name, product_id, room_config_name, is_connected,
                        pipeline_run_id, source_created_at, source_updated_at, extracted_at,
                        source_system, external_id, is_active, created_at, updated_at
                    )
                    VALUES (
                        :id, :migrated_id, :name, :product_id, :room_config_name, :is_connected,
                        :pipeline_run_id, :source_created_at, :source_updated_at, :extracted_at,
                        :source_system, :external_id, :is_active, :created_at, :updated_at
                    )
                    ON CONFLICT (migrated_id) DO UPDATE SET
                        name = EXCLUDED.name,
                        product_id = EXCLUDED.product_id,
                        room_config_name = EXCLUDED.room_config_name,
                        is_connected = EXCLUDED.is_connected,
                        source_updated_at = EXCLUDED.source_updated_at,
                        is_active = EXCLUDED.is_active,
                        updated_at = EXCLUDED.updated_at
                """)

                self.db.execute(insert_sql, variant_record)
                loaded_count += 1

                # Commit every 50 records to avoid large transactions and prevent total data loss
                if loaded_count % 50 == 0:
                    self.db.commit()
                    self._log(run, "INFO", "load", f"Committed {loaded_count} product_variant records")

                self._log(run, "DEBUG", "load", f"Loaded product_variant record {i+1}: {variant_id} -> product: {product_id}")

            except Exception as e:
                error_count += 1
                self._log(run, "ERROR", "load", f"Failed to load product_variant record {i+1}: {e}")
                self._log(run, "ERROR", "load", f"Record data: (id: {record.get('external_id')}, product_id: {record.get('product_id')})") # Modified to log only non-sensitive data
                # Continue processing without rolling back - individual record failure shouldn't affect others
                continue

        # Final commit for remaining records
        try:
            self.db.commit()
            self._log(run, "INFO", "load", f"Successfully loaded {loaded_count} records to product_variant table")
            if error_count > 0:
                self._log(run, "WARNING", "load", f"Failed to load {error_count} records due to errors")
            if missing_product_count > 0:
                self._log(run, "WARNING", "load", f"Skipped {missing_product_count} records due to missing product references")
        except Exception as e:
            self._log(run, "ERROR", "load", f"Error committing final product_variant records: {e}")
            self.db.rollback()

        return loaded_count

    def _resolve_product_id_for_variant(self, room_type_id: str) -> str:
        """Resolve Room_Type__c ID to product ID with improved error handling"""
        if not room_type_id:
            return None

        try:
            # Look up the product by its 'id' (prod_ prefixed) or 'migrated_id' field
            # The Salesforce Room_Type__c ID is stored in product.migrated_id or external_id
            result = self.db.execute(
                text("""
                    SELECT id FROM product
                    WHERE id = :room_type_id_prod_prefix
                       OR migrated_id = :room_type_id
                       OR external_id = :room_type_id
                    LIMIT 1
                """),
                {
                    "room_type_id_prod_prefix": f"prod_{room_type_id}" if not room_type_id.startswith("prod_") else room_type_id,
                    "room_type_id": room_type_id
                }
            ).fetchone()

            if result:
                product_id = result[0]
                if self.logger:
                    self.logger.debug(f"Resolved Room_Type__c {room_type_id} to product ID: {product_id}")
                return product_id
            else:
                if self.logger:
                    self.logger.warning(f"Product not found for Room_Type__c {room_type_id}. Returning None.")
                return None

        except Exception as e:
            if self.logger:
                self.logger.error(f"Database error resolving product ID for Room_Type__c {room_type_id}: {e}")
            return None
