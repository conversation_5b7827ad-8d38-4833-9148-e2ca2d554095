"""
Pipeline strategies for different source/destination combinations.
"""
from .base_strategy import BasePipelineStrategy, PipelineExecutionResult

# Individual pipeline strategies (imported on demand to avoid dependencies)
# from .destination_pipeline import DestinationPipelineStrategy
# from .hotel_pipeline import HotelPipelineStrategy
# from .room_type_pipeline import RoomTypePipelineStrategy
# from .room_pipeline import RoomPipelineStrategy

# Main strategies (imported on demand to avoid Salesforce dependency)
# from .salesforce_to_postgresql_strategy import SalesforceToPostgreSQLStrategy
# from .api_to_postgresql_strategy import APIToPostgreSQLStrategy

__all__ = [
    "BasePipelineStrategy",
    "PipelineExecutionResult",
    # Individual strategies available for import
    "DestinationPipelineStrategy",
    "HotelPipelineStrategy",
    "RoomTypePipelineStrategy",
    "RoomPipelineStrategy",
    # Main strategies available for import
    "SalesforceToPostgreSQLStrategy",
    "APIToPostgreSQLStrategy"
]
