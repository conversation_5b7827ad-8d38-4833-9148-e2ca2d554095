"""
Salesforce to PostgreSQL pipeline strategy implementation.

This is the main orchestrator that delegates to individual pipeline strategies:
- DestinationPipelineStrategy (01): Resort → destination table
- HotelPipelineStrategy (02): Hotel → hotel + product_category tables
- RoomTypePipelineStrategy (03): Room_Type → product table
- RoomPipelineStrategy (04): Room → product_variant table
"""
from datetime import datetime
from typing import Any, Dict, List
from sqlalchemy.orm import Session

from .base_strategy import BasePipelineStrategy, PipelineExecutionResult
from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun
from app.etl.extractors.salesforce_extractor import SalesforceExtractor
from app.services.minio_service import minio_service
from app.services.websocket_manager import websocket_manager

# Import individual pipeline strategies
from .destination_pipeline import DestinationPipelineStrategy
from .hotel_pipeline import HotelPipelineStrategy
from .room_type_pipeline import RoomTypePipelineStrategy
from .room_pipeline import RoomPipelineStrategy


class SalesforceToPostgreSQLStrategy(BasePipelineStrategy):
    """
    Main orchestrator strategy for Salesforce to PostgreSQL ETL pipelines.

    This strategy orchestrates the complete ETL process:
    - Salesforce data extraction
    - Raw data storage in MinIO
    - Data transformation based on field mappings
    - Processed data storage in MinIO
    - Delegation to specialized pipeline strategies for database loading

    The actual database loading is delegated to individual pipeline strategies:
    - DestinationPipelineStrategy: Resort__c → destination table
    - HotelPipelineStrategy: Hotel__c → hotel + product_category tables
    - RoomTypePipelineStrategy: Room_Type__c → product table
    - RoomPipelineStrategy: Room__c → product_variant table
    """

    def __init__(self, db_session: Session):
        super().__init__(db_session)
        self.salesforce_extractor = SalesforceExtractor()

        # Initialize individual pipeline strategies for delegation
        self.destination_pipeline = DestinationPipelineStrategy(db_session, self.logger)
        self.hotel_pipeline = HotelPipelineStrategy(db_session, self.logger)
        self.room_type_pipeline = RoomTypePipelineStrategy(db_session, self.logger)
        self.room_pipeline = RoomPipelineStrategy(db_session, self.logger)
    
    def get_strategy_name(self) -> str:
        return "salesforce_to_postgresql"
    
    def get_supported_source_types(self) -> List[str]:
        return ["salesforce"]
    
    def get_supported_destination_types(self) -> List[str]:
        return ["postgresql"]
    
    def validate_configuration(self, pipeline: ETLPipeline) -> tuple[bool, List[str]]:
        """Validate Salesforce to PostgreSQL pipeline configuration"""
        errors = []
        
        # Validate source configuration
        if not pipeline.source_object:
            errors.append("Source object is required for Salesforce pipelines")
        
        if not pipeline.source_fields or len(pipeline.source_fields) == 0:
            errors.append("Source fields are required for Salesforce pipelines")
        
        # Validate destination configuration
        if not pipeline.destination_table:
            errors.append("Destination table is required for PostgreSQL pipelines")
        
        if not pipeline.destination_fields or len(pipeline.destination_fields) == 0:
            errors.append("Destination field mappings are required")
        
        # Validate field mappings
        if pipeline.destination_fields:
            for target_field, source_field in pipeline.destination_fields.items():
                if not target_field or not source_field:
                    errors.append(f"Invalid field mapping: {target_field} -> {source_field}")
        
        return len(errors) == 0, errors

    def _execute_prerequisite_pipelines(self, pipeline: ETLPipeline, run: ETLPipelineRun) -> bool:
        """Execute any prerequisite pipelines if needed"""
        # For now, no prerequisite pipelines are required
        # This can be extended in the future if needed
        return True

    def _log(self, run: ETLPipelineRun, level: str, stage: str, message: str):
        """Log a message and add it to the run logs"""
        if self.logger:
            if level == "ERROR":
                self.logger.error(f"[{stage}] {message}")
            elif level == "WARNING":
                self.logger.warning(f"[{stage}] {message}")
            elif level == "DEBUG":
                self.logger.debug(f"[{stage}] {message}")
            else:
                self.logger.info(f"[{stage}] {message}")

        # Add to run logs
        if run and hasattr(run, 'logs'):
            import json
            try:
                logs = json.loads(run.logs) if run.logs else []
            except (json.JSONDecodeError, TypeError):
                logs = []

            logs.append({
                "timestamp": datetime.now().isoformat(),
                "level": level,
                "stage": stage,
                "message": message
            })

            run.logs = json.dumps(logs)

    def execute(self, pipeline: ETLPipeline, run: ETLPipelineRun) -> PipelineExecutionResult:
        """Execute Salesforce to PostgreSQL ETL pipeline"""
        start_time = datetime.utcnow()
        result = PipelineExecutionResult(success=False, records_processed=0)

        print("Executing Salesforce to PostgreSQL pipeline...")
        
        try:
            self._log(run, "INFO", "pipeline", f"Starting Salesforce to PostgreSQL pipeline: {pipeline.name}")

            # Step 0: Execute prerequisite pipelines if needed
            if not self._execute_prerequisite_pipelines(pipeline, run):
                self._log(run, "ERROR", "pipeline", "Failed to execute prerequisite pipelines")
                result.error_message = "Failed to execute prerequisite pipelines"
                return result

            # Step 1: Extract data from Salesforce
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "extract", "running", 0, "Extracting data from Salesforce"
            ))
            
            raw_data = self._extract_data(pipeline, run)
            print(f"Extracted {len(raw_data)} records from Salesforce")
            result.records_extracted = len(raw_data)
            
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "extract", "completed", 20, f"Extracted {len(raw_data)} records"
            ))
            
            print(f"Storing {len(raw_data)} raw records in MinIO...")
            # Step 2: Store raw data in MinIO
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "store-raw", "running", 20, "Storing raw data in MinIO"
            ))
            
            raw_data_path = self._store_raw_data(pipeline, run, raw_data)
            result.raw_data_path = raw_data_path
            
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "store-raw", "completed", 40, "Raw data stored successfully"
            ))
            
            print(f"Transforming {len(raw_data)} records...")
            # Step 3: Transform data
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "transform", "running", 40, "Applying transformation rules"
            ))
            
            transformed_data = self._transform_data(pipeline, run, raw_data)
            print(f"Transformed {len(transformed_data)} records")
            result.records_transformed = len(transformed_data)
            
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "transform", "completed", 60, f"Transformed {len(transformed_data)} records"
            ))
            
            # Step 4: Store processed data in MinIO
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "store-processed", "running", 60, "Storing processed data in MinIO"
            ))
            
            processed_data_path = self._store_processed_data(pipeline, run, transformed_data)
            result.processed_data_path = processed_data_path
            
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "store-processed", "completed", 80, "Processed data stored successfully"
            ))
            
            print("Loading Start data to PostgreSQL...")
            # Step 5: Load data to PostgreSQL
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "load", "running", 80, "Loading data to PostgreSQL"
            ))
            print("Loading data to PostgreSQL...")
            loaded_count = self._load_data_to_postgresql(pipeline, run, transformed_data)
            result.records_loaded = loaded_count
            result.records_processed = loaded_count
            
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "load", "completed", 100, f"Loaded {loaded_count} records to PostgreSQL"
            ))
            
            # Mark as successful
            result.success = True
            end_time = datetime.utcnow()
            result.execution_time = (end_time - start_time).total_seconds()
            
            self._log(run, "INFO", "pipeline", f"Pipeline completed successfully. Processed {result.records_processed} records")
            
            # Send completion notification
            self._safe_create_task(websocket_manager.send_pipeline_completion(
                pipeline.id, "completed", run.to_dict()
            ))
            
        except Exception as e:
            result.success = False
            result.error_message = str(e)
            end_time = datetime.utcnow()
            result.execution_time = (end_time - start_time).total_seconds()
            
            self.logger.error(f"Pipeline execution failed: {e}")
            self._log(run, "ERROR", "pipeline", f"Pipeline execution failed: {e}")
            
            # Send failure notification
            self._safe_create_task(websocket_manager.send_pipeline_completion(
                pipeline.id, "failed", run.to_dict()
            ))
            
            raise
        
        return result

    def _extract_data(self, pipeline: ETLPipeline, run: ETLPipelineRun) -> List[Dict[str, Any]]:
        """Extract data from Salesforce"""
        # Authenticate with Salesforce
        if not self.salesforce_extractor.authenticate():
            raise ValueError("Failed to authenticate with Salesforce")

        # Extract data
        data = self.salesforce_extractor.extract_records(
            object_name=pipeline.source_object,
            fields=pipeline.source_fields or [],
            limit=None  # Extract all records
        )

        run.records_extracted = len(data)
        self.db.commit()  # Ensure extraction count is persisted
        return data

    def _store_raw_data(self, pipeline: ETLPipeline, run: ETLPipelineRun, data: List[Dict[str, Any]]) -> str:
        """Store raw data in MinIO"""
        self._log(run, "INFO", "store-raw", f"Storing {len(data)} raw records in MinIO")

        # Store using the MinIO service's store_raw_data method
        file_path = minio_service.store_raw_data(data, pipeline.name, run.id)

        self._log(run, "INFO", "store-raw", f"Raw data stored at: {file_path}")
        return file_path

    def _transform_data(self, pipeline: ETLPipeline, run: ETLPipelineRun, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Transform data according to pipeline configuration"""
        self._log(run, "INFO", "transform", "Starting data transformation")
        self._log(run, "INFO", "transform", f"Input data: {len(data)} records")
        self._log(run, "DEBUG", "transform", f"Field mappings: {pipeline.destination_fields}")
        self._log(run, "DEBUG", "transform", f"Transformation config: {pipeline.transformation_config}")

        transformed_data = []
        transformation_config = pipeline.transformation_config or {}
        field_mappings = pipeline.destination_fields or {}

        # Get field transformations configuration
        field_transformations = transformation_config.get("field_transformations", {})

        for i, record in enumerate(data):
            try:
                transformed_record = {}

                # Apply field mappings
                for target_field, source_field in field_mappings.items():
                    value = None

                    # Handle special Medusa field mappings
                    if isinstance(source_field, str) and source_field.startswith("_default_"):
                        # Handle default values
                        default_value = source_field.replace("_default_", "")
                        if default_value == "true":
                            value = True
                        elif default_value == "false":
                            value = False
                        elif default_value == "published":
                            value = "published"
                        elif default_value == "salesforce":
                            value = "salesforce"
                        else:
                            value = default_value
                    elif isinstance(source_field, str) and source_field == "_auto_generate":
                        # Handle auto-generated fields
                        if target_field == "handle":
                            # Generate handle from title
                            title = record.get("Name", "")
                            if title:
                                import re
                                # Convert to lowercase, replace spaces and special chars with hyphens
                                handle = re.sub(r'[^a-z0-9]+', '-', title.lower().strip())
                                handle = re.sub(r'-+', '-', handle).strip('-')
                                value = handle or f"room-type-{record.get('Id', 'unknown')}"
                            else:
                                value = f"room-type-{record.get('Id', 'unknown')}"
                    elif isinstance(source_field, dict):
                        # Handle metadata object
                        metadata = {}
                        for meta_key, meta_source in source_field.items():
                            if isinstance(meta_source, str) and meta_source.startswith("_default_"):
                                default_value = meta_source.replace("_default_", "")
                                if default_value == "salesforce":
                                    metadata[meta_key] = "salesforce"
                                else:
                                    metadata[meta_key] = default_value
                            elif meta_source in record:
                                meta_value = record[meta_source]
                                # Handle special transformations for metadata
                                if meta_key == "is_active" and meta_source == "IsDeleted":
                                    # Invert IsDeleted to get is_active
                                    metadata[meta_key] = not bool(meta_value) if meta_value is not None else True
                                else:
                                    metadata[meta_key] = meta_value
                        value = metadata
                    elif target_field in field_transformations:
                        # Handle configured field transformations
                        field_transform = field_transformations[target_field]
                        transform_type = field_transform.get("type")
                        transform_source = field_transform.get("source_field", source_field)

                        if transform_source in record:
                            raw_value = record[transform_source]

                            # Apply the specific transformation
                            if transform_type == "invert_boolean":
                                if raw_value is None:
                                    value = None
                                elif isinstance(raw_value, str):
                                    raw_value = raw_value.lower()
                                    if raw_value in ('true', '1', 'yes', 'on'):
                                        value = False
                                    elif raw_value in ('false', '0', 'no', 'off'):
                                        value = True
                                    else:
                                        value = None
                                elif isinstance(raw_value, bool):
                                    value = not raw_value
                                else:
                                    value = None
                            elif transform_type == "lowercase_email":
                                # Simple email transformation - just lowercase
                                if raw_value and isinstance(raw_value, str):
                                    value = raw_value.lower().strip()
                                else:
                                    value = raw_value
                            elif transform_type == "normalize_url":
                                # Simple URL normalization
                                if raw_value and isinstance(raw_value, str):
                                    url = raw_value.strip()
                                    if url and not url.startswith(('http://', 'https://')):
                                        value = f'https://{url}'
                                    else:
                                        value = url
                                else:
                                    value = raw_value
                            elif transform_type == "clean_text":
                                # Simple text cleaning
                                if raw_value and isinstance(raw_value, str):
                                    value = raw_value.strip()
                                else:
                                    value = raw_value
                            else:
                                # Unknown transformation type, use raw value
                                self._log(run, "WARNING", "transform", f"Unknown transformation type '{transform_type}' for field '{target_field}'")
                                value = raw_value
                        else:
                            self._log(run, "WARNING", "transform", f"Transform source field '{transform_source}' not found in record {i}")

                    # Standard field mapping
                    elif source_field in record:
                        value = record[source_field]
                    elif source_field in ['salesforce', 'Salesforce']:
                        # Handle literal values for source_system
                        value = 'salesforce'
                    else:
                        self._log(run, "WARNING", "transform", f"Source field '{source_field}' not found in record {i}")

                    # Apply general transformations
                    if value is not None:
                        if transformation_config.get("trim_whitespace", False) and isinstance(value, str):
                            value = value.strip()

                    # Apply clean_nulls filter
                    if not (transformation_config.get("clean_nulls", False) and value is None):
                        transformed_record[target_field] = value

                # Add metadata
                transformed_record["_pipeline_run_id"] = run.id
                transformed_record["_extracted_at"] = datetime.utcnow().isoformat()

                # Handle resort name resolution if configured
                resort_config = transformation_config.get("resort_name_resolution", {})
                if resort_config.get("enabled", False):
                    resort_id = transformed_record.get("resort_id")
                    if resort_id:
                        try:
                            # For now, we'll skip the complex Salesforce lookup that was causing hangs
                            # Instead, we'll just use the resort_id as a placeholder
                            transformed_record["resort_name"] = f"Resort_{resort_id}"
                            self._log(run, "DEBUG", "transform", f"Set resort_name placeholder for resort_id: {resort_id}")
                        except Exception as e:
                            self._log(run, "WARNING", "transform", f"Error resolving resort name for {resort_id}: {e}")

                transformed_data.append(transformed_record)

            except Exception as e:
                self._log(run, "ERROR", "transform", f"Error transforming record {i}: {e}")
                continue

        run.records_transformed = len(transformed_data)
        self.db.commit()  # Ensure transformation count is persisted
        self._log(run, "INFO", "transform", f"Transformed {len(transformed_data)} records")
        return transformed_data

    def _store_processed_data(self, pipeline: ETLPipeline, run: ETLPipelineRun, data: List[Dict[str, Any]]) -> str:
        """Store processed data in MinIO"""
        self._log(run, "INFO", "store-processed", f"Storing {len(data)} processed records in MinIO")

        # Store using the MinIO service's store_processed_data method
        file_path = minio_service.store_processed_data(data, pipeline.name, run.id)

        self._log(run, "INFO", "store-processed", f"Processed data stored at: {file_path}")
        return file_path

    def _load_data_to_postgresql(self, pipeline: ETLPipeline, run: ETLPipelineRun, data: List[Dict[str, Any]]) -> int:
        """
        Load transformed data to PostgreSQL using individual pipeline strategies.
        Routes to appropriate pipeline strategy based on destination table and pipeline name.
        """
        self._log(run, "INFO", "load", f"🔄 Loading {len(data)} records to PostgreSQL table: {pipeline.destination_table}")
        self._log(run, "INFO", "routing", f"📋 Pipeline: '{pipeline.name}' → Table: '{pipeline.destination_table}'")

        name_lc = (pipeline.name or "").lower()
        table = (pipeline.destination_table or "").lower()


        if table == "destination" or "resort" in name_lc:
            self._log(run, "INFO", "routing", "🏨 Using Destination Pipeline Strategy for Resort/Destination data")
            loaded_count = self.destination_pipeline.execute(run, data)
        elif table == "hotel" or "hotel" in name_lc:
            self._log(run, "INFO", "routing", "🏢 Using Hotel Pipeline Strategy for Hotel data")
            loaded_count = self.hotel_pipeline.execute(run, data)
        elif table == "product" or "room_type" in name_lc or "room type" in name_lc:
            self._log(run, "INFO", "routing", "🛏️ Using Room Type Pipeline Strategy for Room Type data")
            loaded_count = self.room_type_pipeline.execute(run, data)
        elif table == "product_variant" or "room" in name_lc:
            self._log(run, "INFO", "routing", "🚪 Using Room Pipeline Strategy for Room data")
            loaded_count = self.room_pipeline.execute(run, data)
        else:
            raise ValueError(
                f"Unsupported destination table or pipeline name: table='{pipeline.destination_table}', name='{pipeline.name}'"
            )

        run.records_loaded = loaded_count
        self.db.commit()  # Ensure loaded count is persisted
        self._log(run, "INFO", "load", f"Successfully loaded {loaded_count} records")
        return loaded_count

    # ========================================
    # UTILITY METHODS
    # ========================================


