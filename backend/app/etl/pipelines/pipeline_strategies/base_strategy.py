"""
Base pipeline strategy interface for ETL operations.
"""
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional
from sqlalchemy.orm import Session

from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun


@dataclass
class PipelineExecutionResult:
    """Result of pipeline execution"""
    success: bool
    records_processed: int
    records_extracted: int = 0
    records_transformed: int = 0
    records_loaded: int = 0
    error_message: Optional[str] = None
    execution_time: Optional[float] = None
    raw_data_path: Optional[str] = None
    processed_data_path: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class BasePipelineStrategy(ABC):
    """
    Abstract base class for pipeline execution strategies.
    
    Each strategy implements the ETL process for a specific source/destination combination.
    This allows for clean separation of concerns and easy extensibility.
    """
    
    def __init__(self, db_session: Session):
        """
        Initialize the pipeline strategy.
        
        Args:
            db_session: Database session for persistence operations
        """
        self.db = db_session
        self.logger = self._get_logger()
    
    @abstractmethod
    def get_strategy_name(self) -> str:
        """Return the name of this strategy"""
        pass
    
    @abstractmethod
    def get_supported_source_types(self) -> List[str]:
        """Return list of supported source types"""
        pass
    
    @abstractmethod
    def get_supported_destination_types(self) -> List[str]:
        """Return list of supported destination types"""
        pass
    
    @abstractmethod
    def validate_configuration(self, pipeline: ETLPipeline) -> tuple[bool, List[str]]:
        """
        Validate pipeline configuration for this strategy.
        
        Args:
            pipeline: Pipeline configuration to validate
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        pass
    
    @abstractmethod
    def execute(self, pipeline: ETLPipeline, run: ETLPipelineRun) -> PipelineExecutionResult:
        """
        Execute the ETL pipeline.
        
        Args:
            pipeline: Pipeline configuration
            run: Pipeline run record for tracking
            
        Returns:
            PipelineExecutionResult with execution details
        """
        pass
    
    def supports_pipeline(self, pipeline: ETLPipeline) -> bool:
        """
        Check if this strategy supports the given pipeline configuration.
        
        Args:
            pipeline: Pipeline to check
            
        Returns:
            True if this strategy can handle the pipeline
        """
        return (
            pipeline.source_type in self.get_supported_source_types() and
            pipeline.destination_type in self.get_supported_destination_types()
        )
    
    def _get_logger(self):
        """Get logger instance for this strategy"""
        import logging
        return logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def _log(self, run: ETLPipelineRun, level: str, stage: str, message: str):
        """
        Add a log entry to the pipeline run.

        Args:
            run: Pipeline run to log to
            level: Log level (INFO, ERROR, DEBUG, etc.)
            stage: Pipeline stage (extract, transform, load, etc.)
            message: Log message
        """
        from app.schemas.etl_pipeline import PipelineLogEntry

        log_entry = PipelineLogEntry(
            timestamp=datetime.utcnow(),
            level=level,
            stage=stage,
            message=message
        )

        if run.logs is None:
            run.logs = []

        # Convert to dict and ensure datetime is serialized as ISO string
        log_dict = log_entry.dict()
        log_dict['timestamp'] = log_entry.timestamp.isoformat()

        run.logs.append(log_dict)

        # Also log to application logger
        log_method = getattr(self.logger, level.lower(), self.logger.info)
        log_method(f"[{stage}] {message}")
    
    def _safe_create_task(self, coro):
        """Safely create an asyncio task if in async context"""
        try:
            import asyncio
            loop = asyncio.get_event_loop()
            if loop.is_running():
                asyncio.create_task(coro)
        except RuntimeError:
            # Not in async context, skip
            pass
