"""
API to PostgreSQL pipeline strategy implementation.
Example of how to extend the factory pattern with new source/destination combinations.
"""
import json
import requests
from datetime import datetime
from typing import Any, Dict, List, Optional
from sqlalchemy.orm import Session

from .base_strategy import BasePipelineStrategy, PipelineExecutionResult
from app.models.etl_pipeline import ET<PERSON>ipeline, ETLPipelineRun
from app.models.destination import Destination
from app.services.minio_service import minio_service
from app.services.websocket_manager import websocket_manager


class APIToPostgreSQLStrategy(BasePipelineStrategy):
    """
    Strategy for ETL pipelines that extract from REST APIs and load to PostgreSQL.
    
    This strategy demonstrates how to extend the factory pattern with new
    source/destination combinations.
    """
    
    def get_strategy_name(self) -> str:
        return "api_to_postgresql"
    
    def get_supported_source_types(self) -> List[str]:
        return ["api", "rest_api", "http_api"]
    
    def get_supported_destination_types(self) -> List[str]:
        return ["postgresql"]
    
    def validate_configuration(self, pipeline: ETLPipeline) -> tuple[bool, List[str]]:
        """Validate API to PostgreSQL pipeline configuration"""
        errors = []
        
        # Validate source configuration
        source_config = pipeline.source_config or {}
        if not source_config.get("api_url"):
            errors.append("API URL is required for API pipelines")
        
        if not source_config.get("endpoint"):
            errors.append("API endpoint is required for API pipelines")
        
        # Validate destination configuration
        if not pipeline.destination_table:
            errors.append("Destination table is required for PostgreSQL pipelines")
        
        if not pipeline.destination_fields or len(pipeline.destination_fields) == 0:
            errors.append("Destination field mappings are required")
        
        return len(errors) == 0, errors
    
    def execute(self, pipeline: ETLPipeline, run: ETLPipelineRun) -> PipelineExecutionResult:
        """Execute API to PostgreSQL ETL pipeline"""
        start_time = datetime.utcnow()
        result = PipelineExecutionResult(success=False, records_processed=0)
        
        try:
            self._log(run, "INFO", "pipeline", f"Starting API to PostgreSQL pipeline: {pipeline.name}")
            
            # Step 1: Extract data from API
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "extract", "running", 0, "Extracting data from API"
            ))
            
            raw_data = self._extract_data_from_api(pipeline, run)
            result.records_extracted = len(raw_data)
            
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "extract", "completed", 20, f"Extracted {len(raw_data)} records"
            ))
            
            # Step 2: Store raw data in MinIO
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "store-raw", "running", 20, "Storing raw data in MinIO"
            ))
            
            raw_data_path = self._store_raw_data(pipeline, run, raw_data)
            result.raw_data_path = raw_data_path
            
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "store-raw", "completed", 40, "Raw data stored successfully"
            ))
            
            # Step 3: Transform data
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "transform", "running", 40, "Applying transformation rules"
            ))
            
            transformed_data = self._transform_data(pipeline, run, raw_data)
            result.records_transformed = len(transformed_data)
            
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "transform", "completed", 60, f"Transformed {len(transformed_data)} records"
            ))
            
            # Step 4: Store processed data in MinIO
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "store-processed", "running", 60, "Storing processed data in MinIO"
            ))
            
            processed_data_path = self._store_processed_data(pipeline, run, transformed_data)
            result.processed_data_path = processed_data_path
            
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "store-processed", "completed", 80, "Processed data stored successfully"
            ))
            
            # Step 5: Load data to PostgreSQL
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "load", "running", 80, "Loading data to PostgreSQL"
            ))
            
            loaded_count = self._load_data_to_postgresql(pipeline, run, transformed_data)
            result.records_loaded = loaded_count
            result.records_processed = loaded_count
            
            self._safe_create_task(websocket_manager.send_pipeline_progress(
                pipeline.id, "load", "completed", 100, f"Loaded {loaded_count} records to PostgreSQL"
            ))
            
            # Mark as successful
            result.success = True
            end_time = datetime.utcnow()
            result.execution_time = (end_time - start_time).total_seconds()
            
            self._log(run, "INFO", "pipeline", f"Pipeline completed successfully. Processed {result.records_processed} records")
            
            # Send completion notification
            self._safe_create_task(websocket_manager.send_pipeline_completion(
                pipeline.id, "completed", run.to_dict()
            ))
            
        except Exception as e:
            result.success = False
            result.error_message = str(e)
            end_time = datetime.utcnow()
            result.execution_time = (end_time - start_time).total_seconds()
            
            self.logger.error(f"Pipeline execution failed: {e}")
            self._log(run, "ERROR", "pipeline", f"Pipeline execution failed: {e}")
            
            # Send failure notification
            self._safe_create_task(websocket_manager.send_pipeline_completion(
                pipeline.id, "failed", run.to_dict()
            ))
            
            raise
        
        return result
    
    def _extract_data_from_api(self, pipeline: ETLPipeline, run: ETLPipelineRun) -> List[Dict[str, Any]]:
        """Extract data from REST API"""
        source_config = pipeline.source_config or {}
        api_url = source_config.get("api_url")
        endpoint = source_config.get("endpoint")
        headers = source_config.get("headers", {})
        params = source_config.get("params", {})
        
        full_url = f"{api_url.rstrip('/')}/{endpoint.lstrip('/')}"
        
        self._log(run, "INFO", "extract", f"Making API request to: {full_url}")
        
        try:
            response = requests.get(full_url, headers=headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            # Handle different response formats
            if isinstance(data, list):
                records = data
            elif isinstance(data, dict):
                # Look for common data keys
                records = data.get("data", data.get("results", data.get("items", [data])))
            else:
                records = [data]
            
            run.records_extracted = len(records)
            self._log(run, "INFO", "extract", f"Extracted {len(records)} records from API")
            return records
            
        except requests.RequestException as e:
            raise ValueError(f"API request failed: {e}")
        except json.JSONDecodeError as e:
            raise ValueError(f"Failed to parse API response as JSON: {e}")
    
    def _store_raw_data(self, pipeline: ETLPipeline, run: ETLPipelineRun, data: List[Dict[str, Any]]) -> str:
        """Store raw data in MinIO"""
        self._log(run, "INFO", "store-raw", "Storing raw data in MinIO")
        
        # Create file path
        file_path = f"raw/{pipeline.name}/{run.id}/data.json"
        
        # Convert data to JSON
        json_data = json.dumps(data, indent=2, default=str)
        
        # Store in MinIO
        minio_service.store_data(file_path, json_data.encode('utf-8'))
        
        self._log(run, "INFO", "store-raw", f"Raw data stored at: {file_path}")
        return file_path
    
    def _transform_data(self, pipeline: ETLPipeline, run: ETLPipelineRun, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Transform data according to pipeline configuration"""
        self._log(run, "INFO", "transform", "Starting data transformation")
        
        transformed_data = []
        field_mappings = pipeline.destination_fields or {}
        transformation_config = pipeline.transformation_config or {}
        
        for i, record in enumerate(data):
            try:
                transformed_record = {}
                
                # Apply field mappings
                for target_field, source_field in field_mappings.items():
                    if source_field in record:
                        value = record[source_field]
                        
                        # Apply transformations
                        if transformation_config.get("clean_nulls", False) and value is None:
                            continue
                        
                        if transformation_config.get("trim_whitespace", False) and isinstance(value, str):
                            value = value.strip()
                        
                        transformed_record[target_field] = value
                    else:
                        self._log(run, "WARNING", "transform", f"Source field '{source_field}' not found in record {i}")
                
                # Add metadata
                transformed_record["_pipeline_run_id"] = run.id
                transformed_record["_extracted_at"] = datetime.utcnow().isoformat()
                
                transformed_data.append(transformed_record)
                
            except Exception as e:
                self._log(run, "ERROR", "transform", f"Error transforming record {i}: {e}")
                continue
        
        run.records_transformed = len(transformed_data)
        self._log(run, "INFO", "transform", f"Transformed {len(transformed_data)} records")
        return transformed_data
    
    def _store_processed_data(self, pipeline: ETLPipeline, run: ETLPipelineRun, data: List[Dict[str, Any]]) -> str:
        """Store processed data in MinIO"""
        self._log(run, "INFO", "store-processed", "Storing processed data in MinIO")
        
        # Create file path
        file_path = f"processed/{pipeline.name}/{run.id}/data.json"
        
        # Convert data to JSON
        json_data = json.dumps(data, indent=2, default=str)
        
        # Store in MinIO
        minio_service.store_data(file_path, json_data.encode('utf-8'))
        
        self._log(run, "INFO", "store-processed", f"Processed data stored at: {file_path}")
        return file_path
    
    def _load_data_to_postgresql(self, pipeline: ETLPipeline, run: ETLPipelineRun, data: List[Dict[str, Any]]) -> int:
        """Load transformed data to PostgreSQL"""
        self._log(run, "INFO", "load", f"Loading {len(data)} records to PostgreSQL table: {pipeline.destination_table}")
        
        loaded_count = 0
        
        if pipeline.destination_table == "destination":
            # Load to destination table
            for record in data:
                try:
                    # Create destination record
                    destination = Destination(
                        name=record.get("name"),
                        country=record.get("country"),
                        pipeline_run_id=run.id
                    )
                    
                    self.db.add(destination)
                    loaded_count += 1
                    
                except Exception as e:
                    self._log(run, "ERROR", "load", f"Error loading record: {e}")
                    continue
            
            # Commit all records
            self.db.commit()
        else:
            raise ValueError(f"Unsupported destination table: {pipeline.destination_table}")
        
        run.records_loaded = loaded_count
        self._log(run, "INFO", "load", f"Successfully loaded {loaded_count} records to PostgreSQL")
        return loaded_count
