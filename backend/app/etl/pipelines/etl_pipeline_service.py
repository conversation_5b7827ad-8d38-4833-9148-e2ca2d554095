"""
ETL Pipeline execution service
"""
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional
from sqlalchemy.orm import Session
import asyncio

from app.core.database import get_db
from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun
from app.models.destination import Destination
from app.services.minio_service import minio_service
from app.etl.extractors.salesforce_extractor import SalesforceExtractor
from app.schemas.etl_pipeline import PipelineLogEntry
from app.services.websocket_manager import websocket_manager
from app.etl.pipelines.pipeline_factory import PipelineFactory

logger = logging.getLogger(__name__)


class ETLPipelineService:
    """Service for executing ETL pipelines"""

    def __init__(self, db: Session):
        self.db = db
        self.pipeline_factory = PipelineFactory(db)
        self.logger = logging.getLogger(__name__)

    def _safe_create_task(self, coro):
        """Safely create an asyncio task, handling cases where there's no event loop"""
        try:
            loop = asyncio.get_running_loop()
            return asyncio.create_task(coro)
        except RuntimeError:
            # No event loop running, skip the websocket notification
            logger.warning("No event loop running, skipping websocket notification")
            return None
    
    def execute_pipeline(self, pipeline_id: int) -> ETLPipelineRun:
        """
        Execute an ETL pipeline
        
        Args:
            pipeline_id: ID of the pipeline to execute
            
        Returns:
            ETLPipelineRun: The pipeline run record
        """
        # Get pipeline configuration
        pipeline = self.db.query(ETLPipeline).filter(ETLPipeline.id == pipeline_id).first()
        if not pipeline:
            raise ValueError(f"Pipeline with ID {pipeline_id} not found")
        
        if not pipeline.is_active:
            raise ValueError(f"Pipeline {pipeline.name} is not active")
        
        # Create pipeline run record
        run = ETLPipelineRun(
            pipeline_id=pipeline_id,
            status="running",
            logs=[]
        )
        self.db.add(run)
        self.db.commit()
        self.db.refresh(run)
        
        try:
            # Create appropriate strategy for this pipeline
            strategy = self.pipeline_factory.create_strategy(pipeline)
            self.logger.info(f"Using strategy: {strategy.get_strategy_name()} for pipeline: {pipeline.name}")

            # Execute pipeline using the strategy
            result = strategy.execute(pipeline, run)

            # Update run status based on result
            if result.success:
                run.status = "completed"
                run.completed_at = datetime.utcnow()
                run.raw_data_path = result.raw_data_path
                run.processed_data_path = result.processed_data_path
                run.records_extracted = result.records_extracted
                run.records_transformed = result.records_transformed
                run.records_loaded = result.records_loaded

                self.logger.info(f"Pipeline execution completed successfully. Processed {result.records_processed} records")

                # Send success notification
                self._safe_create_task(websocket_manager.send_pipeline_completion(
                    pipeline_id, "completed", run.to_dict()
                ))
            else:
                run.status = "failed"
                run.completed_at = datetime.utcnow()
                run.error_message = result.error_message
                self.logger.error(f"Pipeline execution failed: {result.error_message}")

                # Send failure notification
                self._safe_create_task(websocket_manager.send_pipeline_completion(
                    pipeline_id, "failed", run.to_dict()
                ))

        except Exception as e:
            self.logger.error(f"Pipeline execution failed: {e}")
            run.status = "failed"
            run.completed_at = datetime.utcnow()
            run.error_message = str(e)

            # Send failure notification
            self._safe_create_task(websocket_manager.send_pipeline_completion(
                pipeline_id, "failed", run.to_dict()
            ))

        # Ensure status is committed to database
        self.db.commit()
        self.db.refresh(run)

        # Log final status for debugging
        self.logger.info(f"Pipeline {pipeline.name} (ID: {pipeline_id}) execution finished with status: {run.status}")

        return run

    def get_available_strategies(self) -> List[Dict[str, any]]:
        """
        Get information about all available pipeline strategies.

        Returns:
            List of strategy information dictionaries
        """
        return self.pipeline_factory.get_available_strategies()

    def validate_pipeline_configuration(self, pipeline: ETLPipeline) -> tuple[bool, List[str]]:
        """
        Validate a pipeline configuration.

        Args:
            pipeline: Pipeline to validate

        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        return self.pipeline_factory.validate_pipeline_configuration(pipeline)

    def _log(self, run: ETLPipelineRun, level: str, stage: str, message: str):
        """
        Add a log entry to the pipeline run.

        Args:
            run: Pipeline run to log to
            level: Log level (INFO, ERROR, DEBUG, etc.)
            stage: Pipeline stage (extract, transform, load, etc.)
            message: Log message
        """
        log_entry = PipelineLogEntry(
            timestamp=datetime.utcnow(),
            level=level,
            stage=stage,
            message=message
        )

        if run.logs is None:
            run.logs = []

        # Convert to dict and ensure datetime is serialized as ISO string
        log_dict = log_entry.dict()
        log_dict['timestamp'] = log_entry.timestamp.isoformat()

        run.logs.append(log_dict)

        # Also log to application logger
        log_method = getattr(self.logger, level.lower(), self.logger.info)
        log_method(f"[{stage}] {message}")
