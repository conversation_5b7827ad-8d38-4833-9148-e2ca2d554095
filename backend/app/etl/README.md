# ETL (Extract, Transform, Load) Module

This module contains all ETL-related functionality for the Flinkk Transfer Hub, organized into a clean, modular structure that supports scalable data pipeline operations.

## Directory Structure

```
app/etl/
├── extractors/          # Data extraction from various sources
├── transformers/        # Data transformation and validation
├── loaders/            # Data loading to target systems
├── pipelines/          # Pipeline orchestration and management
└── utils/              # Common utilities and helpers
```

## Components Overview

### Extractors (`extractors/`)
Data extraction components for various source systems:
- **Salesforce Extractor**: Extracts data from Salesforce objects using REST and Bulk APIs
- **API Extractors**: Generic REST API data extraction
- **Database Extractors**: Direct database connections and queries

### Transformers (`transformers/`)
Data transformation and validation logic:
- **Field Mapper**: Maps fields between source and target schemas
- **Data Validator**: Validates data quality and business rules
- **Transformation Engine**: Applies complex transformation rules
- **Base Transformer**: Abstract base class for custom transformations

### Loaders (`loaders/`)
Data loading adapters for target systems:
- **Medusa Adapter**: Loads data to Medusa e-commerce platform
- **OPS Adapter**: Loads data to operational systems
- **Base Adapter**: Abstract base class for custom loaders

### Pipelines (`pipelines/`)
Pipeline orchestration and workflow management:
- **Pipeline Factory**: Creates appropriate pipeline strategies
- **Pipeline Strategies**: Implements specific ETL workflows
- **ETL Pipeline Service**: Manages pipeline execution
- **Pipeline Cleanup Service**: Handles cleanup and maintenance

### Utils (`utils/`)
Common utilities and helper functions:
- Configuration helpers
- Logging utilities
- Error handling and retry logic
- Data processing utilities

## ETL Workflow

The typical ETL workflow follows this pattern:

1. **Extract**: Use extractors to pull data from source systems
2. **Transform**: Apply transformations using field mappers and validators
3. **Load**: Use loaders to push data to target systems
4. **Monitor**: Track pipeline execution and handle errors

## Configuration

ETL-specific configuration is managed through:
- `app/config/etl_config.yml`: YAML configuration file
- `app/config/settings.py`: Python configuration management

## Usage Examples

### Basic Pipeline Execution
```python
from app.etl.pipelines.pipeline_factory import PipelineFactory
from app.etl.pipelines.etl_pipeline_service import ETLPipelineService

# Create pipeline strategy
factory = PipelineFactory(db_session)
strategy = factory.create_strategy(pipeline_config)

# Execute pipeline
service = ETLPipelineService(db_session)
result = await service.execute_pipeline(pipeline_id)
```

### Custom Extractor
```python
from app.etl.extractors.salesforce_extractor import SalesforceExtractor

extractor = SalesforceExtractor()
extractor.authenticate()
data = extractor.extract_records('Account', ['Id', 'Name'])
```

### Data Transformation
```python
from app.etl.transformers.field_mapper import FieldMapper

mapper = FieldMapper(field_mappings)
transformed_data = mapper.transform(source_data)
```

## Best Practices

1. **Modularity**: Keep extractors, transformers, and loaders separate
2. **Configuration**: Use YAML configuration for pipeline settings
3. **Error Handling**: Implement proper error handling and retry logic
4. **Monitoring**: Use logging and metrics for pipeline monitoring
5. **Testing**: Write unit tests for each component

## Adding New Components

### New Extractor
1. Create a new file in `extractors/`
2. Inherit from appropriate base class
3. Implement required methods
4. Add to `extractors/__init__.py`

### New Transformer
1. Create a new file in `transformers/`
2. Inherit from `BaseTransformer`
3. Implement transformation logic
4. Add to `transformers/__init__.py`

### New Loader
1. Create a new file in `loaders/`
2. Inherit from `BaseTargetAdapter`
3. Implement loading logic
4. Add to `loaders/__init__.py`

## Related Documentation

- [Pipeline Factory Pattern](../../../PIPELINE_FACTORY_PATTERN.md)
- [ETL Pipeline Documentation](../../../ETL_PIPELINE_README.md)
- [Configuration Guide](../config/README.md)
