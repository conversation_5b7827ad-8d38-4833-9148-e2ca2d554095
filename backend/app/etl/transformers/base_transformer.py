"""
Base transformer class for data transformation operations.
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)


class BaseTransformer(ABC):
    """
    Abstract base class for all data transformers.

    This class defines the interface that all transformers must implement
    to ensure consistency across different transformation operations.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the transformer with optional configuration.

        Args:
            config: Optional configuration dictionary for the transformer
        """
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

    @abstractmethod
    def transform(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform a single record.

        Args:
            data: Input data record as a dictionary

        Returns:
            Transformed data record as a dictionary
        """
        pass

    def transform_batch(self, data_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Transform a batch of records.

        Args:
            data_list: List of input data records

        Returns:
            List of transformed data records
        """
        transformed_records = []

        for i, record in enumerate(data_list):
            try:
                transformed_record = self.transform(record)
                transformed_records.append(transformed_record)
            except Exception as e:
                self.logger.error(f"Error transforming record {i}: {e}")
                # Optionally, you can choose to skip failed records or re-raise
                # For now, we'll skip and continue
                continue

        self.logger.info(f"Transformed {len(transformed_records)} out of {len(data_list)} records")
        return transformed_records

    def validate_config(self) -> bool:
        """
        Validate the transformer configuration.

        Returns:
            True if configuration is valid, False otherwise
        """
        # Base implementation - override in subclasses for specific validation
        return True

    def get_required_fields(self) -> List[str]:
        """
        Get the list of required input fields for this transformer.

        Returns:
            List of required field names
        """
        # Base implementation - override in subclasses
        return []

    def get_output_fields(self) -> List[str]:
        """
        Get the list of output fields that this transformer produces.

        Returns:
            List of output field names
        """
        # Base implementation - override in subclasses
        return []
