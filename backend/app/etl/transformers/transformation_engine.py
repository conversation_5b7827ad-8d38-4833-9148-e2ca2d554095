from typing import Any, Dict, List, Optional, Union
import logging
from datetime import datetime
import json

from .base_transformer import BaseTransformer
from .field_mapper import FieldMapper
from .data_validator import DataValidator, ValidationResult

logger = logging.getLogger(__name__)


class TransformationResult:
    """Result of a transformation operation"""

    def __init__(self):
        self.success = True
        self.transformed_records = []
        self.errors = []
        self.warnings = []
        self.validation_results = []
        self.summary = {}

    def add_error(self, error: str):
        self.errors.append(error)
        self.success = False

    def add_warning(self, warning: str):
        self.warnings.append(warning)

    def to_dict(self) -> Dict[str, Any]:
        return {
            'success': self.success,
            'transformed_records': self.transformed_records,
            'errors': self.errors,
            'warnings': self.warnings,
            'summary': self.summary
        }


class TransformationEngine:
    """
    Main transformation engine that orchestrates the transformation process
    """

    def __init__(self):
        self.logger = logger
        self.field_mapper = FieldMapper()
        self.data_validator = DataValidator()
        self.transformers = {}

    def register_transformer(self, name: str, transformer: BaseTransformer):
        """Register a custom transformer"""
        self.transformers[name] = transformer

    def transform_data(
        self,
        source_data: List[Dict[str, Any]],
        field_mappings: List[Dict[str, Any]],
        target_schema: Dict[str, Any],
        transformation_config: Optional[Dict[str, Any]] = None
    ) -> TransformationResult:
        """
        Transform source data using field mappings and target schema

        Args:
            source_data: List of source records
            field_mappings: List of field mapping configurations
            target_schema: Target schema definition
            transformation_config: Additional transformation configuration

        Returns:
            TransformationResult with transformed data and metadata
        """
        result = TransformationResult()

        try:
            self.logger.info(f"Starting transformation of {len(source_data)} records")

            # Get target schema fields
            target_fields = target_schema.get('fields', [])

            # Transform each record
            for i, source_record in enumerate(source_data):
                try:
                    transformed_record = self._transform_record(
                        source_record,
                        field_mappings,
                        target_fields,
                        transformation_config or {}
                    )

                    # Validate transformed record
                    validation_result = self.data_validator.validate_record(
                        transformed_record,
                        target_fields
                    )
                    result.validation_results.append(validation_result)

                    if validation_result.is_valid:
                        result.transformed_records.append(transformed_record)
                    else:
                        result.add_error(f"Record {i} validation failed: {', '.join(validation_result.errors)}")
                        # Still include the record but mark it as having issues
                        transformed_record['_validation_errors'] = validation_result.errors
                        result.transformed_records.append(transformed_record)

                except Exception as e:
                    error_msg = f"Error transforming record {i}: {str(e)}"
                    self.logger.error(error_msg)
                    result.add_error(error_msg)

            # Generate summary
            result.summary = self._generate_summary(result, len(source_data))

            self.logger.info(f"Transformation completed. {len(result.transformed_records)} records processed")

        except Exception as e:
            error_msg = f"Transformation engine error: {str(e)}"
            self.logger.error(error_msg)
            result.add_error(error_msg)

        return result

    def _transform_record(
        self,
        source_record: Dict[str, Any],
        field_mappings: List[Dict[str, Any]],
        target_fields: List[Dict[str, Any]],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Transform a single record"""

        transformed_record = {}

        # Apply field mappings
        for mapping in field_mappings:
            source_field = mapping.get('source_field')
            target_field = mapping.get('target_field')
            transformation_type = mapping.get('transformation_type', 'direct')
            transformation_config = mapping.get('transformation_config', {})

            if not source_field or not target_field:
                continue

            # Get source value
            source_value = source_record.get(source_field)

            # Apply transformation
            transformed_value = self._apply_transformation(
                source_value,
                transformation_type,
                transformation_config
            )

            # Set target value
            transformed_record[target_field] = transformed_value

        # Add any default values for unmapped required fields
        target_field_map = {field['name']: field for field in target_fields}
        for field_name, field_def in target_field_map.items():
            if field_name not in transformed_record:
                default_value = field_def.get('default_value')
                if default_value is not None:
                    transformed_record[field_name] = default_value
                elif field_def.get('required', False):
                    # Log warning for missing required field
                    self.logger.warning(f"Required field '{field_name}' not mapped and has no default value")

        # Add metadata
        transformed_record['_transformed_at'] = datetime.utcnow().isoformat()
        transformed_record['_source_id'] = source_record.get('Id') or source_record.get('id')

        return transformed_record

    def _apply_transformation(
        self,
        value: Any,
        transformation_type: str,
        config: Dict[str, Any]
    ) -> Any:
        """Apply a specific transformation to a value"""

        if value is None:
            return None

        try:
            if transformation_type == 'direct':
                return value

            elif transformation_type == 'uppercase':
                return str(value).upper() if value else value

            elif transformation_type == 'lowercase':
                return str(value).lower() if value else value

            elif transformation_type == 'trim':
                return str(value).strip() if value else value

            elif transformation_type == 'invert_boolean':
                # Invert boolean value (e.g., IsDeleted -> is_active)
                if value is None:
                    return None
                # Handle string representations of boolean
                if isinstance(value, str):
                    value = value.lower()
                    if value in ('true', '1', 'yes', 'on'):
                        return False
                    elif value in ('false', '0', 'no', 'off'):
                        return True
                    else:
                        return None  # Invalid boolean string
                # Handle actual boolean values
                elif isinstance(value, bool):
                    return not value
                # Handle numeric values (0 = False, anything else = True)
                elif isinstance(value, (int, float)):
                    return not bool(value)
                else:
                    return None

            elif transformation_type == 'format_date':
                # Basic date formatting
                if isinstance(value, str) and value:
                    # Try to parse and reformat date
                    from datetime import datetime
                    try:
                        # Assume ISO format input
                        dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
                        format_string = config.get('format', '%Y-%m-%d')
                        return dt.strftime(format_string)
                    except:
                        return value
                return value

            elif transformation_type == 'format_currency':
                # Basic currency formatting
                try:
                    amount = float(value) if value else 0
                    currency = config.get('currency', 'USD')
                    return f"{amount:.2f} {currency}"
                except:
                    return value

            elif transformation_type == 'concatenate':
                # Concatenate with other values
                separator = config.get('separator', ' ')
                parts = config.get('parts', [])
                result_parts = [str(value) if value else '']
                for part in parts:
                    if isinstance(part, str):
                        result_parts.append(part)
                return separator.join(result_parts)

            elif transformation_type == 'custom':
                # Apply custom transformer if registered
                transformer_name = config.get('transformer')
                if transformer_name and transformer_name in self.transformers:
                    return self.transformers[transformer_name].transform(value, config)
                return value

            else:
                self.logger.warning(f"Unknown transformation type: {transformation_type}")
                return value

        except Exception as e:
            self.logger.error(f"Error applying transformation {transformation_type}: {str(e)}")
            return value

    def _generate_summary(self, result: TransformationResult, total_records: int) -> Dict[str, Any]:
        """Generate transformation summary"""

        valid_records = sum(1 for vr in result.validation_results if vr.is_valid)
        invalid_records = len(result.validation_results) - valid_records

        return {
            'total_source_records': total_records,
            'total_transformed_records': len(result.transformed_records),
            'valid_records': valid_records,
            'invalid_records': invalid_records,
            'success_rate': valid_records / total_records if total_records > 0 else 0,
            'total_errors': len(result.errors),
            'total_warnings': len(result.warnings),
            'transformation_timestamp': datetime.utcnow().isoformat()
        }

    def get_available_transformations(self) -> List[Dict[str, Any]]:
        """Get list of available transformation types"""
        return [
            {'type': 'direct', 'label': 'Direct Copy', 'description': 'Copy value as-is'},
            {'type': 'uppercase', 'label': 'Uppercase', 'description': 'Convert to uppercase'},
            {'type': 'lowercase', 'label': 'Lowercase', 'description': 'Convert to lowercase'},
            {'type': 'trim', 'label': 'Trim Whitespace', 'description': 'Remove leading/trailing whitespace'},
            {'type': 'invert_boolean', 'label': 'Invert Boolean', 'description': 'Invert boolean value (true->false, false->true)'},
            {'type': 'format_date', 'label': 'Format Date', 'description': 'Format date string'},
            {'type': 'format_currency', 'label': 'Format Currency', 'description': 'Format as currency'},
            {'type': 'concatenate', 'label': 'Concatenate', 'description': 'Combine with other values'},
            {'type': 'custom', 'label': 'Custom Transform', 'description': 'Apply custom transformation'}
        ]