from typing import Any, Dict, List, Optional
from pydantic import BaseModel, ValidationError
import logging

logger = logging.getLogger(__name__)


class ValidationResult(BaseModel):
    """Result of data validation"""
    is_valid: bool
    errors: List[str] = []
    warnings: List[str] = []
    field_errors: Dict[str, List[str]] = {}


class DataValidator:
    """Validates data against target schema requirements"""

    def __init__(self):
        self.logger = logger

    def validate_record(self, record: Dict[str, Any], schema_fields: List[Dict[str, Any]]) -> ValidationResult:
        """
        Validate a single record against schema fields

        Args:
            record: The data record to validate
            schema_fields: List of schema field definitions

        Returns:
            ValidationResult with validation status and any errors
        """
        result = ValidationResult(is_valid=True)

        # Create a lookup for schema fields
        schema_field_map = {field['name']: field for field in schema_fields}

        # Check required fields
        for field in schema_fields:
            field_name = field['name']
            is_required = field.get('required', False)

            if is_required and (field_name not in record or record[field_name] is None):
                result.errors.append(f"Required field '{field_name}' is missing or null")
                if field_name not in result.field_errors:
                    result.field_errors[field_name] = []
                result.field_errors[field_name].append("Field is required")
                result.is_valid = False

        # Validate field types and constraints
        for field_name, value in record.items():
            if field_name in schema_field_map:
                field_def = schema_field_map[field_name]
                field_validation = self._validate_field_value(field_name, value, field_def)

                if not field_validation.is_valid:
                    result.errors.extend(field_validation.errors)
                    result.warnings.extend(field_validation.warnings)
                    if field_name not in result.field_errors:
                        result.field_errors[field_name] = []
                    result.field_errors[field_name].extend(field_validation.errors)
                    result.is_valid = False

        return result

    def validate_batch(self, records: List[Dict[str, Any]], schema_fields: List[Dict[str, Any]]) -> List[ValidationResult]:
        """
        Validate a batch of records

        Args:
            records: List of data records to validate
            schema_fields: List of schema field definitions

        Returns:
            List of ValidationResult objects, one per record
        """
        results = []

        for i, record in enumerate(records):
            try:
                result = self.validate_record(record, schema_fields)
                results.append(result)
            except Exception as e:
                self.logger.error(f"Error validating record {i}: {str(e)}")
                error_result = ValidationResult(
                    is_valid=False,
                    errors=[f"Validation error: {str(e)}"]
                )
                results.append(error_result)

        return results

    def _validate_field_value(self, field_name: str, value: Any, field_def: Dict[str, Any]) -> ValidationResult:
        """
        Validate a single field value against its definition

        Args:
            field_name: Name of the field
            value: Value to validate
            field_def: Field definition from schema

        Returns:
            ValidationResult for this field
        """
        result = ValidationResult(is_valid=True)

        if value is None:
            return result  # Null values are handled in required field check

        field_type = field_def.get('type', 'string').lower()

        # Type validation
        if field_type == 'string':
            if not isinstance(value, str):
                result.errors.append(f"Field '{field_name}' must be a string")
                result.is_valid = False
            else:
                # Check string length constraints
                max_length = field_def.get('max_length')
                if max_length and len(value) > max_length:
                    result.errors.append(f"Field '{field_name}' exceeds maximum length of {max_length}")
                    result.is_valid = False

        elif field_type in ['integer', 'int']:
            if not isinstance(value, int):
                try:
                    int(value)
                except (ValueError, TypeError):
                    result.errors.append(f"Field '{field_name}' must be an integer")
                    result.is_valid = False

        elif field_type in ['number', 'float', 'decimal']:
            if not isinstance(value, (int, float)):
                try:
                    float(value)
                except (ValueError, TypeError):
                    result.errors.append(f"Field '{field_name}' must be a number")
                    result.is_valid = False

        elif field_type == 'boolean':
            if not isinstance(value, bool):
                if str(value).lower() not in ['true', 'false', '1', '0']:
                    result.errors.append(f"Field '{field_name}' must be a boolean")
                    result.is_valid = False

        elif field_type == 'date':
            # Basic date format validation
            if isinstance(value, str):
                import re
                date_pattern = r'^\d{4}-\d{2}-\d{2}$'
                if not re.match(date_pattern, value):
                    result.warnings.append(f"Field '{field_name}' should be in YYYY-MM-DD format")

        elif field_type == 'datetime':
            # Basic datetime format validation
            if isinstance(value, str):
                import re
                datetime_pattern = r'^\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}'
                if not re.match(datetime_pattern, value):
                    result.warnings.append(f"Field '{field_name}' should be in ISO datetime format")

        return result

    def get_validation_summary(self, validation_results: List[ValidationResult]) -> Dict[str, Any]:
        """
        Generate a summary of validation results

        Args:
            validation_results: List of validation results

        Returns:
            Summary dictionary with counts and common errors
        """
        total_records = len(validation_results)
        valid_records = sum(1 for r in validation_results if r.is_valid)
        invalid_records = total_records - valid_records

        all_errors = []
        all_warnings = []
        field_error_counts = {}

        for result in validation_results:
            all_errors.extend(result.errors)
            all_warnings.extend(result.warnings)

            for field_name, errors in result.field_errors.items():
                if field_name not in field_error_counts:
                    field_error_counts[field_name] = 0
                field_error_counts[field_name] += len(errors)

        # Count error frequencies
        error_counts = {}
        for error in all_errors:
            error_counts[error] = error_counts.get(error, 0) + 1

        return {
            'total_records': total_records,
            'valid_records': valid_records,
            'invalid_records': invalid_records,
            'validation_rate': valid_records / total_records if total_records > 0 else 0,
            'total_errors': len(all_errors),
            'total_warnings': len(all_warnings),
            'common_errors': sorted(error_counts.items(), key=lambda x: x[1], reverse=True)[:5],
            'fields_with_errors': field_error_counts
        }