"""
Field mapping transformer for converting between different data schemas.
"""
from typing import Dict, Any, List, Optional, Callable
import re
from datetime import datetime
from .base_transformer import BaseTransformer


class FieldMapper(BaseTransformer):
    """
    Transformer that maps fields from source schema to target schema.

    Supports:
    - Simple field renaming
    - Field value transformations
    - Computed fields
    - Conditional mappings
    - Data type conversions
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the field mapper.

        Config structure:
        {
            "mappings": {
                "target_field": {
                    "source": "source_field",
                    "transform": "function_name",
                    "default": "default_value",
                    "required": True/False
                }
            },
            "computed_fields": {
                "target_field": {
                    "expression": "python_expression",
                    "dependencies": ["field1", "field2"]
                }
            }
        }
        """
        super().__init__(config)
        self.mappings = self.config.get("mappings", {})
        self.computed_fields = self.config.get("computed_fields", {})

        # Built-in transformation functions
        self.transform_functions = {
            "uppercase": lambda x: str(x).upper() if x is not None else None,
            "lowercase": lambda x: str(x).lower() if x is not None else None,
            "strip": lambda x: str(x).strip() if x is not None else None,
            "to_string": lambda x: str(x) if x is not None else None,
            "to_int": self._safe_int_convert,
            "to_float": self._safe_float_convert,
            "to_bool": self._safe_bool_convert,
            "format_phone": self._format_phone,
            "format_email": self._format_email,
            "parse_date": self._parse_date,
            "format_currency": self._format_currency,
        }

    def transform(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform a single record using the configured field mappings.

        Args:
            data: Input data record

        Returns:
            Transformed data record
        """
        result = {}

        # Apply field mappings
        for target_field, mapping_config in self.mappings.items():
            try:
                value = self._apply_field_mapping(data, target_field, mapping_config)
                if value is not None or mapping_config.get("include_null", False):
                    result[target_field] = value
            except Exception as e:
                self.logger.error(f"Error mapping field {target_field}: {e}")
                if mapping_config.get("required", False):
                    raise

        # Apply computed fields
        for target_field, compute_config in self.computed_fields.items():
            try:
                value = self._compute_field(data, result, compute_config)
                result[target_field] = value
            except Exception as e:
                self.logger.error(f"Error computing field {target_field}: {e}")

        return result

    def _apply_field_mapping(self, data: Dict[str, Any], target_field: str, mapping_config: Dict[str, Any]) -> Any:
        """Apply a single field mapping."""
        source_field = mapping_config.get("source")
        transform_func = mapping_config.get("transform")
        default_value = mapping_config.get("default")

        # Get source value
        if source_field:
            # Support nested field access with dot notation
            value = self._get_nested_value(data, source_field)
        else:
            value = default_value

        # Use default if value is None
        if value is None:
            value = default_value

        # Apply transformation function
        if transform_func and value is not None:
            if transform_func in self.transform_functions:
                value = self.transform_functions[transform_func](value)
            else:
                self.logger.warning(f"Unknown transform function: {transform_func}")

        return value

    def _compute_field(self, source_data: Dict[str, Any], target_data: Dict[str, Any], compute_config: Dict[str, Any]) -> Any:
        """Compute a field value using an expression."""
        expression = compute_config.get("expression", "")
        dependencies = compute_config.get("dependencies", [])

        # Create context with source and target data
        context = {
            "source": source_data,
            "target": target_data,
            "datetime": datetime,
            "re": re,
        }

        # Add dependency fields to context
        for dep in dependencies:
            context[dep] = self._get_nested_value(source_data, dep)

        # Evaluate expression safely
        try:
            return eval(expression, {"__builtins__": {}}, context)
        except Exception as e:
            self.logger.error(f"Error evaluating expression '{expression}': {e}")
            return None

    def _get_nested_value(self, data: Dict[str, Any], field_path: str) -> Any:
        """Get value from nested dictionary using dot notation."""
        keys = field_path.split('.')
        value = data

        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return None

        return value

    # Utility transformation functions
    def _safe_int_convert(self, value: Any) -> Optional[int]:
        """Safely convert value to integer."""
        if value is None:
            return None
        try:
            return int(float(str(value)))
        except (ValueError, TypeError):
            return None

    def _safe_float_convert(self, value: Any) -> Optional[float]:
        """Safely convert value to float."""
        if value is None:
            return None
        try:
            return float(str(value))
        except (ValueError, TypeError):
            return None

    def _safe_bool_convert(self, value: Any) -> Optional[bool]:
        """Safely convert value to boolean."""
        if value is None:
            return None
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ('true', '1', 'yes', 'on')
        return bool(value)

    def _format_phone(self, value: Any) -> Optional[str]:
        """Format phone number."""
        if not value:
            return None

        # Remove all non-digit characters
        digits = re.sub(r'\D', '', str(value))

        # Format US phone numbers
        if len(digits) == 10:
            return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
        elif len(digits) == 11 and digits[0] == '1':
            return f"+1 ({digits[1:4]}) {digits[4:7]}-{digits[7:]}"
        else:
            return str(value)  # Return original if can't format

    def _format_email(self, value: Any) -> Optional[str]:
        """Format and validate email address."""
        if not value:
            return None

        email = str(value).strip().lower()

        # Basic email validation
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if re.match(email_pattern, email):
            return email
        else:
            self.logger.warning(f"Invalid email format: {value}")
            return str(value)  # Return original if invalid

    def _parse_date(self, value: Any) -> Optional[str]:
        """Parse date string into ISO format."""
        if not value:
            return None

        # Common date formats to try
        date_formats = [
            '%Y-%m-%d',
            '%m/%d/%Y',
            '%d/%m/%Y',
            '%Y-%m-%d %H:%M:%S',
            '%m/%d/%Y %H:%M:%S',
        ]

        for fmt in date_formats:
            try:
                dt = datetime.strptime(str(value), fmt)
                return dt.isoformat()
            except ValueError:
                continue

        self.logger.warning(f"Could not parse date: {value}")
        return str(value)  # Return original if can't parse

    def _format_currency(self, value: Any) -> Optional[float]:
        """Format currency value."""
        if not value:
            return None

        # Remove currency symbols and commas
        cleaned = re.sub(r'[$,]', '', str(value))

        try:
            return round(float(cleaned), 2)
        except (ValueError, TypeError):
            return None
