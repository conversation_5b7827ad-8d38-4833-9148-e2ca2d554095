"""
ETL (Extract, Transform, Load) package for the transfer hub.

This package contains all ETL-related functionality organized into:
- extractors: Data extraction from various sources (Salesforce, APIs, etc.)
- transformers: Data transformation and validation logic
- loaders: Data loading into target systems (PostgreSQL, etc.)
- pipelines: ETL pipeline orchestration and workflow management
- utils: Common utilities and helper functions for ETL operations
"""

__version__ = "1.0.0"
