"""
Staged data endpoints for the ETL Migration System.
"""
from typing import Optional
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func
import math

from app.core.database import get_db
from app.config.settings import settings
from app.models.staged_data import StagedData
from app.schemas.staged_data import (
    StagedDataResponse,
    StagedDataUpdate,
    StagedDataListResponse,
    StagedDataSummary
)

router = APIRouter()


@router.get("/staged/{object_name}", response_model=StagedDataListResponse)
async def get_staged_data(
    object_name: str,
    page: int = Query(1, ge=1, description="Page number (1-based)"),
    limit: int = Query(50, ge=1, le=1000, description="Number of records per page"),
    migration_job_id: Optional[UUID] = Query(None, description="Filter by migration job ID"),
    sync_status: Optional[str] = Query(None, description="Filter by sync status"),
    validation_status: Optional[str] = Query(None, description="Filter by validation status"),
    user_modified: Optional[bool] = Query(None, description="Filter by user modifications"),
    db: Session = Depends(get_db)
):
    """
    Get paginated staged data for a specific object type.

    Supports filtering by:
    - Migration job ID
    - Sync status (pending, synced, failed)
    - Validation status (valid, invalid, warning)
    - User modifications (true/false)
    """
    # Build query
    query = db.query(StagedData).filter(StagedData.object_name == object_name)

    # Apply filters
    if migration_job_id:
        query = query.filter(StagedData.migration_job_id == migration_job_id)

    if sync_status:
        query = query.filter(StagedData.sync_status == sync_status)

    if validation_status:
        query = query.filter(StagedData.validation_status == validation_status)

    if user_modified is not None:
        query = query.filter(StagedData.user_modified == user_modified)

    # Get total count
    total = query.count()

    # Calculate pagination
    pages = math.ceil(total / limit) if total > 0 else 1
    offset = (page - 1) * limit

    # Apply pagination and ordering
    staged_records = (
        query
        .order_by(StagedData.created_at.desc())
        .offset(offset)
        .limit(limit)
        .all()
    )

    # Convert to response models
    items = [StagedDataResponse.from_orm(record) for record in staged_records]

    return StagedDataListResponse(
        items=items,
        total=total,
        page=page,
        limit=limit,
        pages=pages,
        has_next=page < pages,
        has_prev=page > 1
    )


@router.patch("/staged/{object_name}/{record_id}", response_model=StagedDataResponse)
async def update_staged_record(
    object_name: str,
    record_id: UUID,
    update_data: StagedDataUpdate,
    db: Session = Depends(get_db)
):
    """
    Update a staged data record with user modifications.

    This endpoint allows users to edit staged data before final sync.
    Changes are tracked with user ID and timestamp.
    """
    # Find the staged record
    staged_record = (
        db.query(StagedData)
        .filter(
            StagedData.id == record_id,
            StagedData.object_name == object_name
        )
        .first()
    )

    if not staged_record:
        raise HTTPException(
            status_code=404,
            detail=f"Staged record {record_id} not found for object {object_name}"
        )

    # Check if record is already synced
    if staged_record.sync_status == "synced":
        raise HTTPException(
            status_code=400,
            detail="Cannot modify record that has already been synced"
        )

    try:
        # Apply user modifications
        for field_name, new_value in update_data.field_updates.items():
            staged_record.apply_user_edit(
                field_name=field_name,
                new_value=new_value,
                user_id=update_data.user_id
            )

        # Reset sync status if it was failed (user might have fixed the issue)
        if staged_record.sync_status == "failed":
            staged_record.sync_status = "pending"
            staged_record.sync_error = None

        db.commit()
        db.refresh(staged_record)

        return StagedDataResponse.from_orm(staged_record)

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update staged record: {str(e)}"
        )


@router.get("/staged/{object_name}/summary", response_model=StagedDataSummary)
async def get_staged_data_summary(
    object_name: str,
    migration_job_id: Optional[UUID] = Query(None, description="Filter by migration job ID"),
    db: Session = Depends(get_db)
):
    """
    Get summary statistics for staged data.

    Returns counts for different statuses and user modifications.
    """
    # Build base query
    query = db.query(StagedData).filter(StagedData.object_name == object_name)

    if migration_job_id:
        query = query.filter(StagedData.migration_job_id == migration_job_id)

    # Get various counts
    total_records = query.count()

    pending_sync = query.filter(StagedData.sync_status == "pending").count()
    synced = query.filter(StagedData.sync_status == "synced").count()
    failed = query.filter(StagedData.sync_status == "failed").count()

    user_modified = query.filter(StagedData.user_modified == True).count()
    validation_errors = query.filter(StagedData.validation_status == "invalid").count()

    return StagedDataSummary(
        total_records=total_records,
        pending_sync=pending_sync,
        synced=synced,
        failed=failed,
        user_modified=user_modified,
        validation_errors=validation_errors
    )


@router.get("/staged/{object_name}/{record_id}", response_model=StagedDataResponse)
async def get_staged_record(
    object_name: str,
    record_id: UUID,
    db: Session = Depends(get_db)
):
    """
    Get a specific staged data record.

    Returns detailed information about a single staged record.
    """
    staged_record = (
        db.query(StagedData)
        .filter(
            StagedData.id == record_id,
            StagedData.object_name == object_name
        )
        .first()
    )

    if not staged_record:
        raise HTTPException(
            status_code=404,
            detail=f"Staged record {record_id} not found for object {object_name}"
        )

    return StagedDataResponse.from_orm(staged_record)
