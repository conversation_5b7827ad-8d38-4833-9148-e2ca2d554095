"""
Field mapping endpoints for the ETL Migration System.
"""
from typing import List, Optional
from uuid import UUID
from fastapi import APIRouter, HTTPException, Depends, Query
from sqlalchemy.orm import Session
import json
from datetime import datetime

from app.core.database import get_db
from app.models.field_mapping import FieldMapping as FieldMappingModel
from app.models.target_schema import TargetSchema as TargetSchemaModel
from app.schemas.field_mapping import (
    FieldMappingCreate,
    FieldMappingUpdate,
    FieldMappingResponse,
    FieldMappingListResponse,
    ApplyMappingRequest,
    ApplyMappingResponse,
    FieldMappingRule
)
from app.etl.transformers.field_mapper import FieldMapper

router = APIRouter()


def db_mapping_to_response(db_mapping: FieldMappingModel) -> FieldMappingResponse:
    """Convert database model to response model with proper JSON parsing"""
    field_mappings_data = []
    if db_mapping.field_mappings:
        try:
            # Convert the stored JSON to FieldMappingRule objects
            mappings_json = db_mapping.field_mappings
            field_mappings_data = [FieldMappingRule(**mapping) for mapping in mappings_json]
        except (TypeError, ValueError) as e:
            print(f"Error parsing field mappings: {e}")
            field_mappings_data = []

    return FieldMappingResponse(
        id=db_mapping.id,
        mapping_name=db_mapping.mapping_name,
        description=db_mapping.description or "",
        source_object=db_mapping.source_object,
        target_schema_id=db_mapping.target_schema_id,
        field_mappings=field_mappings_data,
        remove_unmapped_fields=db_mapping.remove_unmapped_fields,
        created_by=db_mapping.created_by,
        created_at=db_mapping.created_at,
        updated_at=db_mapping.updated_at
    )


@router.get("/", response_model=FieldMappingListResponse)
async def get_field_mappings(
    target_schema_id: Optional[str] = Query(None, description="Filter by target schema ID"),
    source_object: Optional[str] = Query(None, description="Filter by source object"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(50, ge=1, le=100, description="Items per page"),
    db: Session = Depends(get_db)
):
    """
    Get field mappings with optional filtering and pagination
    """
    try:
        query = db.query(FieldMappingModel)
        
        if target_schema_id:
            query = query.filter(FieldMappingModel.target_schema_id == target_schema_id)
        
        if source_object:
            query = query.filter(FieldMappingModel.source_object == source_object)
        
        # Get total count
        total = query.count()
        
        # Apply pagination
        offset = (page - 1) * limit
        mappings = query.offset(offset).limit(limit).all()
        
        return FieldMappingListResponse(
            mappings=[db_mapping_to_response(mapping) for mapping in mappings],
            total=total,
            page=page,
            limit=limit
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving field mappings: {str(e)}")


@router.get("/{mapping_id}", response_model=FieldMappingResponse)
async def get_field_mapping(mapping_id: UUID, db: Session = Depends(get_db)):
    """
    Get a specific field mapping by ID
    """
    try:
        mapping = db.query(FieldMappingModel).filter(FieldMappingModel.id == mapping_id).first()
        if not mapping:
            raise HTTPException(status_code=404, detail=f"Field mapping '{mapping_id}' not found")
        return db_mapping_to_response(mapping)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving field mapping: {str(e)}")


@router.post("/", response_model=FieldMappingResponse)
async def create_field_mapping(mapping: FieldMappingCreate, db: Session = Depends(get_db)):
    """
    Create a new field mapping
    """
    try:
        # Verify target schema exists
        target_schema = db.query(TargetSchemaModel).filter(
            TargetSchemaModel.id == mapping.target_schema_id
        ).first()
        if not target_schema:
            raise HTTPException(
                status_code=404, 
                detail=f"Target schema '{mapping.target_schema_id}' not found"
            )
        
        # Convert Pydantic models to dict for JSON serialization
        field_mappings_dict = [rule.dict() for rule in mapping.field_mappings]

        db_mapping = FieldMappingModel(
            mapping_name=mapping.mapping_name,
            description=mapping.description,
            source_object=mapping.source_object,
            target_schema_id=mapping.target_schema_id,
            field_mappings=field_mappings_dict,
            remove_unmapped_fields=mapping.remove_unmapped_fields,
            created_by=mapping.created_by,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        db.add(db_mapping)
        db.commit()
        db.refresh(db_mapping)

        return db_mapping_to_response(db_mapping)
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error creating field mapping: {str(e)}")


@router.put("/{mapping_id}", response_model=FieldMappingResponse)
async def update_field_mapping(
    mapping_id: UUID,
    mapping_update: FieldMappingUpdate,
    db: Session = Depends(get_db)
):
    """
    Update an existing field mapping
    """
    try:
        # Get existing mapping
        db_mapping = db.query(FieldMappingModel).filter(FieldMappingModel.id == mapping_id).first()
        if not db_mapping:
            raise HTTPException(status_code=404, detail=f"Field mapping '{mapping_id}' not found")
        
        # Update fields
        if mapping_update.mapping_name is not None:
            db_mapping.mapping_name = mapping_update.mapping_name
        if mapping_update.description is not None:
            db_mapping.description = mapping_update.description
        if mapping_update.field_mappings is not None:
            field_mappings_dict = [rule.dict() for rule in mapping_update.field_mappings]
            db_mapping.field_mappings = field_mappings_dict
        if mapping_update.remove_unmapped_fields is not None:
            db_mapping.remove_unmapped_fields = mapping_update.remove_unmapped_fields
        
        db_mapping.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(db_mapping)

        return db_mapping_to_response(db_mapping)
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error updating field mapping: {str(e)}")


@router.delete("/{mapping_id}")
async def delete_field_mapping(mapping_id: UUID, db: Session = Depends(get_db)):
    """
    Delete a field mapping
    """
    try:
        mapping = db.query(FieldMappingModel).filter(FieldMappingModel.id == mapping_id).first()
        if not mapping:
            raise HTTPException(status_code=404, detail=f"Field mapping '{mapping_id}' not found")
        
        db.delete(mapping)
        db.commit()
        
        return {"message": f"Field mapping '{mapping_id}' deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error deleting field mapping: {str(e)}")


@router.post("/{mapping_id}/apply", response_model=ApplyMappingResponse)
async def apply_field_mapping(
    mapping_id: UUID,
    request: ApplyMappingRequest,
    db: Session = Depends(get_db)
):
    """
    Apply a field mapping to transform data
    """
    try:
        # Get the field mapping
        mapping = db.query(FieldMappingModel).filter(FieldMappingModel.id == mapping_id).first()
        if not mapping:
            raise HTTPException(status_code=404, detail=f"Field mapping '{mapping_id}' not found")
        
        # Convert field mappings to transformer config
        transformer_config = {
            "mappings": {},
            "remove_unmapped": mapping.remove_unmapped_fields
        }
        
        for rule in mapping.field_mappings:
            rule_dict = rule if isinstance(rule, dict) else rule.dict()
            transformer_config["mappings"][rule_dict["target_field"]] = {
                "source": rule_dict["source_field"],
                "default": rule_dict.get("default_value"),
                "required": rule_dict.get("required", False)
            }
            
            # Add transformation if specified
            if rule_dict.get("transformation"):
                transformer_config["mappings"][rule_dict["target_field"]]["transform"] = \
                    rule_dict["transformation"]["function"]
        
        # Apply transformation
        field_mapper = FieldMapper(transformer_config)
        transformed_data = field_mapper.transform_batch(request.data)
        
        # Create summary
        mapping_summary = {
            "total_records": len(request.data),
            "successfully_mapped": len(transformed_data),
            "fields_mapped": len(transformer_config["mappings"]),
            "fields_removed": 0  # TODO: Calculate actual removed fields
        }
        
        return ApplyMappingResponse(
            transformed_data=transformed_data,
            mapping_summary=mapping_summary,
            errors=[]
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error applying field mapping: {str(e)}")
