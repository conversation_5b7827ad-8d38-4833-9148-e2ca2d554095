"""
Migration endpoints for the ETL Migration System.
"""
from typing import Optional, List, Dict, Any
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Header
from sqlalchemy.orm import Session
from pydantic import BaseModel
import logging

from app.core.database import get_db
from app.models.migration_job import MigrationJob, JobStatus
from app.models.raw_data import RawData
from app.models.staged_data import StagedData
from sqlalchemy import func
from app.schemas.migration_job import (
    StartMigrationRequest,
    StartMigrationResponse,
    MigrationJobResponse,
    MigrationJobStatus
)
from app.etl.extractors.salesforce_extractor import SalesforceExtractor
from datetime import datetime
# from app.services.migration_service import MigrationService  # Will create this next

logger = logging.getLogger(__name__)


class SalesforceObjectsResponse(BaseModel):
    objects: List[Dict[str, Any]]
    message: str


class SalesforceDataResponse(BaseModel):
    object_name: str
    records: List[Dict[str, Any]]
    total_count: int
    message: str

router = APIRouter()


class CreateStagedDataRequest(BaseModel):
    """Request model for creating staged data from transformed records."""
    migration_job_id: str
    object_name: str
    transformed_records: List[Dict[str, Any]]
    field_mappings: Optional[List[Dict[str, Any]]] = None
    transformation_config: Optional[Dict[str, Any]] = None


class CreateStagedDataResponse(BaseModel):
    """Response model for staged data creation."""
    success: bool
    migration_job_id: str
    staged_records_created: int
    message: str


# Bearer Token Endpoints (Temporary for testing)
@router.get("/salesforce-bearer/test-connection")
async def test_salesforce_bearer_connection(
    authorization: str = Header(None)
):
    """
    Test Salesforce connection using Bearer token (temporary endpoint).

    Expects Authorization header with Bearer token.
    """
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(
            status_code=401,
            detail="Authorization header with Bearer token required"
        )

    # Extract token from "Bearer <token>"
    bearer_token = authorization.split(" ")[1]

    try:
        # Test connection with Bearer token
        extractor = SalesforceExtractor(bearer_token=bearer_token)

        if extractor.authenticate():
            # Try to get basic org info to verify connection
            try:
                # Test with a simple query
                result = extractor.sf.query("SELECT Id, Name FROM Organization LIMIT 1")
                org_info = result['records'][0] if result['records'] else {}

                return {
                    "status": "success",
                    "message": "Successfully connected to Salesforce using Bearer token",
                    "authenticated": True,
                    "org_info": {
                        "org_id": org_info.get('Id', 'Unknown'),
                        "org_name": org_info.get('Name', 'Unknown'),
                        "instance_url": extractor.sf.sf_instance
                    }
                }
            except Exception as query_error:
                # If org query fails, still return success if authentication worked
                return {
                    "status": "success",
                    "message": "Successfully connected to Salesforce using Bearer token",
                    "authenticated": True,
                    "org_info": {
                        "instance_url": extractor.sf.sf_instance,
                        "note": f"Basic connection successful, but org query failed: {str(query_error)}"
                    }
                }
        else:
            raise HTTPException(
                status_code=401,
                detail="Failed to authenticate with Salesforce using provided Bearer token"
            )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error testing Salesforce connection: {str(e)}"
        )


@router.get("/salesforce-bearer/objects", response_model=SalesforceObjectsResponse)
async def get_salesforce_bearer_objects(
    authorization: str = Header(None)
):
    """
    Get all available Salesforce objects from the org using Bearer token (temporary endpoint).
    """
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(
            status_code=401,
            detail="Authorization header with Bearer token required"
        )

    bearer_token = authorization.split(" ")[1]

    try:
        extractor = SalesforceExtractor(bearer_token=bearer_token)

        if not extractor.authenticate():
            raise HTTPException(
                status_code=401,
                detail="Failed to authenticate with Salesforce"
            )

        # Get all available objects from Salesforce org
        try:
            objects = extractor.get_all_objects()
            return SalesforceObjectsResponse(
                objects=objects,
                message=f"Retrieved {len(objects)} Salesforce objects from your org"
            )
        except Exception as describe_error:
            # If full describe fails, return common objects as fallback
            logger.warning(f"Full describe failed, using fallback objects: {describe_error}")

            fallback_objects = [
                {"name": "Account", "label": "Account", "labelPlural": "Accounts", "queryable": True, "createable": True, "updateable": True, "deletable": True, "custom": False, "keyPrefix": "001"},
                {"name": "Contact", "label": "Contact", "labelPlural": "Contacts", "queryable": True, "createable": True, "updateable": True, "deletable": True, "custom": False, "keyPrefix": "003"},
                {"name": "Opportunity", "label": "Opportunity", "labelPlural": "Opportunities", "queryable": True, "createable": True, "updateable": True, "deletable": True, "custom": False, "keyPrefix": "006"},
                {"name": "Product2", "label": "Product", "labelPlural": "product", "queryable": True, "createable": True, "updateable": True, "deletable": True, "custom": False, "keyPrefix": "01t"},
                {"name": "Lead", "label": "Lead", "labelPlural": "Leads", "queryable": True, "createable": True, "updateable": True, "deletable": True, "custom": False, "keyPrefix": "00Q"},
            ]

            return SalesforceObjectsResponse(
                objects=fallback_objects,
                message=f"Retrieved {len(fallback_objects)} common Salesforce objects (fallback mode due to: {str(describe_error)})"
            )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving Salesforce objects: {str(e)}"
        )


@router.get("/salesforce-bearer/data/{object_name}", response_model=SalesforceDataResponse)
async def get_salesforce_bearer_data(
    object_name: str,
    authorization: str = Header(None),
    limit: int = 10
):
    """
    Extract data from a specific Salesforce object using Bearer token (temporary endpoint).
    """
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(
            status_code=401,
            detail="Authorization header with Bearer token required"
        )

    bearer_token = authorization.split(" ")[1]

    try:
        extractor = SalesforceExtractor(bearer_token=bearer_token)

        if not extractor.authenticate():
            raise HTTPException(
                status_code=401,
                detail="Failed to authenticate with Salesforce"
            )

        # Extract records from the specified object
        records = extractor.extract_records(
            object_name=object_name,
            limit=limit
        )

        return SalesforceDataResponse(
            object_name=object_name,
            records=records,
            total_count=len(records),
            message=f"Successfully extracted {len(records)} records from {object_name}"
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error extracting data from {object_name}: {str(e)}"
        )


# OAuth Credential-based Endpoints
@router.get("/salesforce/test-connection")
async def test_salesforce_connection():
    """
    Test Salesforce connection using OAuth credentials from environment.
    """
    import logging
    logger = logging.getLogger(__name__)

    try:
        logger.info("Starting Salesforce connection test...")

        # Create extractor without bearer token (uses credentials)
        extractor = SalesforceExtractor()
        logger.info("Created SalesforceExtractor instance")

        logger.info("Attempting authentication...")
        if extractor.authenticate():
            logger.info("Authentication successful, testing with org query...")

            # Try to get basic org info to verify connection
            try:
                result = extractor.sf.query("SELECT Id, Name FROM Organization LIMIT 1")
                org_info = result['records'][0] if result['records'] else {}
                logger.info(f"Org query successful: {org_info}")

                return {
                    "status": "success",
                    "message": "Successfully connected to Salesforce using OAuth credentials",
                    "authenticated": True,
                    "org_info": {
                        "org_id": org_info.get('Id', 'Unknown'),
                        "org_name": org_info.get('Name', 'Unknown'),
                        "instance_url": extractor.sf.sf_instance
                    }
                }
            except Exception as query_error:
                logger.warning(f"Org query failed but authentication was successful: {query_error}")
                return {
                    "status": "success",
                    "message": "Successfully connected to Salesforce using OAuth credentials",
                    "authenticated": True,
                    "org_info": {
                        "instance_url": extractor.sf.sf_instance,
                        "note": f"Basic connection successful, but org query failed: {str(query_error)}"
                    }
                }
        else:
            logger.error("Authentication failed")
            raise HTTPException(
                status_code=401,
                detail="Failed to authenticate with Salesforce using OAuth credentials"
            )

    except Exception as e:
        logger.error(f"Error in test_salesforce_connection: {e}")
        logger.error(f"Error type: {type(e).__name__}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

        raise HTTPException(
            status_code=500,
            detail=f"Error testing Salesforce connection: {str(e)}"
        )


@router.get("/salesforce/objects", response_model=SalesforceObjectsResponse)
async def get_salesforce_objects():
    """
    Get all available Salesforce objects using OAuth credentials from environment.
    """
    try:
        # Create extractor without bearer token (uses credentials)
        extractor = SalesforceExtractor()

        if not extractor.authenticate():
            raise HTTPException(
                status_code=401,
                detail="Failed to authenticate with Salesforce using OAuth credentials"
            )

        # Get all available objects from Salesforce org
        try:
            objects = extractor.get_all_objects()
            return SalesforceObjectsResponse(
                objects=objects,
                message=f"Retrieved {len(objects)} Salesforce objects using OAuth credentials"
            )
        except Exception as describe_error:
            # If full describe fails, return common objects as fallback
            logger.warning(f"Full describe failed, using fallback objects: {describe_error}")

            fallback_objects = [
                {"name": "Account", "label": "Account", "labelPlural": "Accounts", "queryable": True, "createable": True, "updateable": True, "deletable": True, "custom": False, "keyPrefix": "001"},
                {"name": "Contact", "label": "Contact", "labelPlural": "Contacts", "queryable": True, "createable": True, "updateable": True, "deletable": True, "custom": False, "keyPrefix": "003"},
                {"name": "Opportunity", "label": "Opportunity", "labelPlural": "Opportunities", "queryable": True, "createable": True, "updateable": True, "deletable": True, "custom": False, "keyPrefix": "006"},
                {"name": "Product2", "label": "Product", "labelPlural": "product", "queryable": True, "createable": True, "updateable": True, "deletable": True, "custom": False, "keyPrefix": "01t"},
                {"name": "Lead", "label": "Lead", "labelPlural": "Leads", "queryable": True, "createable": True, "updateable": True, "deletable": True, "custom": False, "keyPrefix": "00Q"},
            ]

            return SalesforceObjectsResponse(
                objects=fallback_objects,
                message=f"Retrieved {len(fallback_objects)} common Salesforce objects (fallback mode due to: {str(describe_error)})"
            )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving Salesforce objects: {str(e)}"
        )


@router.get("/salesforce/objects/{object_name}/fields")
async def get_object_fields(object_name: str):
    """
    Get all fields for a specific Salesforce object using OAuth credentials from environment.
    """
    try:
        # Create extractor without bearer token (uses credentials)
        extractor = SalesforceExtractor()

        if not extractor.authenticate():
            raise HTTPException(
                status_code=401,
                detail="Failed to authenticate with Salesforce using OAuth credentials"
            )

        # Use Salesforce describe API to get field metadata
        try:
            describe_result = extractor.sf.__getattr__(object_name).describe()

            fields = []
            for field in describe_result['fields']:
                fields.append({
                    "name": field['name'],
                    "label": field['label'],
                    "type": field['type'],
                    "required": not field['nillable'] and not field.get('defaultedOnCreate', False),
                    "custom": field['custom'],
                    "updateable": field['updateable'],
                    "createable": field['createable'],
                    "length": field.get('length', 0),
                    "picklistValues": [pv['value'] for pv in field.get('picklistValues', [])] if field['type'] == 'picklist' else []
                })

            return {
                "object_name": object_name,
                "fields": fields,
                "total_fields": len(fields),
                "message": f"Successfully retrieved {len(fields)} fields for {object_name}"
            }

        except Exception as describe_error:
            logger.error(f"Failed to describe {object_name}: {describe_error}")
            raise HTTPException(
                status_code=400,
                detail=f"Failed to get field information for {object_name}: {str(describe_error)}"
            )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving fields for {object_name}: {str(e)}"
        )

@router.post("/salesforce/data/{object_name}")
async def extract_selected_data(
    object_name: str,
    request: dict,
    db: Session = Depends(get_db)
):
    """
    Extract specific fields from a Salesforce object and optionally create migration job.
    """
    try:
        # Create extractor without bearer token (uses credentials)
        extractor = SalesforceExtractor()

        if not extractor.authenticate():
            raise HTTPException(
                status_code=401,
                detail="Failed to authenticate with Salesforce using OAuth credentials"
            )

        # Get fields and limit from request
        fields = request.get('fields', [])
        limit = request.get('limit', 100)
        create_job = request.get('create_job', False)  # Option to create migration job

        if not fields:
            raise HTTPException(
                status_code=400,
                detail="No fields specified for extraction"
            )

        # Build SOQL query with selected fields
        fields_str = ", ".join(fields)
        soql = f"SELECT {fields_str} FROM {object_name} LIMIT {limit}"

        logger.info(f"Executing SOQL: {soql}")

        # Execute query
        result = extractor.sf.query(soql)

        # Clean up the records (remove attributes)
        records = []
        for record in result['records']:
            clean_record = {k: v for k, v in record.items() if k != 'attributes'}
            records.append(clean_record)

        migration_job_id = None

        # Create migration job if requested
        if create_job:
            migration_job = MigrationJob(
                object_name=object_name,
                job_name=f"Extract {object_name} - {len(records)} records",
                status=JobStatus.EXTRACTING,
                source_config={
                    "object_name": object_name,
                    "fields": fields,
                    "limit": limit
                },
                records_extracted=len(records),
                started_at=datetime.utcnow()
            )
            db.add(migration_job)
            db.commit()
            db.refresh(migration_job)
            migration_job_id = str(migration_job.id)

            # Store raw data records
            for record in records:
                raw_data = RawData(
                    migration_job_id=migration_job.id,
                    object_name=object_name,
                    salesforce_id=record.get('Id', ''),
                    raw_payload=record,
                    salesforce_last_modified=record.get('LastModifiedDate')
                )
                db.add(raw_data)

            # Update job status
            migration_job.status = JobStatus.COMPLETED
            migration_job.completed_at = datetime.utcnow()
            migration_job.update_progress(100, "Extraction completed")
            db.commit()

        response_data = {
            "object_name": object_name,
            "fields": fields,
            "records": records,
            "total_count": len(records),
            "message": f"Successfully extracted {len(records)} records with {len(fields)} fields from {object_name}"
        }

        if migration_job_id:
            response_data["migration_job_id"] = migration_job_id

        return response_data

    except Exception as e:
        if create_job and 'migration_job' in locals():
            # Update job status to failed
            migration_job.status = JobStatus.FAILED
            migration_job.error_message = str(e)
            migration_job.completed_at = datetime.utcnow()
            db.commit()

        logger.error(f"Error extracting selected data from {object_name}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error extracting data from {object_name}: {str(e)}"
        )

@router.get("/salesforce/data/{object_name}", response_model=SalesforceDataResponse)
async def get_salesforce_data(
    object_name: str,
    limit: int = 10
):
    """
    Extract data from a specific Salesforce object using OAuth credentials from environment.
    (Legacy endpoint - use POST /salesforce/data/{object_name} for field selection)
    """
    try:
        # Create extractor without bearer token (uses credentials)
        extractor = SalesforceExtractor()

        if not extractor.authenticate():
            raise HTTPException(
                status_code=401,
                detail="Failed to authenticate with Salesforce using OAuth credentials"
            )

        # Extract records from the specified object
        records = extractor.extract_records(
            object_name=object_name,
            limit=limit
        )

        return SalesforceDataResponse(
            object_name=object_name,
            records=records,
            total_count=len(records),
            message=f"Successfully extracted {len(records)} records from {object_name} using OAuth credentials"
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error extracting data from {object_name}: {str(e)}"
        )


@router.post("/start-migration", response_model=StartMigrationResponse)
async def start_migration(
    request: StartMigrationRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Start a new ETL migration job.

    This endpoint:
    1. Creates a new migration job record
    2. Starts the ETL process in the background
    3. Returns immediately with job ID and status
    """
    try:
        # Create migration job record
        migration_job = MigrationJob(
            object_name=request.object_name,
            job_name=request.job_name,
            source_config=request.source_config,
            target_config=request.target_config,
            transformation_config=request.transformation_config,
            status=JobStatus.PENDING
        )

        db.add(migration_job)
        db.commit()
        db.refresh(migration_job)

        # Start background ETL process
        # TODO: Replace with actual migration service
        # migration_service = MigrationService()
        # background_tasks.add_task(
        #     migration_service.run_etl_pipeline,
        #     migration_job.id,
        #     db
        # )

        # For now, just update status to indicate it's started
        migration_job.status = JobStatus.PENDING
        migration_job.current_step = "Migration job queued for processing"
        db.commit()

        return StartMigrationResponse(
            job_id=migration_job.id,
            message=f"Migration job for {request.object_name} started successfully",
            status=migration_job.status
        )

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start migration: {str(e)}"
        )


@router.get("/migration-status/{job_id}", response_model=MigrationJobStatus)
async def get_migration_status(
    job_id: UUID,
    db: Session = Depends(get_db)
):
    """
    Get the status of a migration job.

    Returns current progress, status, and statistics for the specified job.
    """
    migration_job = db.query(MigrationJob).filter(MigrationJob.id == job_id).first()

    if not migration_job:
        raise HTTPException(
            status_code=404,
            detail=f"Migration job {job_id} not found"
        )

    return MigrationJobStatus(
        id=migration_job.id,
        status=migration_job.status,
        progress_percentage=migration_job.progress_percentage,
        current_step=migration_job.current_step,
        error_message=migration_job.error_message,
        records_extracted=migration_job.records_extracted,
        records_transformed=migration_job.records_transformed,
        records_staged=migration_job.records_staged,
        records_synced=migration_job.records_synced,
        records_failed=migration_job.records_failed
    )


@router.get("/migration-job/{job_id}", response_model=MigrationJobResponse)
async def get_migration_job(
    job_id: UUID,
    db: Session = Depends(get_db)
):
    """
    Get detailed information about a migration job.

    Returns complete job details including configuration, timestamps, and statistics.
    """
    migration_job = db.query(MigrationJob).filter(MigrationJob.id == job_id).first()

    if not migration_job:
        raise HTTPException(
            status_code=404,
            detail=f"Migration job {job_id} not found"
        )

    return MigrationJobResponse.from_orm(migration_job)


@router.get("/migration-jobs", response_model=list[MigrationJobResponse])
async def list_migration_jobs(
    object_name: Optional[str] = None,
    status: Optional[JobStatus] = None,
    limit: int = 50,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """
    List migration jobs with optional filtering.

    Supports filtering by object name and status, with pagination.
    """
    query = db.query(MigrationJob)

    if object_name:
        query = query.filter(MigrationJob.object_name == object_name)

    if status:
        query = query.filter(MigrationJob.status == status)

    # Order by most recent first
    query = query.order_by(MigrationJob.created_at.desc())

    # Apply pagination
    migration_jobs = query.offset(offset).limit(limit).all()

    return [MigrationJobResponse.from_orm(job) for job in migration_jobs]


@router.post("/migration-job/{job_id}/cancel")
async def cancel_migration_job(
    job_id: UUID,
    db: Session = Depends(get_db)
):
    """
    Cancel a running migration job.

    Sets the job status to cancelled if it's currently running.
    """
    migration_job = db.query(MigrationJob).filter(MigrationJob.id == job_id).first()

    if not migration_job:
        raise HTTPException(
            status_code=404,
            detail=f"Migration job {job_id} not found"
        )

    if migration_job.is_completed:
        raise HTTPException(
            status_code=400,
            detail=f"Cannot cancel job {job_id}: job is already completed"
        )

    migration_job.status = JobStatus.CANCELLED
    migration_job.current_step = "Job cancelled by user"
    migration_job.error_message = "Job was cancelled by user request"

    db.commit()

    return {"message": f"Migration job {job_id} has been cancelled"}


@router.post("/staged-data", response_model=CreateStagedDataResponse)
async def create_staged_data(
    request: CreateStagedDataRequest,
    db: Session = Depends(get_db)
):
    """
    Create staged data records from transformed data.

    This endpoint takes transformed data and creates staged data records
    that can be reviewed, edited, and then loaded to target systems.
    """
    try:
        # Validate migration job exists
        migration_job = db.query(MigrationJob).filter(
            MigrationJob.id == request.migration_job_id
        ).first()

        if not migration_job:
            raise HTTPException(
                status_code=404,
                detail=f"Migration job {request.migration_job_id} not found"
            )

        # Update job status to staging
        migration_job.status = JobStatus.STAGING
        migration_job.current_step = "Creating staged data"
        migration_job.transformation_config = request.transformation_config

        staged_records_created = 0

        # Create staged data records
        for record in request.transformed_records:
            # Extract source ID (usually Salesforce ID)
            source_id = record.get('Id') or record.get('salesforce_id') or record.get('source_id', '')

            # Find corresponding raw data record for lineage
            raw_data_record = None
            if source_id:
                raw_data_record = db.query(RawData).filter(
                    RawData.migration_job_id == migration_job.id,
                    RawData.salesforce_id == source_id
                ).first()

            # Create staged data record with proper lineage
            staged_data = StagedData(
                migration_job_id=migration_job.id,
                raw_data_id=raw_data_record.id if raw_data_record else None,  # Link to raw data
                object_name=request.object_name,
                source_id=source_id,
                transformed_data=record,
                original_data=record.copy(),  # Keep a copy of the original transformed data
                sync_status="pending",
                validation_status="valid"
            )

            db.add(staged_data)
            staged_records_created += 1

        # Update job statistics
        migration_job.records_staged = staged_records_created
        migration_job.status = JobStatus.COMPLETED  # Mark as completed after staging
        migration_job.current_step = "Staged data created successfully"
        migration_job.update_progress(100, "Staging completed")

        db.commit()

        return CreateStagedDataResponse(
            success=True,
            migration_job_id=request.migration_job_id,
            staged_records_created=staged_records_created,
            message=f"Successfully created {staged_records_created} staged data records"
        )

    except Exception as e:
        db.rollback()
        logger.error(f"Error creating staged data: {e}")

        # Update job status to failed
        if 'migration_job' in locals() and migration_job:
            migration_job.status = JobStatus.FAILED
            migration_job.error_message = str(e)
            migration_job.current_step = "Failed to create staged data"
            db.commit()

        raise HTTPException(
            status_code=500,
            detail=f"Failed to create staged data: {str(e)}"
        )


@router.get("/migrations")
async def get_migrations(db: Session = Depends(get_db)):
    """
    Get all migrations with combined job and staged data information.

    Returns a list of migrations with job status and staged data summary.
    """
    try:
        # Get all migration jobs with staged data counts
        migrations = db.query(MigrationJob).all()

        result = []
        for job in migrations:
            # Get staged data counts for this job
            total_staged = db.query(func.count(StagedData.id)).filter(
                StagedData.migration_job_id == job.id
            ).scalar() or 0

            pending_count = db.query(func.count(StagedData.id)).filter(
                StagedData.migration_job_id == job.id,
                StagedData.sync_status == 'pending'
            ).scalar() or 0

            synced_count = db.query(func.count(StagedData.id)).filter(
                StagedData.migration_job_id == job.id,
                StagedData.sync_status == 'completed'
            ).scalar() or 0

            failed_count = db.query(func.count(StagedData.id)).filter(
                StagedData.migration_job_id == job.id,
                StagedData.sync_status == 'failed'
            ).scalar() or 0

            # Calculate real progress based on ETL pipeline completion
            real_progress = 0
            if job.records_extracted > 0:
                real_progress = 25  # Extract completed
            if total_staged > 0:
                real_progress = 75  # Transform/Stage completed
            if synced_count > 0 and total_staged > 0:
                # Calculate sync completion percentage
                sync_percentage = (synced_count / total_staged) * 25  # 25% for sync phase
                real_progress = 75 + sync_percentage

            # Determine if migration can sync (has pending staged data)
            can_sync = (
                job.status == JobStatus.COMPLETED and
                pending_count > 0
            )

            # Determine if migration has errors
            has_errors = (
                job.status == JobStatus.FAILED or
                failed_count > 0
            )

            # Determine current step based on progress
            if real_progress == 100:
                current_step = "Sync completed"
            elif synced_count > 0:
                current_step = f"Syncing ({synced_count}/{total_staged} synced)"
            elif total_staged > 0:
                current_step = "Ready to sync"
            elif job.records_extracted > 0:
                current_step = "Staging completed"
            else:
                current_step = job.current_step or "Ready"

            migration_data = {
                "id": str(job.id),
                "job_name": job.job_name or f"Migration {job.object_name}",
                "object_name": job.object_name,
                "status": job.status.value,
                "progress_percentage": int(real_progress),  # Use calculated progress
                "current_step": current_step,  # Use calculated step
                "created_at": job.created_at.isoformat(),
                "completed_at": job.completed_at.isoformat() if job.completed_at else None,
                "records_extracted": job.records_extracted,
                "records_staged": total_staged,
                "records_synced": synced_count,
                "records_failed": failed_count,
                "error_message": job.error_message,
                "can_sync": can_sync,
                "has_errors": has_errors
            }
            result.append(migration_data)

        return {"migrations": result}

    except Exception as e:
        logger.error(f"Error getting migrations: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get migrations: {str(e)}"
        )


@router.get("/migrations/{migration_id}")
async def get_migration_details(
    migration_id: UUID,
    db: Session = Depends(get_db)
):
    """
    Get detailed information about a specific migration including staged data.
    """
    try:
        # Get migration job
        migration_job = db.query(MigrationJob).filter(
            MigrationJob.id == migration_id
        ).first()

        if not migration_job:
            raise HTTPException(
                status_code=404,
                detail=f"Migration {migration_id} not found"
            )

        # Get staged data for this migration
        staged_data = db.query(StagedData).filter(
            StagedData.migration_job_id == migration_id
        ).all()

        # Get raw data count
        raw_data_count = db.query(func.count(RawData.id)).filter(
            RawData.migration_job_id == migration_id
        ).scalar()

        return {
            "migration": {
                "id": str(migration_job.id),
                "job_name": migration_job.job_name,
                "object_name": migration_job.object_name,
                "status": migration_job.status.value,
                "progress_percentage": migration_job.progress_percentage,
                "current_step": migration_job.current_step,
                "created_at": migration_job.created_at.isoformat(),
                "started_at": migration_job.started_at.isoformat() if migration_job.started_at else None,
                "completed_at": migration_job.completed_at.isoformat() if migration_job.completed_at else None,
                "source_config": migration_job.source_config,
                "target_config": migration_job.target_config,
                "transformation_config": migration_job.transformation_config,
                "records_extracted": migration_job.records_extracted,
                "records_staged": len(staged_data),
                "raw_data_count": raw_data_count,
                "error_message": migration_job.error_message
            },
            "staged_data": [
                {
                    "id": str(record.id),
                    "source_id": record.source_id,
                    "target_id": record.target_id,
                    "transformed_data": record.transformed_data,
                    "user_modified": record.user_modified,
                    "sync_status": record.sync_status,
                    "validation_status": record.validation_status,
                    "created_at": record.created_at.isoformat()
                }
                for record in staged_data
            ]
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting migration details: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get migration details: {str(e)}"
        )


@router.delete("/migrations/{migration_id}")
async def delete_migration(
    migration_id: UUID,
    db: Session = Depends(get_db)
):
    """
    Delete a migration and all its associated data (raw data and staged data).

    This will cascade delete all related records.
    """
    try:
        # Get migration job
        migration_job = db.query(MigrationJob).filter(
            MigrationJob.id == migration_id
        ).first()

        if not migration_job:
            raise HTTPException(
                status_code=404,
                detail=f"Migration {migration_id} not found"
            )

        # Check if migration is currently running
        if migration_job.is_running:
            raise HTTPException(
                status_code=400,
                detail=f"Cannot delete migration {migration_id}: migration is currently running"
            )

        # Get counts before deletion for response
        raw_data_count = db.query(func.count(RawData.id)).filter(
            RawData.migration_job_id == migration_id
        ).scalar()

        staged_data_count = db.query(func.count(StagedData.id)).filter(
            StagedData.migration_job_id == migration_id
        ).scalar()

        # Delete migration job (will cascade to raw_data and staged_data due to relationships)
        db.delete(migration_job)
        db.commit()

        return {
            "message": f"Migration {migration_id} deleted successfully",
            "deleted_records": {
                "migration_job": 1,
                "raw_data_records": raw_data_count,
                "staged_data_records": staged_data_count
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting migration: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete migration: {str(e)}"
        )
