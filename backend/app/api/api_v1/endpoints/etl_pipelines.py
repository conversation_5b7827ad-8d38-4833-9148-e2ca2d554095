"""
ETL Pipeline API endpoints
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import desc

from app.core.database import get_db
from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun
from app.models.destination import Destination
from app.schemas.etl_pipeline import (
    ETLPipelineCreate,
    ETLPipelineUpdate,
    ETLPipelineResponse,
    ETLPipelineWithRuns,
    ETLPipelineRunResponse,
    PipelineExecutionRequest,
    PipelineExecutionResponse,
    PipelineStatsResponse,
    DestinationResponse
)
from app.etl.pipelines.etl_pipeline_service import ETLPipelineService
from app.etl.pipelines.pipeline_cleanup_service import PipelineCleanupService

router = APIRouter()


@router.get("/", response_model=List[ETLPipelineWithRuns])
def list_pipelines(
    skip: int = 0,
    limit: int = 100,
    include_inactive: bool = False,
    db: Session = Depends(get_db)
):
    """List all ETL pipelines with recent runs"""
    query = db.query(ETLPipeline)
    
    if not include_inactive:
        query = query.filter(ETLPipeline.is_active == True)
    
    pipelines = query.offset(skip).limit(limit).all()
    
    result = []
    for pipeline in pipelines:
        # Get recent runs (last 5)
        recent_runs = (
            db.query(ETLPipelineRun)
            .filter(ETLPipelineRun.pipeline_id == pipeline.id)
            .order_by(desc(ETLPipelineRun.started_at))
            .limit(5)
            .all()
        )
        
        # Get last run
        last_run = recent_runs[0] if recent_runs else None
        
        # Get total runs count
        total_runs = (
            db.query(ETLPipelineRun)
            .filter(ETLPipelineRun.pipeline_id == pipeline.id)
            .count()
        )
        
        # Use model_validate to properly handle complex data types
        pipeline_data = ETLPipelineWithRuns.model_validate(pipeline)
        pipeline_data.recent_runs = [ETLPipelineRunResponse.model_validate(run) for run in recent_runs]
        pipeline_data.last_run = ETLPipelineRunResponse.model_validate(last_run) if last_run else None
        pipeline_data.total_runs = total_runs
        result.append(pipeline_data)
    
    return result


@router.post("/", response_model=ETLPipelineResponse)
def create_pipeline(
    pipeline: ETLPipelineCreate,
    db: Session = Depends(get_db)
):
    """Create a new ETL pipeline"""
    # Check if pipeline name already exists
    existing = db.query(ETLPipeline).filter(ETLPipeline.name == pipeline.name).first()
    if existing:
        raise HTTPException(status_code=400, detail="Pipeline name already exists")
    
    db_pipeline = ETLPipeline(**pipeline.dict())
    db.add(db_pipeline)
    db.commit()
    db.refresh(db_pipeline)
    
    return ETLPipelineResponse(**db_pipeline.to_dict())


@router.get("/{pipeline_id}", response_model=ETLPipelineWithRuns)
def get_pipeline(
    pipeline_id: int,
    db: Session = Depends(get_db)
):
    """Get a specific ETL pipeline with runs"""
    pipeline = db.query(ETLPipeline).filter(ETLPipeline.id == pipeline_id).first()
    if not pipeline:
        raise HTTPException(status_code=404, detail="Pipeline not found")
    
    # Get recent runs (last 10)
    recent_runs = (
        db.query(ETLPipelineRun)
        .filter(ETLPipelineRun.pipeline_id == pipeline_id)
        .order_by(desc(ETLPipelineRun.started_at))
        .limit(10)
        .all()
    )
    
    # Get last run
    last_run = recent_runs[0] if recent_runs else None
    
    # Get total runs count
    total_runs = (
        db.query(ETLPipelineRun)
        .filter(ETLPipelineRun.pipeline_id == pipeline_id)
        .count()
    )
    
    return ETLPipelineWithRuns(
        **pipeline.to_dict(),
        recent_runs=[ETLPipelineRunResponse(**run.to_dict()) for run in recent_runs],
        last_run=ETLPipelineRunResponse(**last_run.to_dict()) if last_run else None,
        total_runs=total_runs
    )


@router.put("/{pipeline_id}", response_model=ETLPipelineResponse)
def update_pipeline(
    pipeline_id: int,
    pipeline_update: ETLPipelineUpdate,
    db: Session = Depends(get_db)
):
    """Update an ETL pipeline"""
    pipeline = db.query(ETLPipeline).filter(ETLPipeline.id == pipeline_id).first()
    if not pipeline:
        raise HTTPException(status_code=404, detail="Pipeline not found")
    
    # Update fields
    update_data = pipeline_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(pipeline, field, value)
    
    db.commit()
    db.refresh(pipeline)
    
    return ETLPipelineResponse(**pipeline.to_dict())


@router.delete("/{pipeline_id}")
def delete_pipeline(
    pipeline_id: int,
    db: Session = Depends(get_db)
):
    """Delete an ETL pipeline"""
    pipeline = db.query(ETLPipeline).filter(ETLPipeline.id == pipeline_id).first()
    if not pipeline:
        raise HTTPException(status_code=404, detail="Pipeline not found")
    
    db.delete(pipeline)
    db.commit()
    
    return {"message": "Pipeline deleted successfully"}


@router.post("/{pipeline_id}/execute", response_model=PipelineExecutionResponse)
def execute_pipeline(
    pipeline_id: int,
    background_tasks: BackgroundTasks,
    force_run: bool = False,
    db: Session = Depends(get_db)
):
    """Execute an ETL pipeline"""
    pipeline = db.query(ETLPipeline).filter(ETLPipeline.id == pipeline_id).first()
    if not pipeline:
        raise HTTPException(status_code=404, detail="Pipeline not found")

    if not pipeline.is_active and not force_run:
        raise HTTPException(status_code=400, detail="Pipeline is not active")

    # Clean up stale and completed pipeline runs before checking for running pipelines
    from datetime import datetime, timedelta

    # Find all "running" pipeline runs for this pipeline
    running_runs = (
        db.query(ETLPipelineRun)
        .filter(
            ETLPipelineRun.pipeline_id == pipeline_id,
            ETLPipelineRun.status == "running"
        )
        .all()
    )

    cleanup_count = 0
    for run in running_runs:
        runtime = datetime.utcnow() - run.started_at if run.started_at else timedelta(0)

        # Check if run has been running too long (more than 2 hours)
        if runtime > timedelta(hours=2):
            run.status = "failed"
            run.error_message = f"Pipeline run terminated due to exceeding maximum runtime of 2 hours (actual: {runtime})"
            run.completed_at = datetime.utcnow()
            cleanup_count += 1
        # Check if run appears to have completed but status wasn't updated
        elif run.records_loaded and run.records_loaded > 0:
            run.status = "completed"
            run.completed_at = datetime.utcnow()
            cleanup_count += 1
        # Check if run has extracted data but failed during processing (stuck for more than 10 minutes)
        elif run.records_extracted and run.records_extracted > 0 and runtime > timedelta(minutes=10):
            run.status = "failed"
            run.error_message = f"Pipeline run stuck during processing for {runtime} - auto-cleaned up"
            run.completed_at = datetime.utcnow()
            cleanup_count += 1
        # Check if run has been stuck at startup for more than 5 minutes
        elif not run.records_extracted and runtime > timedelta(minutes=5):
            run.status = "failed"
            run.error_message = f"Pipeline run stuck at startup for {runtime} - auto-cleaned up"
            run.completed_at = datetime.utcnow()
            cleanup_count += 1

    if cleanup_count > 0:
        db.commit()
        logger.info(f"Cleaned up {cleanup_count} stuck pipeline runs for pipeline {pipeline_id}")

    # Check if pipeline is currently running (after cleanup)
    running_run = (
        db.query(ETLPipelineRun)
        .filter(
            ETLPipelineRun.pipeline_id == pipeline_id,
            ETLPipelineRun.status == "running"
        )
        .first()
    )

    if running_run:
        raise HTTPException(status_code=400, detail="Pipeline is already running")
    
    # Execute pipeline in background
    def run_pipeline():
        from app.core.database import SessionLocal
        db_session = SessionLocal()
        try:
            service = ETLPipelineService(db_session)
            service.execute_pipeline(pipeline_id)
        finally:
            db_session.close()

    background_tasks.add_task(run_pipeline)
    
    return PipelineExecutionResponse(
        run_id=0,  # Will be created in background
        pipeline_id=pipeline_id,
        status="running",
        message="Pipeline execution started"
    )


@router.get("/{pipeline_id}/runs", response_model=List[ETLPipelineRunResponse])
def get_pipeline_runs(
    pipeline_id: int,
    skip: int = 0,
    limit: int = 50,
    db: Session = Depends(get_db)
):
    """Get runs for a specific pipeline"""
    runs = (
        db.query(ETLPipelineRun)
        .filter(ETLPipelineRun.pipeline_id == pipeline_id)
        .order_by(desc(ETLPipelineRun.started_at))
        .offset(skip)
        .limit(limit)
        .all()
    )
    
    return [ETLPipelineRunResponse(**run.to_dict()) for run in runs]


@router.get("/stats/overview", response_model=PipelineStatsResponse)
def get_pipeline_stats(db: Session = Depends(get_db)):
    """Get pipeline statistics"""
    total_pipelines = db.query(ETLPipeline).count()
    active_pipelines = db.query(ETLPipeline).filter(ETLPipeline.is_active == True).count()
    total_runs = db.query(ETLPipelineRun).count()
    successful_runs = db.query(ETLPipelineRun).filter(ETLPipelineRun.status == "completed").count()
    failed_runs = db.query(ETLPipelineRun).filter(ETLPipelineRun.status == "failed").count()
    running_pipelines = db.query(ETLPipelineRun).filter(ETLPipelineRun.status == "running").count()
    
    return PipelineStatsResponse(
        total_pipelines=total_pipelines,
        active_pipelines=active_pipelines,
        total_runs=total_runs,
        successful_runs=successful_runs,
        failed_runs=failed_runs,
        running_pipelines=running_pipelines
    )


@router.get("/destination/", response_model=List[DestinationResponse])
def list_destination(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """List destination (for Resort Pipeline results)"""
    destination = (
        db.query(Destination)
        .order_by(desc(Destination.created_at))
        .offset(skip)
        .limit(limit)
        .all()
    )

    return [DestinationResponse(**dest.to_dict()) for dest in destination]


@router.get("/strategies/")
def list_pipeline_strategies(db: Session = Depends(get_db)):
    """List all available pipeline strategies"""
    service = ETLPipelineService(db)
    return {
        "strategies": service.get_available_strategies(),
        "message": "Available pipeline strategies"
    }


@router.post("/{pipeline_id}/validate")
def validate_pipeline(
    pipeline_id: int,
    db: Session = Depends(get_db)
):
    """Validate a pipeline configuration"""
    pipeline = db.query(ETLPipeline).filter(ETLPipeline.id == pipeline_id).first()
    if not pipeline:
        raise HTTPException(status_code=404, detail="Pipeline not found")

    service = ETLPipelineService(db)
    is_valid, errors = service.validate_pipeline_configuration(pipeline)

    return {
        "pipeline_id": pipeline_id,
        "is_valid": is_valid,
        "errors": errors,
        "message": "Pipeline validation completed"
    }


@router.get("/{pipeline_id}/runs/{run_id}/details")
def get_run_details(
    pipeline_id: int,
    run_id: int,
    db: Session = Depends(get_db)
):
    """Get detailed information about a pipeline run including raw, processed, and destination data"""
    # Get the pipeline run
    run = (
        db.query(ETLPipelineRun)
        .filter(
            ETLPipelineRun.pipeline_id == pipeline_id,
            ETLPipelineRun.id == run_id
        )
        .first()
    )

    if not run:
        raise HTTPException(status_code=404, detail="Pipeline run not found")

    # Get the pipeline
    pipeline = db.query(ETLPipeline).filter(ETLPipeline.id == pipeline_id).first()
    if not pipeline:
        raise HTTPException(status_code=404, detail="Pipeline not found")

    try:
        from app.services.minio_service import minio_service

        # Get raw data from MinIO
        raw_data = []
        if run.raw_data_path:
            try:
                raw_data = minio_service.get_data(run.raw_data_path)
            except Exception as e:
                print(f"Error fetching raw data: {e}")

        # Get processed data from MinIO
        processed_data = []
        if run.processed_data_path:
            try:
                processed_data = minio_service.get_data(run.processed_data_path)
            except Exception as e:
                print(f"Error fetching processed data: {e}")

        # Get destination data (most recent records as sample)
        destination_data = []
        if pipeline.destination_table == "destination":
            # Get most recent records as sample
            destination_data = (
                db.query(Destination)
                .order_by(desc(Destination.created_at))
                .limit(10)  # Limit to 10 sample records
                .all()
            )
            destination_data = [dest.to_dict() for dest in destination_data]

        return {
            "run": ETLPipelineRunResponse(**run.to_dict()),
            "raw_data": raw_data[:10] if raw_data else [],  # Limit to first 10 records
            "processed_data": processed_data[:10] if processed_data else [],  # Limit to first 10 records
            "destination_data": destination_data[:10] if destination_data else []  # Limit to first 10 records
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching run details: {str(e)}")


@router.post("/cleanup/stuck-runs")
def cleanup_stuck_pipeline_runs(
    max_runtime_hours: int = 2,
    db: Session = Depends(get_db)
):
    """
    Manually trigger cleanup of stuck pipeline runs

    Args:
        max_runtime_hours: Maximum hours a pipeline should run before being considered stuck
    """
    try:
        cleanup_service = PipelineCleanupService(db)
        cleaned_count = cleanup_service.cleanup_stuck_runs(max_runtime_hours)

        return {
            "message": f"Cleanup completed successfully",
            "cleaned_runs": cleaned_count,
            "max_runtime_hours": max_runtime_hours
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error during cleanup: {str(e)}")


@router.get("/status/running")
def get_running_pipelines_status(db: Session = Depends(get_db)):
    """
    Get status of currently running pipelines for monitoring
    """
    try:
        cleanup_service = PipelineCleanupService(db)
        running_summary = cleanup_service.get_running_pipelines_summary()

        return {
            "running_pipelines": running_summary,
            "total_running": len(running_summary)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching running pipeline status: {str(e)}")
