from fastapi import APIRouter, HTTPException, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel
import json
from datetime import datetime

from app.core.database import get_db
from app.models.target_schema import TargetSchema as TargetSchemaModel, TargetSystem
from app.schemas.target_schema import TargetSchema, TargetSchemaCreate, TargetSchemaUpdate, TargetField, TargetSystemEnum

router = APIRouter()

def db_schema_to_response(db_schema: TargetSchemaModel) -> TargetSchema:
    """Convert database model to response model with proper JSON parsing"""
    fields_data = []
    if db_schema.fields:
        try:
            fields_json = json.loads(db_schema.fields)
            fields_data = [TargetField(**field) for field in fields_json]
        except (json.JSONDecodeError, TypeError):
            fields_data = []

    return TargetSchema(
        id=db_schema.id,
        name=db_schema.name,
        label=db_schema.label,
        description=db_schema.description or "",
        target_system=TargetSystemEnum(db_schema.target_system.value),
        fields=fields_data,
        created_at=db_schema.created_at,
        updated_at=db_schema.updated_at
    )

@router.get("/", response_model=List[TargetSchema])
async def get_target_schemas(
    target_system: Optional[TargetSystemEnum] = Query(None, description="Filter by target system"),
    db: Session = Depends(get_db)
):
    """
    Get all target schemas, optionally filtered by target system
    """
    try:
        query = db.query(TargetSchemaModel)

        if target_system:
            query = query.filter(TargetSchemaModel.target_system == TargetSystem(target_system.value))

        schemas = query.all()
        return [db_schema_to_response(schema) for schema in schemas]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving target schemas: {str(e)}")

@router.get("/{schema_id}", response_model=TargetSchema)
async def get_target_schema(schema_id: str, db: Session = Depends(get_db)):
    """
    Get a specific target schema by ID
    """
    try:
        schema = db.query(TargetSchemaModel).filter(TargetSchemaModel.id == schema_id).first()
        if not schema:
            raise HTTPException(status_code=404, detail=f"Target schema '{schema_id}' not found")
        return db_schema_to_response(schema)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving target schema: {str(e)}")

@router.post("/", response_model=TargetSchema)
async def create_target_schema(schema: TargetSchemaCreate, db: Session = Depends(get_db)):
    """
    Create a new target schema
    """
    try:
        # Check if schema with same ID already exists
        existing = db.query(TargetSchemaModel).filter(TargetSchemaModel.id == schema.id).first()
        if existing:
            raise HTTPException(status_code=400, detail=f"Target schema with ID '{schema.id}' already exists")
        
        # Create new schema
        # Convert Pydantic models to dict for JSON serialization
        fields_dict = [field.dict() for field in schema.fields]

        db_schema = TargetSchemaModel(
            id=schema.id,
            name=schema.name,
            label=schema.label,
            description=schema.description,
            target_system=TargetSystem(schema.target_system.value),
            fields=json.dumps(fields_dict),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        db.add(db_schema)
        db.commit()
        db.refresh(db_schema)

        return db_schema_to_response(db_schema)
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error creating target schema: {str(e)}")

@router.put("/{schema_id}", response_model=TargetSchema)
async def update_target_schema(
    schema_id: str, 
    schema_update: TargetSchemaUpdate, 
    db: Session = Depends(get_db)
):
    """
    Update an existing target schema
    """
    try:
        # Get existing schema
        db_schema = db.query(TargetSchemaModel).filter(TargetSchemaModel.id == schema_id).first()
        if not db_schema:
            raise HTTPException(status_code=404, detail=f"Target schema '{schema_id}' not found")
        
        # Update fields
        if schema_update.name is not None:
            db_schema.name = schema_update.name
        if schema_update.label is not None:
            db_schema.label = schema_update.label
        if schema_update.description is not None:
            db_schema.description = schema_update.description
        if schema_update.target_system is not None:
            db_schema.target_system = TargetSystem(schema_update.target_system.value)
        if schema_update.fields is not None:
            fields_dict = [field.dict() for field in schema_update.fields]
            db_schema.fields = json.dumps(fields_dict)
        
        db_schema.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(db_schema)

        return db_schema_to_response(db_schema)
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error updating target schema: {str(e)}")

@router.delete("/{schema_id}")
async def delete_target_schema(schema_id: str, db: Session = Depends(get_db)):
    """
    Delete a target schema
    """
    try:
        # Get existing schema
        db_schema = db.query(TargetSchemaModel).filter(TargetSchemaModel.id == schema_id).first()
        if not db_schema:
            raise HTTPException(status_code=404, detail=f"Target schema '{schema_id}' not found")
        
        db.delete(db_schema)
        db.commit()
        
        return {"message": f"Target schema '{schema_id}' deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error deleting target schema: {str(e)}")

@router.get("/{schema_id}/fields")
async def get_schema_fields(schema_id: str, db: Session = Depends(get_db)):
    """
    Get fields for a specific target schema
    """
    try:
        schema = db.query(TargetSchemaModel).filter(TargetSchemaModel.id == schema_id).first()
        if not schema:
            raise HTTPException(status_code=404, detail=f"Target schema '{schema_id}' not found")
        
        fields = json.loads(schema.fields) if schema.fields else []
        return {
            "schema_id": schema_id,
            "schema_name": schema.name,
            "fields": fields
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving schema fields: {str(e)}")
