"""
Load endpoints for the ETL Migration System.
"""
from typing import List, Dict, Any, Optional
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field
import logging

from app.core.database import get_db
from app.config.settings import settings
from app.models.staged_data import StagedData
from app.models.migration_job import MigrationJob, JobStatus
from app.etl.loaders.ops_adapter import OpsAdapter
from app.etl.loaders.base_adapter import SyncOperation, SyncResult

logger = logging.getLogger(__name__)

router = APIRouter()


class LoadRequest(BaseModel):
    """Request model for loading data to target systems."""
    migration_job_id: UUID = Field(..., description="Migration job ID")
    target_system: str = Field(..., description="Target system (ops, crm)")
    object_type: str = Field(..., description="Object type (destination, etc.)")
    operation: str = Field(default="upsert", description="Sync operation (create, update, upsert, delete)")
    config: Dict[str, Any] = Field(default={}, description="Target system configuration")


class LoadResponse(BaseModel):
    """Response model for load operations."""
    success: bool = Field(..., description="Whether the load operation was successful")
    total_records: int = Field(..., description="Total number of records processed")
    successful_records: int = Field(..., description="Number of successfully loaded records")
    failed_records: int = Field(..., description="Number of failed records")
    errors: List[str] = Field(default=[], description="List of error messages")
    message: str = Field(..., description="Operation result message")


class BulkLoadRequest(BaseModel):
    """Request model for bulk loading data."""
    target_system: str = Field(..., description="Target system (ops, crm)")
    object_type: str = Field(..., description="Object type (destination, etc.)")
    records: List[Dict[str, Any]] = Field(..., description="Records to load")
    operation: str = Field(default="upsert", description="Sync operation")
    config: Dict[str, Any] = Field(default={}, description="Target system configuration")


@router.post("/staged/{migration_job_id}", response_model=LoadResponse)
async def load_staged_data(
    migration_job_id: UUID,
    request: LoadRequest,
    db: Session = Depends(get_db)
):
    """
    Load staged data to target system.
    
    This endpoint loads all staged data for a migration job to the specified target system.
    """
    try:
        # Get staged data for the migration job
        staged_records = db.query(StagedData).filter(
            StagedData.migration_job_id == migration_job_id,
            StagedData.sync_status == "pending"
        ).all()
        
        if not staged_records:
            return LoadResponse(
                success=True,
                total_records=0,
                successful_records=0,
                failed_records=0,
                message="No pending staged data found for migration job"
            )
        
        # Initialize target adapter
        adapter = _get_target_adapter(request.target_system, request.config)
        if not adapter:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported target system: {request.target_system}"
            )
        
        # Authenticate with target system
        if not adapter.authenticate():
            raise HTTPException(
                status_code=500,
                detail=f"Failed to authenticate with {request.target_system}"
            )
        
        # Prepare records for sync
        records_to_sync = []
        for staged_record in staged_records:
            # Combine transformed data with user modifications
            record_data = staged_record.transformed_data.copy()
            if staged_record.user_modified and staged_record.user_modifications:
                record_data.update(staged_record.user_modifications)
            records_to_sync.append(record_data)
        
        # Perform batch sync
        operation = SyncOperation(request.operation)
        sync_results = adapter.sync_batch(request.object_type, records_to_sync, operation)
        
        # Update staged data with sync results
        successful_count = 0
        failed_count = 0
        errors = []
        
        for i, (staged_record, sync_result) in enumerate(zip(staged_records, sync_results)):
            if sync_result.success:
                staged_record.sync_status = "completed"
                staged_record.target_id = sync_result.target_id
                staged_record.sync_error = None
                successful_count += 1
            else:
                staged_record.sync_status = "failed"
                staged_record.sync_error = sync_result.error
                errors.append(f"Record {i+1}: {sync_result.error}")
                failed_count += 1
        
        # Commit changes to database
        db.commit()
        
        return LoadResponse(
            success=failed_count == 0,
            total_records=len(staged_records),
            successful_records=successful_count,
            failed_records=failed_count,
            errors=errors,
            message=f"Loaded {successful_count}/{len(staged_records)} records to {request.target_system}"
        )
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error loading staged data: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to load staged data: {str(e)}"
        )


@router.post("/sync-migration/{migration_id}")
async def sync_migration(
    migration_id: UUID,
    db: Session = Depends(get_db)
):
    """
    Sync all staged data for a migration to the target system.

    This will load all pending staged data records for the migration.
    """
    try:
        # Get migration job
        migration_job = db.query(MigrationJob).filter(
            MigrationJob.id == migration_id
        ).first()

        if not migration_job:
            raise HTTPException(
                status_code=404,
                detail=f"Migration {migration_id} not found"
            )

        # Get all pending staged data for this migration
        staged_records = db.query(StagedData).filter(
            StagedData.migration_job_id == migration_id,
            StagedData.sync_status == "pending"
        ).all()

        if not staged_records:
            return {
                "message": "No pending records to sync",
                "records_processed": 0,
                "records_synced": 0,
                "records_failed": 0
            }

        # Update migration status to syncing
        migration_job.status = JobStatus.SYNCING
        migration_job.current_step = "Syncing to target system"
        db.commit()

        # Group records by target system (from target_config)
        target_config = migration_job.target_config or {}
        target_system = target_config.get('target_system', 'ops')

        # Prepare data for bulk load
        records_to_sync = []
        for staged_record in staged_records:
            records_to_sync.append(staged_record.transformed_data)

        # Perform bulk load based on target system
        sync_results = {
            "records_processed": len(records_to_sync),
            "records_synced": 0,
            "records_failed": 0,
            "errors": []
        }

        if target_system == 'ops':
            # Load to OPS system
            try:
                # Prepare destination data for OPS API
                destination_data = {
                    "destination": records_to_sync
                }

                # Make API call to OPS system
                ops_response = requests.post(
                    "http://localhost:9000/store/etl/destination/bulk",
                    json=destination_data,
                    headers={"Content-Type": "application/json"}
                )

                if ops_response.status_code == 200:
                    # Mark all records as synced
                    for staged_record in staged_records:
                        staged_record.sync_status = "completed"
                        staged_record.synced_at = datetime.utcnow()

                    sync_results["records_synced"] = len(staged_records)

                else:
                    # Mark all records as failed
                    error_msg = f"OPS API error: {ops_response.status_code} - {ops_response.text}"
                    for staged_record in staged_records:
                        staged_record.sync_status = "failed"
                        staged_record.error_message = error_msg

                    sync_results["records_failed"] = len(staged_records)
                    sync_results["errors"].append(error_msg)

            except Exception as e:
                # Mark all records as failed
                error_msg = f"Failed to sync to OPS: {str(e)}"
                for staged_record in staged_records:
                    staged_record.sync_status = "failed"
                    staged_record.error_message = error_msg

                sync_results["records_failed"] = len(staged_records)
                sync_results["errors"].append(error_msg)

        else:
            # Unsupported target system
            error_msg = f"Unsupported target system: {target_system}"
            for staged_record in staged_records:
                staged_record.sync_status = "failed"
                staged_record.error_message = error_msg

            sync_results["records_failed"] = len(staged_records)
            sync_results["errors"].append(error_msg)

        # Update migration job status and statistics
        migration_job.records_synced = sync_results["records_synced"]
        migration_job.records_failed += sync_results["records_failed"]

        if sync_results["records_failed"] > 0:
            migration_job.status = JobStatus.FAILED
            migration_job.error_message = "; ".join(sync_results["errors"])
            migration_job.current_step = "Sync failed"
        else:
            migration_job.status = JobStatus.COMPLETED
            migration_job.current_step = "Sync completed"
            migration_job.progress_percentage = 100

        migration_job.completed_at = datetime.utcnow()
        db.commit()

        return {
            "message": "Migration sync completed",
            "migration_id": str(migration_id),
            **sync_results
        }

    except HTTPException:
        raise
    except Exception as e:
        # Update migration status to failed
        if 'migration_job' in locals():
            migration_job.status = JobStatus.FAILED
            migration_job.error_message = str(e)
            migration_job.completed_at = datetime.utcnow()
            db.commit()

        logger.error(f"Error syncing migration: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to sync migration: {str(e)}"
        )


@router.post("/bulk", response_model=LoadResponse)
async def bulk_load_data(
    request: BulkLoadRequest
):
    """
    Bulk load data directly to target system.
    
    This endpoint allows direct bulk loading of data without going through the staged data process.
    """
    try:
        # Initialize target adapter
        adapter = _get_target_adapter(request.target_system, request.config)
        if not adapter:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported target system: {request.target_system}"
            )
        
        # Authenticate with target system
        if not adapter.authenticate():
            raise HTTPException(
                status_code=500,
                detail=f"Failed to authenticate with {request.target_system}"
            )
        
        # Perform batch sync
        operation = SyncOperation(request.operation)
        sync_results = adapter.sync_batch(request.object_type, request.records, operation)
        
        # Process results
        successful_count = 0
        failed_count = 0
        errors = []
        
        for i, sync_result in enumerate(sync_results):
            if sync_result.success:
                successful_count += 1
            else:
                errors.append(f"Record {i+1}: {sync_result.error}")
                failed_count += 1
        
        return LoadResponse(
            success=failed_count == 0,
            total_records=len(request.records),
            successful_records=successful_count,
            failed_records=failed_count,
            errors=errors,
            message=f"Bulk loaded {successful_count}/{len(request.records)} records to {request.target_system}"
        )
        
    except Exception as e:
        logger.error(f"Error bulk loading data: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to bulk load data: {str(e)}"
        )


@router.post("/test-connection")
async def test_target_connection(
    target_system: str,
    config: Dict[str, Any] = {}
):
    """
    Test connection to target system.
    """
    try:
        adapter = _get_target_adapter(target_system, config)
        if not adapter:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported target system: {target_system}"
            )
        
        # Test authentication and connection
        auth_success = adapter.authenticate()
        if not auth_success:
            return {
                "success": False,
                "message": f"Failed to authenticate with {target_system}"
            }
        
        connection_success = adapter.test_connection()
        return {
            "success": connection_success,
            "message": f"Connection to {target_system} {'successful' if connection_success else 'failed'}"
        }
        
    except Exception as e:
        logger.error(f"Error testing connection: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to test connection: {str(e)}"
        )


def _get_target_adapter(target_system: str, config: Dict[str, Any]):
    """
    Get the appropriate target adapter based on system type.

    Args:
        target_system: Target system identifier (ops, crm)
        config: Configuration for the target system

    Returns:
        Target adapter instance or None if unsupported
    """
    if target_system.lower() == "ops":
        # Merge provided config with settings
        ops_config = {
            "api_url": config.get("api_url", settings.ops_url),
            "api_key": config.get("api_key", settings.ops_api_key),
            "timeout": config.get("timeout", 30)
        }
        return OpsAdapter(ops_config)
    # Add other adapters here as needed
    # elif target_system.lower() == "crm":
    #     return CrmAdapter(config)

    return None
