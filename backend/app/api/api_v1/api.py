"""
Main API router for the ETL Migration System.
"""
from fastapi import APIRouter

from app.api.api_v1.endpoints import migration, staged_data, target_schemas, field_mappings, load, etl_pipelines, websocket

api_router = APIRouter()

# Include migration endpoints
api_router.include_router(
    migration.router,
    prefix="/migration",
    tags=["migration"]
)

# Include staged data endpoints
api_router.include_router(
    staged_data.router,
    prefix="/data",
    tags=["staged-data"]
)

# Include target schemas endpoints
api_router.include_router(
    target_schemas.router,
    prefix="/target-schemas",
    tags=["target-schemas"]
)

# Include field mappings endpoints
api_router.include_router(
    field_mappings.router,
    prefix="/field-mappings",
    tags=["field-mappings"]
)

# Include load endpoints
api_router.include_router(
    load.router,
    prefix="/load",
    tags=["load"]
)

# Include ETL pipeline endpoints
api_router.include_router(
    etl_pipelines.router,
    prefix="/pipelines",
    tags=["etl-pipelines"]
)

# Include WebSocket endpoints
api_router.include_router(
    websocket.router,
    prefix="/ws",
    tags=["websocket"]
)
