"""
Main FastAPI application for the ETL Migration System.
"""
from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import logging

from app.config.settings import settings
from app.core.database import check_db_connection, create_tables
from app.api.api_v1.api import api_router
# Import all models to ensure they're registered with SQLAlchemy
from app.models import MigrationJob, RawData, StagedData, TargetSchema, FieldMapping

# Configure logging
logging.basicConfig(level=settings.log_level)
logger = logging.getLogger(__name__)

# Suppress SQLAlchemy INFO level logs for the engine
logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events."""
    # Startup
    logger.info("Starting Flinkk Transfer Hub...")

    # Log configuration for debugging (without sensitive data)
    logger.info("=== Configuration Debug Info ===")
    logger.info(f"Environment: {settings.environment}")
    logger.info(f"App Name: {settings.app_name}")
    logger.info(f"Database URL (first 30 chars): {settings.database_url[:30]}...")
    logger.info(f"Salesforce Username: {settings.salesforce_username}")
    logger.info(f"Salesforce Client ID: {settings.salesforce_client_id}")
    logger.info(f"Salesforce Client Secret (length): {len(settings.salesforce_client_secret)} chars")
    logger.info(f"Salesforce Security Token (length): {len(settings.salesforce_security_token)} chars")
    logger.info(f"Salesforce Password (length): {len(settings.salesforce_password)} chars")
    logger.info(f"CORS Origins: {settings.cors_origins_list}")
    logger.info("=== End Configuration Debug ===")

    # Check database connection (skip in development if DB not available)
    if not check_db_connection():
        if settings.environment == "development":
            logger.warning("Database connection failed - continuing in development mode")
        else:
            raise HTTPException(
                status_code=500,
                detail="Cannot connect to Azure PostgreSQL database"
            )
    else:
        # Create tables if they don't exist
        create_tables()

        # Clean up any stuck pipeline runs from previous server sessions
        try:
            from app.core.database import SessionLocal
            from app.etl.pipelines.pipeline_cleanup_service import PipelineCleanupService

            db = SessionLocal()
            try:
                cleanup_service = PipelineCleanupService(db)
                cleaned_count = cleanup_service.cleanup_on_startup()
                if cleaned_count > 0:
                    logger.info(f"Startup cleanup: Fixed {cleaned_count} stuck pipeline runs")
                else:
                    logger.info("Startup cleanup: No stuck pipeline runs found")
            finally:
                db.close()
        except Exception as e:
            logger.error(f"Error during startup pipeline cleanup: {e}")

    logger.info("Application startup complete")
    yield

    # Shutdown
    logger.info("Shutting down Flinkk Transfer Hub...")


# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    version=settings.version,
    description="Scalable ETL migration system for Salesforce to CRM data transfer",
    lifespan=lifespan,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins_list,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(api_router, prefix=settings.api_v1_prefix)


@app.get("/")
async def root():
    """Root endpoint with basic application info."""
    return {
        "message": "Flinkk Transfer Hub - ETL Migration System",
        "version": settings.version,
        "environment": settings.environment,
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    db_status = check_db_connection()
    return {
        "status": "healthy" if db_status else "unhealthy",
        "database": "connected" if db_status else "disconnected",
        "version": settings.version
    }
