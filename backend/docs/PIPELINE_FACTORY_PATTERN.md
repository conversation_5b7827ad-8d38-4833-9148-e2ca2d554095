# Pipeline Factory Pattern Implementation

## Overview

The Pipeline Factory Pattern has been implemented to provide a clean, extensible architecture for ETL pipelines that supports different source/destination combinations. This pattern ensures excellent separation of concerns and makes it easy to add new pipeline types without modifying existing code.

## Architecture Components

### 1. Base Strategy Interface (`BasePipelineStrategy`)

The abstract base class that defines the contract for all pipeline strategies:

```python
class BasePipelineStrategy(ABC):
    @abstractmethod
    def get_strategy_name(self) -> str
    
    @abstractmethod
    def get_supported_source_types(self) -> List[str]
    
    @abstractmethod
    def get_supported_destination_types(self) -> List[str]
    
    @abstractmethod
    def validate_configuration(self, pipeline: ETLPipeline) -> tuple[bool, List[str]]
    
    @abstractmethod
    def execute(self, pipeline: ETLPipeline, run: ETLPipelineRun) -> PipelineExecutionResult
```

### 2. Concrete Strategy Implementations

#### SalesforceToPostgreSQLStrategy
- **Sources**: `["salesforce"]`
- **destination**: `["postgresql"]`
- **Features**: Salesforce authentication, field mapping, MinIO storage, PostgreSQL loading

#### APIToPostgreSQLStrategy
- **Sources**: `["api", "rest_api", "http_api"]`
- **destination**: `["postgresql"]`
- **Features**: REST API calls, JSON parsing, configurable endpoints, PostgreSQL loading

### 3. Pipeline Type Registry (`PipelineTypeRegistry`)

Manages registration and discovery of pipeline strategies:

```python
# Register a new strategy
pipeline_registry.register_strategy("custom_strategy", CustomStrategy)

# Find compatible strategies
compatible = pipeline_registry.find_compatible_strategies("source", "destination", db_session)

# List all strategies
strategies = pipeline_registry.list_strategies()
```

### 4. Pipeline Factory (`PipelineFactory`)

Creates appropriate strategy instances based on pipeline configuration:

```python
factory = PipelineFactory(db_session)

# Automatically selects and creates the right strategy
strategy = factory.create_strategy(pipeline)

# Execute the pipeline
result = strategy.execute(pipeline, run)
```

## Benefits

### ✅ Clean Separation of Concerns
- Each strategy handles one specific source/destination combination
- Business logic is encapsulated within strategies
- Factory handles strategy selection and instantiation

### ✅ Extensibility
- Add new source/destination combinations without modifying existing code
- Strategies are self-contained and independent
- Automatic discovery and registration

### ✅ Maintainability
- Clear interfaces and contracts
- Easy to test individual strategies
- Centralized strategy management

### ✅ Scalability
- Strategies can be optimized independently
- Easy to add parallel processing or async execution
- Support for different execution patterns

## How to Add New Pipeline Strategies

### Step 1: Create Strategy Class

```python
from app.services.pipeline_strategies.base_strategy import BasePipelineStrategy, PipelineExecutionResult

class MyCustomStrategy(BasePipelineStrategy):
    def get_strategy_name(self) -> str:
        return "my_custom_strategy"
    
    def get_supported_source_types(self) -> List[str]:
        return ["my_source"]
    
    def get_supported_destination_types(self) -> List[str]:
        return ["my_destination"]
    
    def validate_configuration(self, pipeline: ETLPipeline) -> tuple[bool, List[str]]:
        errors = []
        # Add validation logic
        return len(errors) == 0, errors
    
    def execute(self, pipeline: ETLPipeline, run: ETLPipelineRun) -> PipelineExecutionResult:
        # Implement ETL logic
        result = PipelineExecutionResult(success=True, records_processed=0)
        # ... implementation
        return result
```

### Step 2: Register Strategy

```python
# In pipeline_factory.py
from .my_custom_strategy import MyCustomStrategy

class PipelineTypeRegistry:
    def _register_default_strategies(self):
        # ... existing registrations
        self.register_strategy("my_custom", MyCustomStrategy)
```

### Step 3: Use Immediately

```python
# The factory will automatically discover and use your strategy
pipeline = ETLPipeline(
    source_type="my_source",
    destination_type="my_destination",
    # ... other config
)

factory = PipelineFactory(db_session)
strategy = factory.create_strategy(pipeline)  # Returns MyCustomStrategy instance
result = strategy.execute(pipeline, run)
```

## API Endpoints

### List Available Strategies
```http
GET /api/v1/etl-pipelines/strategies/
```

Response:
```json
{
  "strategies": [
    {
      "name": "salesforce_to_postgresql",
      "strategy_name": "salesforce_to_postgresql",
      "supported_sources": ["salesforce"],
      "supported_destination": ["postgresql"],
      "class_name": "SalesforceToPostgreSQLStrategy"
    }
  ],
  "message": "Available pipeline strategies"
}
```

### Validate Pipeline Configuration
```http
POST /api/v1/etl-pipelines/{pipeline_id}/validate
```

Response:
```json
{
  "pipeline_id": 1,
  "is_valid": true,
  "errors": [],
  "message": "Pipeline validation completed"
}
```

## Example Usage

### Creating a Salesforce to PostgreSQL Pipeline

```python
# Pipeline configuration
pipeline = ETLPipeline(
    name="Customer Data Pipeline",
    source_type="salesforce",
    source_object="Account",
    source_fields=["Name", "Type", "Industry"],
    destination_type="postgresql",
    destination_table="customers",
    destination_fields={
        "name": "Name",
        "type": "Type",
        "industry": "Industry"
    }
)

# Factory automatically selects SalesforceToPostgreSQLStrategy
service = ETLPipelineService(db_session)
run = service.execute_pipeline(pipeline.id)
```

### Creating an API to PostgreSQL Pipeline

```python
# Pipeline configuration
pipeline = ETLPipeline(
    name="External API Data Pipeline",
    source_type="api",
    source_config={
        "api_url": "https://api.example.com",
        "endpoint": "/users",
        "headers": {"Authorization": "Bearer token"}
    },
    destination_type="postgresql",
    destination_table="users",
    destination_fields={
        "name": "full_name",
        "email": "email_address"
    }
)

# Factory automatically selects APIToPostgreSQLStrategy
service = ETLPipelineService(db_session)
run = service.execute_pipeline(pipeline.id)
```

## Testing

Run the factory pattern test:

```bash
cd backend
python3 simple_factory_test.py
```

This demonstrates:
- Strategy registration and discovery
- Automatic strategy selection
- Error handling for unsupported combinations
- Extensibility patterns

## Migration from Legacy Code

The existing `ETLPipelineService` has been refactored to use the factory pattern while maintaining backward compatibility. The old monolithic approach has been replaced with:

1. **Strategy Selection**: Factory automatically selects the appropriate strategy
2. **Execution Delegation**: All ETL logic is delegated to the selected strategy
3. **Result Handling**: Standardized result handling across all strategies

This ensures that existing pipelines continue to work while new pipelines benefit from the improved architecture.
