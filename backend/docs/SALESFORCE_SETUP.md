# Salesforce Integration Setup & Testing

## 🚀 Quick Setup

### 1. Get Salesforce Credentials

You need to create a **Connected App** in Salesforce to get API credentials:

#### Step 1: Create Connected App
1. Log into your Salesforce org
2. Go to **Setup** → **App Manager**
3. Click **New Connected App**
4. Fill in basic information:
   - **Connected App Name**: "ETL Migration System"
   - **API Name**: "ETL_Migration_System"
   - **Contact Email**: your email

#### Step 2: Enable OAuth Settings
1. Check **Enable OAuth Settings**
2. **Callback URL**: `https://login.salesforce.com/services/oauth2/success`
3. **Selected OAuth Scopes**:
   - Access and manage your data (api)
   - Perform requests on your behalf at any time (refresh_token, offline_access)
   - Access your basic information (id, profile, email, address, phone)

#### Step 3: Get Client ID & Secret
1. Save the Connected App
2. Copy the **Consumer Key** (this is your CLIENT_ID)
3. Copy the **Consumer Secret** (this is your CLIENT_SECRET)

#### Step 4: Get Security Token
1. Go to your **Personal Settings** → **Reset My Security Token**
2. Click **Reset Security Token**
3. Check your email for the security token

### 2. Set Up Environment Variables

```bash
# Copy the template
cp .env.test .env

# Edit the .env file with your credentials
nano .env
```

Fill in these values in your `.env` file:
```bash
SALESFORCE_CLIENT_ID=your_consumer_key_from_connected_app
SALESFORCE_CLIENT_SECRET=your_consumer_secret_from_connected_app
SALESFORCE_USERNAME=<EMAIL>
SALESFORCE_PASSWORD=your_salesforce_password
SALESFORCE_SECURITY_TOKEN=your_security_token_from_email
```

### 3. Run the Test

```bash
# Make sure you're in the backend directory and virtual environment is active
cd backend
source venv/bin/activate

# Run the Salesforce integration test
python test_salesforce.py
```

## 🧪 What the Test Does

The test script will:

1. **Test Authentication** - Verify your credentials work
2. **Test Object Info** - Get metadata for Account, Contact, Lead objects
3. **Test Record Counting** - Count records in each object
4. **Test Data Extraction** - Extract a few sample records
5. **Test API Selection** - Verify both REST API and Bulk API work
