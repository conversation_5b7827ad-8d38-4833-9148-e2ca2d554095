# OPS Integration Guide

This document describes the OPS (Medusa) integration for the ETL Migration System.

## Overview

The OPS integration allows you to load transformed Salesforce data into the OPS (Medusa) system using the bulk destination endpoint.

## Configuration

Add the following environment variables to your `.env` file:

```bash
# OPS Configuration
OPS_URL=http://localhost:9000
OPS_API_KEY=pk_d00b2f6be8293bf310a4f2dab6bba2f4e7e1800647febdbe9b994e9af6191a9b
```

## Supported Objects

Currently, the OPS adapter supports:

- **destination**: Bulk import of destination records

### Destination Schema

The destination object supports the following fields:

**Required Fields:**
- `name` (string): Destination name
- `country` (string): Country name
- `description` (string): Destination description

**Optional Fields:**
- `is_active` (boolean): Whether the destination is active (default: true)
- `location` (string): Location description
- `website` (string): Website URL
- `is_featured` (boolean): Whether the destination is featured (default: false)
- `tags` (array): Array of tags
- `salesforce_id` (string): Salesforce record ID for tracking
- `import_batch` (string): Import batch identifier
- `region` (string): Region classification
- `timezone` (string): Timezone identifier
- `faqs` (object): FAQ data
- `metadata` (object): Additional metadata

## API Endpoints

### Test Connection
```http
POST /api/v1/load/test-connection?target_system=ops
Content-Type: application/json

{}
```

### Bulk Load Data
```http
POST /api/v1/load/bulk
Content-Type: application/json

{
  "target_system": "ops",
  "object_type": "destination",
  "records": [
    {
      "name": "Paris, France",
      "country": "France",
      "description": "The City of Light",
      "is_active": true,
      "location": "Western Europe",
      "website": "https://paris.fr",
      "is_featured": true,
      "tags": ["romantic", "culture", "art"],
      "salesforce_id": "SF_DEST_001",
      "import_batch": "2024-001",
      "region": "europe",
      "timezone": "Europe/Paris"
    }
  ],
  "operation": "create"
}
```

### Load Staged Data
```http
POST /api/v1/load/staged/{migration_job_id}
Content-Type: application/json

{
  "migration_job_id": "uuid-here",
  "target_system": "ops",
  "object_type": "destination",
  "operation": "create"
}
```

## Frontend Integration

The frontend migration flow now includes real OPS integration:

1. **Select**: Choose Salesforce object
2. **Extract**: Fetch data from Salesforce
3. **Transform**: Map fields to OPS destination schema
4. **Edit**: Review and modify data
5. **Load**: Send data to OPS system
6. **Complete**: View load results

## Testing

Use the test script to verify the integration:

```bash
cd backend
python test_ops_integration.py
```

This will test:
1. Direct OPS API call
2. Connection test through our API
3. Bulk load through our API

## Error Handling

The system includes comprehensive error handling:

- **Validation Errors**: Field validation before sending to OPS
- **Connection Errors**: Network and authentication issues
- **API Errors**: OPS API response errors
- **Transformation Errors**: Data transformation issues

All errors are logged and returned to the frontend for user feedback.

## OPS API Endpoint

The OPS system expects data at:
```
POST http://localhost:9000/store/etl/destination/bulk
```

With payload format:
```json
{
  "destination": [
    {
      "name": "Destination Name",
      "country": "Country",
      "description": "Description",
      // ... other fields
    }
  ]
}
```

## Next Steps

Future enhancements may include:
- Support for other OPS object types
- Real-time sync status updates
- Batch processing with progress tracking
- Retry mechanisms for failed records
- Data validation rules configuration
