"""Update ProductCategory model

Revision ID: 2236561e4840
Revises: 011
Create Date: 2025-08-12 19:47:11.663458

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2236561e4840'
down_revision: Union[str, None] = '011'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('product_category', sa.Column('description', sa.Text(), nullable=True))
    op.add_column('product_category', sa.Column('handle', sa.Text(), nullable=True))
    op.add_column('product_category', sa.Column('mpath', sa.Text(), nullable=True))
    op.add_column('product_category', sa.Column('is_active', sa.<PERSON>(), nullable=False, server_default=sa.text('true')))
    op.add_column('product_category', sa.Column('is_internal', sa.<PERSON>(), nullable=False, server_default=sa.text('false')))
    op.add_column('product_category', sa.Column('rank', sa.Integer(), nullable=False, server_default=sa.text('0')))
    op.add_column('product_category', sa.Column('parent_category_id', sa.Text(), nullable=True))
    op.add_column('product_category', sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('product_category', sa.Column('metadata', sa.JSON(), nullable=True))
    op.alter_column('product_category', 'id',
               existing_type=sa.VARCHAR(length=255),
               type_=sa.Text(),
               existing_nullable=False)
    op.alter_column('product_category', 'name',
               existing_type=sa.VARCHAR(length=255),
               type_=sa.Text(),
               nullable=False)
    op.alter_column('product_category', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('product_category', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.create_index(op.f('ix_product_category_mpath'), 'product_category', ['mpath'], unique=False)
    op.create_unique_constraint(None, 'product_category', ['handle'])
    op.create_foreign_key(None, 'product_category', 'product_category', ['parent_category_id'], ['id'])
    op.drop_column('product_category', 'pipeline_run_id')
    op.drop_column('product_category', 'migrated_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('product_category', sa.Column('migrated_id', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    op.add_column('product_category', sa.Column('pipeline_run_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'product_category', type_='foreignkey')
    op.drop_constraint(None, 'product_category', type_='unique')
    op.drop_index(op.f('ix_product_category_mpath'), table_name='product_category')
    op.alter_column('product_category', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('product_category', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('product_category', 'name',
               existing_type=sa.Text(),
               type_=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('product_category', 'id',
               existing_type=sa.Text(),
               type_=sa.VARCHAR(length=255),
               existing_nullable=False)
    op.drop_column('product_category', 'metadata')
    op.drop_column('product_category', 'deleted_at')
    op.drop_column('product_category', 'parent_category_id')
    op.drop_column('product_category', 'rank')
    op.drop_column('product_category', 'is_internal')
    op.drop_column('product_category', 'is_active')
    op.drop_column('product_category', 'mpath')
    op.drop_column('product_category', 'handle')
    op.drop_column('product_category', 'description')
    # ### end Alembic commands ###
