"""Reorder primary keys to first position (simple approach)

Revision ID: 007
Revises: 005
Create Date: 2025-08-11 18:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '007'
down_revision = '005'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """
    Reorder tables to have primary keys as first columns.
    This migration focuses only on column ordering without changing data types.
    """
    
    print("Starting primary key position restructuring...")
    
    # 1. destination TABLE
    print("Restructuring destination table...")
    
    # Drop foreign key constraints first
    try:
        op.drop_constraint('hotel_destination_id_fkey', 'hotel', type_='foreignkey')
    except:
        pass
    
    # Create new destination table with id as first column
    op.execute("""
        CREATE TABLE destination_new (
            id INTEGER PRIMARY KEY DEFAULT nextval('destination_id_seq'),
            migrated_id VARCHAR(255),
            country VARCHAR(255),
            name VARCHAR(255),
            is_active BOOLEAN DEFAULT TRUE,
            currency VARCHAR(10),
            description TEXT,
            pipeline_run_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
            source_created_at TIMESTAMP,
            source_updated_at TIMESTAMP,
            extracted_at TIMESTAMP,
            source_system VARCHAR(255),
            external_id VARCHAR(255)
        )
    """)
    
    # Copy data preserving existing IDs
    op.execute("""
        INSERT INTO destination_new 
        SELECT id, migrated_id, country, name, is_active, currency, description,
               pipeline_run_id, created_at, updated_at, source_created_at, source_updated_at,
               extracted_at, source_system, external_id
        FROM destination
        ORDER BY id
    """)
    
    # Drop old table and rename
    op.drop_table('destination')
    op.execute('ALTER TABLE destination_new RENAME TO destination')
    
    # Recreate indexes
    op.create_index('idx_destination_migrated_id', 'destination', ['migrated_id'])
    op.create_index('idx_destination_country', 'destination', ['country'])
    op.create_index('idx_destination_is_active', 'destination', ['is_active'])
    op.create_index('idx_destination_currency', 'destination', ['currency'])
    op.create_index('idx_destination_pipeline_run_id', 'destination', ['pipeline_run_id'])
    
    # 2. hotel TABLE
    print("Restructuring hotel table...")
    
    # Drop foreign key constraints
    try:
        op.drop_constraint('hotel_category_id_fkey', 'hotel', type_='foreignkey')
    except:
        pass
    try:
        op.drop_constraint('hotel_pipeline_run_id_fkey', 'hotel', type_='foreignkey')
    except:
        pass
    
    # Create new hotel table with id as first column
    op.execute("""
        CREATE TABLE hotel_new (
            id INTEGER PRIMARY KEY DEFAULT nextval('hotel_id_seq'),
            migrated_id VARCHAR(255),
            name VARCHAR(255),
            destination_id INTEGER,
            category_id VARCHAR(255),
            pipeline_run_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
            source_created_at TIMESTAMP,
            source_updated_at TIMESTAMP,
            extracted_at TIMESTAMP,
            source_system VARCHAR(255),
            external_id VARCHAR(255),
            is_active BOOLEAN,
            currency VARCHAR(255),
            description TEXT,
            resort_name VARCHAR(255),
            hotel_name VARCHAR(255),
            currency_iso_code VARCHAR(255),
            current_status VARCHAR(255),
            website VARCHAR(255),
            notes TEXT,
            email VARCHAR(255),
            resort_id VARCHAR(255)
        )
    """)
    
    # Copy data preserving existing IDs
    op.execute("""
        INSERT INTO hotel_new 
        SELECT id, migrated_id, name, destination_id, category_id, pipeline_run_id,
               created_at, updated_at, source_created_at, source_updated_at, extracted_at,
               source_system, external_id, is_active, currency, description,
               resort_name, hotel_name, currency_iso_code, current_status, website,
               notes, email, resort_id
        FROM hotel
        ORDER BY id
    """)
    
    # Drop old table and rename
    op.drop_table('hotel')
    op.execute('ALTER TABLE hotel_new RENAME TO hotel')
    
    # Recreate indexes
    op.create_index('idx_hotel_migrated_id', 'hotel', ['migrated_id'])
    op.create_index('idx_hotel_name', 'hotel', ['name'])
    op.create_index('idx_hotel_destination_id', 'hotel', ['destination_id'])
    op.create_index('idx_hotel_category_id', 'hotel', ['category_id'])
    op.create_index('idx_hotel_pipeline_run_id', 'hotel', ['pipeline_run_id'])
    
    # 3. PRODUCT_CATEGORY TABLE
    print("Restructuring product_category table...")
    
    # Create new product_category table with id as first column
    op.execute("""
        CREATE TABLE product_category_new (
            id VARCHAR(255) PRIMARY KEY,
            migrated_id VARCHAR(255),
            name VARCHAR(255),
            pipeline_run_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
        )
    """)
    
    # Copy data preserving existing IDs
    op.execute("""
        INSERT INTO product_category_new 
        SELECT id, migrated_id, name, pipeline_run_id, created_at, updated_at
        FROM product_category
        ORDER BY created_at
    """)
    
    # Drop old table and rename
    op.drop_table('product_category')
    op.execute('ALTER TABLE product_category_new RENAME TO product_category')
    
    # Recreate indexes
    op.create_index('idx_product_category_migrated_id', 'product_category', ['migrated_id'])
    op.create_index('idx_product_category_name', 'product_category', ['name'])
    op.create_index('idx_product_category_pipeline_run_id', 'product_category', ['pipeline_run_id'])
    
    # Recreate foreign key constraints
    op.create_foreign_key(
        'fk_destination_pipeline_run_id',
        'destination', 'etl_pipeline_runs',
        ['pipeline_run_id'], ['id'],
        ondelete='SET NULL'
    )
    
    op.create_foreign_key(
        'fk_hotel_destination_id',
        'hotel', 'destination',
        ['destination_id'], ['id'],
        ondelete='SET NULL'
    )
    
    op.create_foreign_key(
        'fk_hotel_category_id',
        'hotel', 'product_category',
        ['category_id'], ['id'],
        ondelete='SET NULL'
    )
    
    op.create_foreign_key(
        'fk_hotel_pipeline_run_id',
        'hotel', 'etl_pipeline_runs',
        ['pipeline_run_id'], ['id'],
        ondelete='SET NULL'
    )
    
    op.create_foreign_key(
        'fk_product_category_pipeline_run_id',
        'product_category', 'etl_pipeline_runs',
        ['pipeline_run_id'], ['id'],
        ondelete='SET NULL'
    )
    
    print("Primary key restructuring completed successfully!")


def downgrade() -> None:
    """Revert the primary key restructuring"""
    
    print("WARNING: This migration restructures table schemas significantly.")
    print("Downgrade not implemented to prevent data loss.")
    pass
