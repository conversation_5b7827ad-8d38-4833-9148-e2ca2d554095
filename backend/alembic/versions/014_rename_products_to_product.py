"""Rename products to product and update schema

Revision ID: 014
Revises: 013
Create Date: 2025-08-12 16:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy import text

# revision identifiers, used by Alembic.
revision = '014'
down_revision = '013'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.rename_table('products', 'product')
    op.add_column('product', sa.Column('handle', sa.Text(), nullable=True))
    op.add_column('product', sa.Column('subtitle', sa.Text(), nullable=True))
    op.add_column('product', sa.Column('is_giftcard', sa.<PERSON>an(), nullable=False, server_default=sa.text('false')))
    op.add_column('product', sa.Column('status', sa.Text(), nullable=True))
    op.add_column('product', sa.Column('thumbnail', sa.Text(), nullable=True))
    op.add_column('product', sa.Column('weight', sa.Text(), nullable=True))
    op.add_column('product', sa.Column('length', sa.Text(), nullable=True))
    op.add_column('product', sa.Column('height', sa.Text(), nullable=True))
    op.add_column('product', sa.Column('width', sa.Text(), nullable=True))
    op.add_column('product', sa.Column('origin_country', sa.Text(), nullable=True))
    op.add_column('product', sa.Column('hs_code', sa.Text(), nullable=True))
    op.add_column('product', sa.Column('mid_code', sa.Text(), nullable=True))
    op.add_column('product', sa.Column('material', sa.Text(), nullable=True))
    op.add_column('product', sa.Column('collection_id', sa.Text(), nullable=True))
    op.add_column('product', sa.Column('type_id', sa.Text(), nullable=True))
    op.add_column('product', sa.Column('discountable', sa.Boolean(), nullable=False, server_default=sa.text('true')))
    op.add_column('product', sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('product', sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.alter_column('product', 'id',
               existing_type=sa.VARCHAR(length=26),
               type_=sa.Text(),
               existing_nullable=False)
    op.alter_column('product', 'title',
               existing_type=sa.VARCHAR(length=255),
               type_=sa.Text(),
               existing_nullable=True)
    op.alter_column('product', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('product', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))

    # Check and drop indexes
    indexes_to_drop = [
        'idx_products_category_id',
        'idx_products_hotel_id',
        'idx_products_is_active',
        'idx_products_migrated_id',
        'idx_products_pipeline_run_id',
        'idx_products_title',
    ]

    for index_name in indexes_to_drop:
        # Check if the index exists before attempting to drop it
        # This query checks for the index in pg_indexes for the current database
        # and ensures it belongs to the 'product' table (which was 'products' before rename)
        result = op.get_bind().execute(text(f"SELECT 1 FROM pg_indexes WHERE indexname = '{index_name}' AND tablename = 'product'")).scalar()
        
        if result:
            print(f"DEBUG: Index {index_name} found on table 'product'. Attempting to drop.")
            op.drop_index(index_name, table_name='product')
        else:
            print(f"DEBUG: Index {index_name} not found on table 'product'. Skipping drop.")

    # Check and drop foreign key constraints
    constraints_to_drop = [
        'fk_products_category_id',
        'fk_products_hotel_id',
        'fk_products_pipeline_run_id',
    ]

    for constraint_name in constraints_to_drop:
        # Check if the constraint exists before attempting to drop it
        # This query checks for the constraint in information_schema.table_constraints
        # and ensures it belongs to the 'product' table
        result = op.get_bind().execute(text(f"""
            SELECT 1 FROM information_schema.table_constraints
            WHERE constraint_name = '{constraint_name}'
            AND table_name = 'product'
            AND constraint_type = 'FOREIGN KEY'
        """)).scalar()
        
        if result:
            print(f"DEBUG: Foreign key constraint {constraint_name} found on table 'product'. Attempting to drop.")
            op.drop_constraint(constraint_name, 'product', type_='foreignkey')
        else:
            print(f"DEBUG: Foreign key constraint {constraint_name} not found on table 'product'. Skipping drop.")

    op.drop_column('product', 'source_created_at')
    op.drop_column('product', 'is_active')
    op.drop_column('product', 'source_updated_at')
    op.drop_column('product', 'hotel_name')
    op.drop_column('product', 'migrated_id')
    op.drop_column('product', 'pipeline_run_id')
    op.drop_column('product', 'category_id')
    op.drop_column('product', 'extracted_at')
    op.drop_column('product', 'source_system')
    op.drop_column('product', 'hotel_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('product', sa.Column('hotel_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('product', sa.Column('source_system', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    op.add_column('product', sa.Column('extracted_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True))
    op.add_column('product', sa.Column('category_id', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    op.add_column('product', sa.Column('pipeline_run_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('product', sa.Column('migrated_id', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    op.add_column('product', sa.Column('hotel_name', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    op.add_column('product', sa.Column('source_updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True))
    op.add_column('product', sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('product', sa.Column('source_created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True))
    op.create_foreign_key('fk_products_pipeline_run_id', 'product', 'etl_pipeline_runs', ['pipeline_run_id'], ['id'], ondelete='SET NULL')
    op.create_foreign_key('fk_products_hotel_id', 'product', 'hotel', ['hotel_id'], ['id'], ondelete='SET NULL')
    op.create_foreign_key('fk_products_category_id', 'product', 'product_category', ['category_id'], ['id'], ondelete='SET NULL')
    op.create_index('idx_products_title', 'product', ['title'], unique=False)
    op.create_index('idx_products_pipeline_run_id', 'product', ['pipeline_run_id'], unique=False)
    op.create_index('idx_products_migrated_id', 'product', ['migrated_id'], unique=False)
    op.create_index('idx_products_is_active', 'product', ['is_active'], unique=False)
    op.create_index('idx_products_hotel_id', 'product', ['hotel_id'], unique=False)
    op.create_index('idx_products_category_id', 'product', ['category_id'], unique=False)
    op.alter_column('product', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('product', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('product', 'title',
               existing_type=sa.Text(),
               type_=sa.VARCHAR(length=255),
               existing_nullable=True)
    op.alter_column('product', 'id',
               existing_type=sa.Text(),
               type_=sa.VARCHAR(length=26),
               existing_nullable=False)
    op.drop_column('product', 'metadata')
    op.drop_column('product', 'deleted_at')
    op.drop_column('product', 'discountable')
    op.drop_column('product', 'type_id')
    op.drop_column('product', 'collection_id')
    op.drop_column('product', 'material')
    op.drop_column('product', 'mid_code')
    op.drop_column('product', 'hs_code')
    op.drop_column('product', 'origin_country')
    op.drop_column('product', 'width')
    op.drop_column('product', 'height')
    op.drop_column('product', 'length')
    op.drop_column('product', 'weight')
    op.drop_column('product', 'thumbnail')
    op.drop_column('product', 'status')
    op.drop_column('product', 'is_giftcard')
    op.drop_column('product', 'subtitle')
    op.drop_column('product', 'handle')
    op.rename_table('product', 'products')