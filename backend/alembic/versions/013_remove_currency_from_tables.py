"""Remove currency and currency_code from tables

Revision ID: 013
Revises: 012
Create Date: 2025-08-12 15:31:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '013'
down_revision = '012'
branch_labels = None
depends_on = None


def upgrade():
    op.drop_column('products', 'currency_code')


def downgrade():
    op.add_column('products', sa.Column('currency_code', sa.String(), nullable=True))