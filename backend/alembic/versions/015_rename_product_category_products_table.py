"""Rename product_category_products table

Revision ID: 015
Revises: 014
Create Date: 2025-08-12 22:41:56.958769

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '015'
down_revision: Union[str, None] = '014'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    print("Renaming 'product_category_products' to 'product_category_product'...")
    op.rename_table('product_category_products', 'product_category_product')
    print("Table 'product_category_products' renamed to 'product_category_product' successfully.")
    # ### end Alembic commands ###


def downgrade() -> None:
    print("Renaming 'product_category_product' back to 'product_category_products'...")
    op.rename_table('product_category_product', 'product_category_products')
    print("Table 'product_category_product' renamed to 'product_category_products' successfully.")
