"""Create product_category_product junction table

Revision ID: 009
Revises: 008
Create Date: 2025-08-11 18:45:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '009'
down_revision = '008'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """
    Create product_category_product junction table for many-to-many relationship
    between product categories and product with automatic population during ETL
    """
    
    print("Creating product_category_product junction table...")
    
    # Create junction table with exactly 2 fields as specified
    op.create_table(
        'product_category_product',
        sa.Column('product_category_id', sa.String(255), nullable=False, index=True),
        sa.Column('product_id', sa.String(26), nullable=False, index=True),
    )
    
    # Create composite primary key on both columns
    op.create_primary_key(
        'pk_product_category_product',
        'product_category_product',
        ['product_category_id', 'product_id']
    )
    
    # Create foreign key constraints
    op.create_foreign_key(
        'fk_product_category_product_category_id',
        'product_category_product', 'product_category',
        ['product_category_id'], ['id'],
        ondelete='CASCADE'
    )
    
    op.create_foreign_key(
        'fk_product_category_product_product_id',
        'product_category_product', 'product',
        ['product_id'], ['id'],
        ondelete='CASCADE'
    )
    
    # Create indexes for performance
    op.create_index(
        'idx_product_category_product_category_id',
        'product_category_product',
        ['product_category_id']
    )
    
    op.create_index(
        'idx_product_category_product_product_id',
        'product_category_product',
        ['product_id']
    )
    
    # Create unique constraint to prevent duplicate relationships
    op.create_unique_constraint(
        'uq_product_category_product',
        'product_category_product',
        ['product_category_id', 'product_id']
    )
    
    print("Junction table created successfully!")
    print("- Composite primary key: (product_category_id, product_id)")
    print("- Foreign key constraints established:")
    print("  * product_category_product.product_category_id → product_category.id")
    print("  * product_category_product.product_id → product.id")
    print("- Indexes created for performance optimization")
    print("- Unique constraint prevents duplicate relationships")


def downgrade() -> None:
    """Drop the product_category_product junction table"""
    
    print("Dropping product_category_product junction table...")
    
    # Drop unique constraint
    op.drop_constraint('uq_product_category_product', 'product_category_product', type_='unique')
    
    # Drop indexes
    op.drop_index('idx_product_category_product_product_id', 'product_category_product')
    op.drop_index('idx_product_category_product_category_id', 'product_category_product')
    
    # Drop foreign key constraints
    op.drop_constraint('fk_product_category_product_product_id', 'product_category_product', type_='foreignkey')
    op.drop_constraint('fk_product_category_product_category_id', 'product_category_product', type_='foreignkey')
    
    # Drop primary key
    op.drop_constraint('pk_product_category_product', 'product_category_product', type_='primary')
    
    # Drop the table
    op.drop_table('product_category_product')
    
    print("Junction table dropped successfully!")
