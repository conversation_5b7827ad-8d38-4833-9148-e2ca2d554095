"""Convert destination and hotel primary keys to 26-character alphanumeric string IDs

Revision ID: 011
Revises: 010
Create Date: 2025-08-12 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
import string
import random
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '011'
down_revision = '010'
branch_labels = None
depends_on = None


def generate_destination_id():
    """Generate a 26-character alphanumeric ID for destination"""
    return ''.join(random.choices(string.ascii_letters + string.digits, k=26))


def generate_hotel_id():
    """Generate a 26-character alphanumeric ID for hotel"""
    return ''.join(random.choices(string.ascii_letters + string.digits, k=26))


def upgrade() -> None:
    """
    Convert destination and hotel tables to use 26-character alphanumeric string IDs
    and update all foreign key references throughout the database.
    """
    
    print("Starting conversion of destination and hotel to string IDs...")
    
    # Get database connection for data operations
    connection = op.get_bind()
    
    # ========================================
    # STEP 1: Convert destination table
    # ========================================
    print("Step 1: Converting destination table...")
    
    # Add new string ID column to destination
    op.add_column('destination', sa.Column('new_id', sa.String(26), nullable=True))
    
    # Generate new string IDs for existing destination
    destination_result = connection.execute(sa.text("SELECT id FROM destination ORDER BY id"))
    destination = destination_result.fetchall()
    
    destination_id_mapping = {}
    for dest in destination:
        old_id = dest[0]
        new_id = generate_destination_id()
        destination_id_mapping[old_id] = new_id
        connection.execute(
            sa.text("UPDATE destination SET new_id = :new_id WHERE id = :old_id"),
            {"new_id": new_id, "old_id": old_id}
        )
    
    print(f"Generated new IDs for {len(destination)} destination")
    
    # ========================================
    # STEP 2: Convert hotel table
    # ========================================
    print("Step 2: Converting hotel table...")
    
    # Add new string ID column to hotel
    op.add_column('hotel', sa.Column('new_id', sa.String(26), nullable=True))
    op.add_column('hotel', sa.Column('new_destination_id', sa.String(26), nullable=True))
    
    # Generate new string IDs for existing hotel and update destination references
    hotel_result = connection.execute(sa.text("SELECT id, destination_id FROM hotel ORDER BY id"))
    hotel = hotel_result.fetchall()
    
    hotel_id_mapping = {}
    for hotel in hotel:
        old_id = hotel[0]
        old_destination_id = hotel[1]
        new_id = generate_hotel_id()
        hotel_id_mapping[old_id] = new_id
        
        # Map destination_id to new string ID
        new_destination_id = destination_id_mapping.get(old_destination_id) if old_destination_id else None
        
        connection.execute(
            sa.text("UPDATE hotel SET new_id = :new_id, new_destination_id = :new_dest_id WHERE id = :old_id"),
            {"new_id": new_id, "new_dest_id": new_destination_id, "old_id": old_id}
        )
    
    print(f"Generated new IDs for {len(hotel)} hotel")
    
    # ========================================
    # STEP 3: Update foreign key references in product table
    # ========================================
    print("Step 3: Updating product table foreign key references...")
    
    # Add new foreign key column for hotel
    op.add_column('product', sa.Column('new_hotel_id', sa.String(26), nullable=True))
    
    # Update product.hotel_id references
    product_result = connection.execute(sa.text("SELECT id, hotel_id FROM product WHERE hotel_id IS NOT NULL"))
    product = product_result.fetchall()
    
    for product in product:
        product_id = product[0]
        old_hotel_id = product[1]
        new_hotel_id = hotel_id_mapping.get(old_hotel_id)
        
        if new_hotel_id:
            connection.execute(
                sa.text("UPDATE product SET new_hotel_id = :new_hotel_id WHERE id = :product_id"),
                {"new_hotel_id": new_hotel_id, "product_id": product_id}
            )
    
    print(f"Updated hotel_id references for {len(product)} product")
    
    # ========================================
    # STEP 4: Update foreign key references in product_variant table
    # ========================================
    print("Step 4: Updating product_variant table foreign key references...")
    
    # Note: product_variant references product.id (string) and doesn't directly reference hotel
    # But we should verify this table exists and handle any hotel references if they exist
    
    # Check if product_variant table exists and has any hotel_id references
    inspector = sa.inspect(connection)
    if 'product_variant' in inspector.get_table_names():
        columns = [col['name'] for col in inspector.get_columns('product_variant')]
        if 'hotel_id' in columns:
            # Add new foreign key column for hotel if it exists
            op.add_column('product_variant', sa.Column('new_hotel_id', sa.String(26), nullable=True))
            
            # Update product_variant.hotel_id references if they exist
            variants_result = connection.execute(sa.text("SELECT id, hotel_id FROM product_variant WHERE hotel_id IS NOT NULL"))
            variants = variants_result.fetchall()
            
            for variant in variants:
                variant_id = variant[0]
                old_hotel_id = variant[1]
                new_hotel_id = hotel_id_mapping.get(old_hotel_id)
                
                if new_hotel_id:
                    connection.execute(
                        sa.text("UPDATE product_variant SET new_hotel_id = :new_hotel_id WHERE id = :variant_id"),
                        {"new_hotel_id": new_hotel_id, "variant_id": variant_id}
                    )
            
            print(f"Updated hotel_id references for {len(variants)} product variants")
    
    # ========================================
    # STEP 5: Drop old foreign key constraints
    # ========================================
    print("Step 5: Dropping old foreign key constraints...")
    
    # Drop foreign key constraints that reference the old integer IDs
    try:
        op.drop_constraint('fk_hotel_destination_id', 'hotel', type_='foreignkey')
    except:
        pass  # Constraint might not exist or have different name
    
    try:
        op.drop_constraint('fk_product_hotel_id', 'product', type_='foreignkey')
    except:
        pass
    
    if 'product_variant' in inspector.get_table_names():
        columns = [col['name'] for col in inspector.get_columns('product_variant')]
        if 'hotel_id' in columns:
            try:
                op.drop_constraint('fk_product_variant_hotel_id', 'product_variant', type_='foreignkey')
            except:
                pass
    
    # ========================================
    # STEP 6: Drop old columns and rename new columns
    # ========================================
    print("Step 6: Finalizing column changes...")
    
    # Drop old integer ID columns and rename new string columns
    
    # For destination table
    op.drop_column('destination', 'id')
    op.alter_column('destination', 'new_id', new_column_name='id', nullable=False)
    
    # For hotel table  
    op.drop_column('hotel', 'id')
    op.drop_column('hotel', 'destination_id')
    op.alter_column('hotel', 'new_id', new_column_name='id', nullable=False)
    op.alter_column('hotel', 'new_destination_id', new_column_name='destination_id', nullable=True)
    
    # For product table
    op.drop_column('product', 'hotel_id')
    op.alter_column('product', 'new_hotel_id', new_column_name='hotel_id', nullable=True)
    
    # For product_variant table (if hotel_id column exists)
    if 'product_variant' in inspector.get_table_names():
        columns = [col['name'] for col in inspector.get_columns('product_variant')]
        if 'hotel_id' in columns:
            op.drop_column('product_variant', 'hotel_id')
            op.alter_column('product_variant', 'new_hotel_id', new_column_name='hotel_id', nullable=True)
    
    # ========================================
    # STEP 7: Create new primary key constraints and indexes
    # ========================================
    print("Step 7: Creating new primary key constraints and indexes...")
    
    # Create primary key constraints
    op.create_primary_key('pk_destination', 'destination', ['id'])
    op.create_primary_key('pk_hotel', 'hotel', ['id'])
    
    # Create indexes
    op.create_index('idx_destination_id', 'destination', ['id'])
    op.create_index('idx_hotel_id', 'hotel', ['id'])
    op.create_index('idx_hotel_destination_id_new', 'hotel', ['destination_id'])
    op.create_index('idx_product_hotel_id_new', 'product', ['hotel_id'])
    
    if 'product_variant' in inspector.get_table_names():
        columns = [col['name'] for col in inspector.get_columns('product_variant')]
        if 'hotel_id' in columns:
            op.create_index('idx_product_variant_hotel_id_new', 'product_variant', ['hotel_id'])
    
    # ========================================
    # STEP 8: Create new foreign key constraints
    # ========================================
    print("Step 8: Creating new foreign key constraints...")
    
    # Create foreign key constraints with new string IDs
    op.create_foreign_key(
        'fk_hotel_destination_id_new',
        'hotel', 'destination',
        ['destination_id'], ['id'],
        ondelete='SET NULL'
    )
    
    op.create_foreign_key(
        'fk_product_hotel_id_new',
        'product', 'hotel',
        ['hotel_id'], ['id'],
        ondelete='SET NULL'
    )
    
    if 'product_variant' in inspector.get_table_names():
        columns = [col['name'] for col in inspector.get_columns('product_variant')]
        if 'hotel_id' in columns:
            op.create_foreign_key(
                'fk_product_variant_hotel_id_new',
                'product_variant', 'hotel',
                ['hotel_id'], ['id'],
                ondelete='SET NULL'
            )
    
    print("✅ Successfully converted destination and hotel to string IDs!")
    print(f"   - Converted {len(destination)} destination")
    print(f"   - Converted {len(hotel)} hotel")
    print(f"   - Updated {len(product)} product")
    print("   - All foreign key relationships maintained")


def downgrade() -> None:
    """
    Rollback the string ID conversion - convert back to integer IDs
    WARNING: This will lose the string ID values and regenerate integer IDs
    """
    
    print("Rolling back string ID conversion...")
    print("⚠️  WARNING: This will lose string ID values and regenerate integer IDs")
    
    connection = op.get_bind()
    inspector = sa.inspect(connection)
    
    # ========================================
    # STEP 1: Drop new foreign key constraints
    # ========================================
    print("Step 1: Dropping string ID foreign key constraints...")
    
    try:
        op.drop_constraint('fk_hotel_destination_id_new', 'hotel', type_='foreignkey')
    except:
        pass
    
    try:
        op.drop_constraint('fk_product_hotel_id_new', 'product', type_='foreignkey')
    except:
        pass
    
    if 'product_variant' in inspector.get_table_names():
        try:
            op.drop_constraint('fk_product_variant_hotel_id_new', 'product_variant', type_='foreignkey')
        except:
            pass
    
    # ========================================
    # STEP 2: Add temporary integer ID columns
    # ========================================
    print("Step 2: Adding temporary integer ID columns...")
    
    # Add temporary integer columns
    op.add_column('destination', sa.Column('temp_id', sa.Integer, nullable=True))
    op.add_column('hotel', sa.Column('temp_id', sa.Integer, nullable=True))
    op.add_column('hotel', sa.Column('temp_destination_id', sa.Integer, nullable=True))
    op.add_column('product', sa.Column('temp_hotel_id', sa.Integer, nullable=True))
    
    if 'product_variant' in inspector.get_table_names():
        columns = [col['name'] for col in inspector.get_columns('product_variant')]
        if 'hotel_id' in columns:
            op.add_column('product_variant', sa.Column('temp_hotel_id', sa.Integer, nullable=True))
    
    # ========================================
    # STEP 3: Generate new integer IDs and create mappings
    # ========================================
    print("Step 3: Generating new integer IDs...")
    
    # Create sequences for auto-incrementing IDs
    op.execute("CREATE SEQUENCE IF NOT EXISTS destination_id_seq")
    op.execute("CREATE SEQUENCE IF NOT EXISTS hotel_id_seq")
    
    # Generate integer IDs for destination
    destination_result = connection.execute(sa.text("SELECT id FROM destination ORDER BY created_at"))
    destination = destination_result.fetchall()
    
    destination_mapping = {}
    for i, dest in enumerate(destination, 1):
        old_string_id = dest[0]
        new_int_id = i
        destination_mapping[old_string_id] = new_int_id
        connection.execute(
            sa.text("UPDATE destination SET temp_id = :new_id WHERE id = :old_id"),
            {"new_id": new_int_id, "old_id": old_string_id}
        )
    
    # Update sequence to continue from max ID
    if destination:
        op.execute(f"SELECT setval('destination_id_seq', {len(destination)})")
    
    # Generate integer IDs for hotel
    hotel_result = connection.execute(sa.text("SELECT id, destination_id FROM hotel ORDER BY created_at"))
    hotel = hotel_result.fetchall()
    
    hotel_mapping = {}
    for i, hotel in enumerate(hotel, 1):
        old_string_id = hotel[0]
        old_dest_string_id = hotel[1]
        new_int_id = i
        hotel_mapping[old_string_id] = new_int_id
        
        # Map destination reference back to integer
        new_dest_int_id = destination_mapping.get(old_dest_string_id) if old_dest_string_id else None
        
        connection.execute(
            sa.text("UPDATE hotel SET temp_id = :new_id, temp_destination_id = :new_dest_id WHERE id = :old_id"),
            {"new_id": new_int_id, "new_dest_id": new_dest_int_id, "old_id": old_string_id}
        )
    
    # Update sequence to continue from max ID
    if hotel:
        op.execute(f"SELECT setval('hotel_id_seq', {len(hotel)})")
    
    # Update product table references
    product_result = connection.execute(sa.text("SELECT id, hotel_id FROM product WHERE hotel_id IS NOT NULL"))
    product = product_result.fetchall()
    
    for product in product:
        product_id = product[0]
        old_hotel_string_id = product[1]
        new_hotel_int_id = hotel_mapping.get(old_hotel_string_id)
        
        if new_hotel_int_id:
            connection.execute(
                sa.text("UPDATE product SET temp_hotel_id = :new_hotel_id WHERE id = :product_id"),
                {"new_hotel_id": new_hotel_int_id, "product_id": product_id}
            )
    
    # Update product_variant table references if they exist
    if 'product_variant' in inspector.get_table_names():
        columns = [col['name'] for col in inspector.get_columns('product_variant')]
        if 'hotel_id' in columns:
            variants_result = connection.execute(sa.text("SELECT id, hotel_id FROM product_variant WHERE hotel_id IS NOT NULL"))
            variants = variants_result.fetchall()
            
            for variant in variants:
                variant_id = variant[0]
                old_hotel_string_id = variant[1]
                new_hotel_int_id = hotel_mapping.get(old_hotel_string_id)
                
                if new_hotel_int_id:
                    connection.execute(
                        sa.text("UPDATE product_variant SET temp_hotel_id = :new_hotel_id WHERE id = :variant_id"),
                        {"new_hotel_id": new_hotel_int_id, "variant_id": variant_id}
                    )
    
    # ========================================
    # STEP 4: Drop string columns and rename integer columns
    # ========================================
    print("Step 4: Finalizing rollback...")
    
    # Drop primary key constraints
    op.drop_constraint('pk_destination', 'destination', type_='primary')
    op.drop_constraint('pk_hotel', 'hotel', type_='primary')
    
    # Drop string ID columns and rename integer columns
    op.drop_column('destination', 'id')
    op.alter_column('destination', 'temp_id', new_column_name='id', nullable=False)
    
    op.drop_column('hotel', 'id')
    op.drop_column('hotel', 'destination_id')
    op.alter_column('hotel', 'temp_id', new_column_name='id', nullable=False)
    op.alter_column('hotel', 'temp_destination_id', new_column_name='destination_id', nullable=True)
    
    op.drop_column('product', 'hotel_id')
    op.alter_column('product', 'temp_hotel_id', new_column_name='hotel_id', nullable=True)
    
    if 'product_variant' in inspector.get_table_names():
        columns = [col['name'] for col in inspector.get_columns('product_variant')]
        if 'temp_hotel_id' in [col['name'] for col in inspector.get_columns('product_variant')]:
            op.drop_column('product_variant', 'hotel_id')
            op.alter_column('product_variant', 'temp_hotel_id', new_column_name='hotel_id', nullable=True)
    
    # Recreate primary key constraints with integer IDs
    op.create_primary_key('destination_pkey', 'destination', ['id'])
    op.create_primary_key('hotel_pkey', 'hotel', ['id'])
    
    # Set default values for auto-increment
    op.alter_column('destination', 'id', server_default=sa.text("nextval('destination_id_seq')"))
    op.alter_column('hotel', 'id', server_default=sa.text("nextval('hotel_id_seq')"))
    
    # Recreate foreign key constraints
    op.create_foreign_key(
        'fk_hotel_destination_id',
        'hotel', 'destination',
        ['destination_id'], ['id'],
        ondelete='SET NULL'
    )
    
    op.create_foreign_key(
        'fk_product_hotel_id',
        'product', 'hotel',
        ['hotel_id'], ['id'],
        ondelete='SET NULL'
    )
    
    if 'product_variant' in inspector.get_table_names():
        columns = [col['name'] for col in inspector.get_columns('product_variant')]
        if 'hotel_id' in columns:
            op.create_foreign_key(
                'fk_product_variant_hotel_id',
                'product_variant', 'hotel',
                ['hotel_id'], ['id'],
                ondelete='SET NULL'
            )
    
    print("✅ Successfully rolled back to integer IDs!")
    print(f"   - Converted {len(destination)} destination back to integer IDs")
    print(f"   - Converted {len(hotel)} hotel back to integer IDs")
    print("   - All foreign key relationships restored")
