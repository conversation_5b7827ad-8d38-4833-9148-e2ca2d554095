"""Add product variant fields to match e-commerce schema

Revision ID: 016_add_product_variant_fields
Revises: 015_rename_product_category_products_table
Create Date: 2025-08-13 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '016_add_product_variant_fields'
down_revision = '015'
branch_labels = None
depends_on = None


def upgrade():
    """Add new fields to product_variant table to match e-commerce schema"""
    print("Adding new fields to product_variant table...")
    
    # Add product variant core fields
    op.add_column('product_variant', sa.Column('title', sa.Text(), nullable=True))
    op.add_column('product_variant', sa.Column('sku', sa.Text(), nullable=True))
    op.add_column('product_variant', sa.Column('barcode', sa.Text(), nullable=True))
    op.add_column('product_variant', sa.Column('ean', sa.Text(), nullable=True))
    op.add_column('product_variant', sa.Column('upc', sa.Text(), nullable=True))
    
    # Add inventory management fields
    op.add_column('product_variant', sa.Column('allow_backorder', sa.Boolean(), nullable=True, default=False))
    op.add_column('product_variant', sa.Column('manage_inventory', sa.Boolean(), nullable=True, default=True))
    
    # Add product classification fields
    op.add_column('product_variant', sa.Column('hs_code', sa.Text(), nullable=True))
    op.add_column('product_variant', sa.Column('origin_country', sa.Text(), nullable=True))
    op.add_column('product_variant', sa.Column('mid_code', sa.Text(), nullable=True))
    op.add_column('product_variant', sa.Column('material', sa.Text(), nullable=True))
    
    # Add physical dimensions (in mm for length/width/height, grams for weight)
    op.add_column('product_variant', sa.Column('weight', sa.Integer(), nullable=True))
    op.add_column('product_variant', sa.Column('length', sa.Integer(), nullable=True))
    op.add_column('product_variant', sa.Column('height', sa.Integer(), nullable=True))
    op.add_column('product_variant', sa.Column('width', sa.Integer(), nullable=True))
    
    # Add additional fields
    op.add_column('product_variant', sa.Column('additional_metadata', postgresql.JSON(astext_type=sa.Text()), nullable=True))
    op.add_column('product_variant', sa.Column('variant_rank', sa.Integer(), nullable=True))
    
    # Create indexes for performance on commonly queried fields
    op.create_index('idx_product_variant_sku', 'product_variant', ['sku'])
    op.create_index('idx_product_variant_barcode', 'product_variant', ['barcode'])
    op.create_index('idx_product_variant_ean', 'product_variant', ['ean'])
    op.create_index('idx_product_variant_upc', 'product_variant', ['upc'])
    op.create_index('idx_product_variant_variant_rank', 'product_variant', ['variant_rank'])
    
    print("Successfully added new fields to product_variant table:")
    print("- Core fields: title, sku, barcode, ean, upc")
    print("- Inventory: allow_backorder, manage_inventory")
    print("- Classification: hs_code, origin_country, mid_code, material")
    print("- Dimensions: weight, length, height, width")
    print("- Additional: additional_metadata, variant_rank")
    print("- Indexes created for performance optimization")


def downgrade():
    """Remove the added fields from product_variant table"""
    print("Removing added fields from product_variant table...")
    
    # Drop indexes first
    op.drop_index('idx_product_variant_variant_rank', 'product_variant')
    op.drop_index('idx_product_variant_upc', 'product_variant')
    op.drop_index('idx_product_variant_ean', 'product_variant')
    op.drop_index('idx_product_variant_barcode', 'product_variant')
    op.drop_index('idx_product_variant_sku', 'product_variant')
    
    # Drop columns
    op.drop_column('product_variant', 'variant_rank')
    op.drop_column('product_variant', 'additional_metadata')
    op.drop_column('product_variant', 'width')
    op.drop_column('product_variant', 'height')
    op.drop_column('product_variant', 'length')
    op.drop_column('product_variant', 'weight')
    op.drop_column('product_variant', 'material')
    op.drop_column('product_variant', 'mid_code')
    op.drop_column('product_variant', 'origin_country')
    op.drop_column('product_variant', 'hs_code')
    op.drop_column('product_variant', 'manage_inventory')
    op.drop_column('product_variant', 'allow_backorder')
    op.drop_column('product_variant', 'upc')
    op.drop_column('product_variant', 'ean')
    op.drop_column('product_variant', 'barcode')
    op.drop_column('product_variant', 'sku')
    op.drop_column('product_variant', 'title')
    
    print("Successfully removed added fields from product_variant table")
