"""Add ETL pipeline tables

Revision ID: 003_add_etl_pipeline_tables
Revises: a7e95aa2471d
Create Date: 2025-08-07 11:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '003_add_etl_pipeline_tables'
down_revision = 'a7e95aa2471d'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create destination table for Resort Pipeline
    op.create_table('destination',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('country', sa.String(255), nullable=True),
        sa.Column('name', sa.String(255), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create index on country for better query performance
    op.create_index('idx_destination_country', 'destination', ['country'])
    
    # Create ETL pipelines table
    op.create_table('etl_pipelines',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('source_type', sa.String(100), nullable=False),  # 'salesforce', 'api', etc.
        sa.Column('source_object', sa.String(255), nullable=True),  # Salesforce object name
        sa.Column('source_fields', postgresql.JSONB(), nullable=True),  # List of source fields
        sa.Column('destination_type', sa.String(100), nullable=False),  # 'postgresql', 'minio', etc.
        sa.Column('destination_table', sa.String(255), nullable=True),  # Target table name
        sa.Column('destination_fields', postgresql.JSONB(), nullable=True),  # Field mappings
        sa.Column('transformation_config', postgresql.JSONB(), nullable=True),  # Transformation rules
        sa.Column('schedule_config', postgresql.JSONB(), nullable=True),  # Scheduling configuration
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create unique index on pipeline name
    op.create_index('idx_etl_pipelines_name', 'etl_pipelines', ['name'], unique=True)
    
    # Create ETL pipeline runs table
    op.create_table('etl_pipeline_runs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('pipeline_id', sa.Integer(), nullable=False),
        sa.Column('status', sa.String(50), nullable=False),  # 'running', 'completed', 'failed', 'cancelled'
        sa.Column('started_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.Column('records_extracted', sa.Integer(), nullable=True),
        sa.Column('records_transformed', sa.Integer(), nullable=True),
        sa.Column('records_loaded', sa.Integer(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('logs', postgresql.JSONB(), nullable=True),  # Detailed execution logs
        sa.Column('raw_data_path', sa.String(500), nullable=True),  # MinIO path to raw data
        sa.Column('processed_data_path', sa.String(500), nullable=True),  # MinIO path to processed data
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['pipeline_id'], ['etl_pipelines.id'], ondelete='CASCADE')
    )
    
    # Create indexes for pipeline runs
    op.create_index('idx_etl_pipeline_runs_pipeline_id', 'etl_pipeline_runs', ['pipeline_id'])
    op.create_index('idx_etl_pipeline_runs_status', 'etl_pipeline_runs', ['status'])
    op.create_index('idx_etl_pipeline_runs_started_at', 'etl_pipeline_runs', ['started_at'])


def downgrade() -> None:
    # Drop tables in reverse order
    op.drop_table('etl_pipeline_runs')
    op.drop_table('etl_pipelines')
    op.drop_table('destination')
