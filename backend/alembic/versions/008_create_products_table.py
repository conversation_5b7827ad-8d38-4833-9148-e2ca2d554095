"""Create product table for Room Type Pipeline

Revision ID: 008
Revises: 007
Create Date: 2025-08-11 18:30:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '008'
down_revision = '007'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """
    Create product table for Room Type Pipeline with proper schema:
    - Primary key 'id' as first column with prod_ prefix
    - Foreign key relationships to hotel and product_category
    - Comprehensive field mapping from Room_Type__c
    """
    
    print("Creating product table for Room Type Pipeline...")
    
    # Create product table with id as first column
    op.create_table(
        'product',
        sa.Column('id', sa.String(26), primary_key=True, index=True),  # prod_ prefixed ID
        sa.Column('migrated_id', sa.String(255), nullable=True, index=True),  # Salesforce Room_Type__c.Id
        sa.Column('title', sa.String(255), nullable=True, index=True),  # Room_Type__c.Name
        sa.Column('description', sa.Text, nullable=True),  # Room_Type__c description field
        sa.Column('hotel_name', sa.String(255), nullable=True),  # Resolved from Hotel__c.Name
        sa.Column('hotel_id', sa.Integer, nullable=True, index=True),  # FK to hotel.id
        sa.Column('currency', sa.String(10), nullable=True),  # Room_Type__c.CurrencyIsoCode
        sa.Column('category_id', sa.String(255), nullable=True, index=True),  # FK to product_category.id
        sa.Column('pipeline_run_id', sa.Integer, nullable=True, index=True),  # FK to etl_pipeline_runs.id
        sa.Column('created_at', sa.DateTime, server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('updated_at', sa.DateTime, server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('source_created_at', sa.DateTime, nullable=True),  # Room_Type__c.CreatedDate
        sa.Column('source_updated_at', sa.DateTime, nullable=True),  # Room_Type__c.LastModifiedDate
        sa.Column('extracted_at', sa.DateTime, nullable=True),  # Extraction timestamp
        sa.Column('source_system', sa.String(255), nullable=True),  # Default 'salesforce'
        sa.Column('external_id', sa.String(255), nullable=True),  # Room_Type__c.Id
        sa.Column('is_active', sa.Boolean, nullable=True, index=True),  # !Room_Type__c.IsDeleted
    )
    
    # Create indexes for performance
    op.create_index('idx_product_migrated_id', 'product', ['migrated_id'])
    op.create_index('idx_product_title', 'product', ['title'])
    op.create_index('idx_product_hotel_id', 'product', ['hotel_id'])
    op.create_index('idx_product_category_id', 'product', ['category_id'])
    op.create_index('idx_product_pipeline_run_id', 'product', ['pipeline_run_id'])
    op.create_index('idx_product_is_active', 'product', ['is_active'])
    op.create_index('idx_product_currency', 'product', ['currency'])
    
    # Create foreign key constraints
    op.create_foreign_key(
        'fk_product_hotel_id',
        'product', 'hotel',
        ['hotel_id'], ['id'],
        ondelete='SET NULL'
    )
    
    op.create_foreign_key(
        'fk_product_category_id',
        'product', 'product_category',
        ['category_id'], ['id'],
        ondelete='SET NULL'
    )
    
    op.create_foreign_key(
        'fk_product_pipeline_run_id',
        'product', 'etl_pipeline_runs',
        ['pipeline_run_id'], ['id'],
        ondelete='SET NULL'
    )
    
    print("product table created successfully!")
    print("- Primary key 'id' positioned as first column")
    print("- Foreign key relationships established:")
    print("  * product.hotel_id → hotel.id")
    print("  * product.category_id → product_category.id")
    print("  * product.pipeline_run_id → etl_pipeline_runs.id")
    print("- Indexes created for performance optimization")


def downgrade() -> None:
    """Drop the product table"""
    
    print("Dropping product table...")
    
    # Drop foreign key constraints first
    op.drop_constraint('fk_product_pipeline_run_id', 'product', type_='foreignkey')
    op.drop_constraint('fk_product_category_id', 'product', type_='foreignkey')
    op.drop_constraint('fk_product_hotel_id', 'product', type_='foreignkey')
    
    # Drop indexes
    op.drop_index('idx_product_currency', 'product')
    op.drop_index('idx_product_is_active', 'product')
    op.drop_index('idx_product_pipeline_run_id', 'product')
    op.drop_index('idx_product_category_id', 'product')
    op.drop_index('idx_product_hotel_id', 'product')
    op.drop_index('idx_product_title', 'product')
    op.drop_index('idx_product_migrated_id', 'product')
    
    # Drop the table
    op.drop_table('product')
    
    print("product table dropped successfully!")
