"""Add resort fields to destination table

Revision ID: 004
Revises: 003
Create Date: 2024-08-09 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '004'
down_revision = '003_add_etl_pipeline_tables'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add new fields to destination table
    op.add_column('destination', sa.Column('is_active', sa.<PERSON>(), nullable=True, default=True))
    op.add_column('destination', sa.Column('currency', sa.String(10), nullable=True))
    op.add_column('destination', sa.Column('description', sa.Text(), nullable=True))
    
    # Add indexes for better query performance
    op.create_index('idx_destination_is_active', 'destination', ['is_active'])
    op.create_index('idx_destination_currency', 'destination', ['currency'])


def downgrade() -> None:
    # Remove indexes
    op.drop_index('idx_destination_currency', table_name='destination')
    op.drop_index('idx_destination_is_active', table_name='destination')
    
    # Remove columns
    op.drop_column('destination', 'description')
    op.drop_column('destination', 'currency')
    op.drop_column('destination', 'is_active')
