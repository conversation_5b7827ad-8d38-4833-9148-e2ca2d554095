"""Add hotel pipeline tables and migrated_id to destination

Revision ID: 005
Revises: 004
Create Date: 2025-01-11 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '005'
down_revision = '004'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Check if migrated_id column exists in destination table
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    destination_columns = [col['name'] for col in inspector.get_columns('destination')]

    # Add migrated_id field to destination table if it doesn't exist
    if 'migrated_id' not in destination_columns:
        op.add_column('destination', sa.Column('migrated_id', sa.String(255), nullable=True))
        op.create_index('idx_destination_migrated_id', 'destination', ['migrated_id'])

    # Check if product_category table exists
    existing_tables = inspector.get_table_names()

    # Create product_category table if it doesn't exist
    if 'product_category' not in existing_tables:
        op.create_table('product_category',
            sa.Column('id', sa.Integer(), nullable=False),
            sa.Column('migrated_id', sa.String(255), nullable=True),
            sa.Column('name', sa.String(255), nullable=True),
            sa.Column('pipeline_run_id', sa.Integer(), nullable=True),
            sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
            sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
            sa.PrimaryKeyConstraint('id'),
            sa.ForeignKeyConstraint(['pipeline_run_id'], ['etl_pipeline_runs.id'], ondelete='SET NULL')
        )

        # Create indexes for product_category
        op.create_index('idx_product_category_migrated_id', 'product_category', ['migrated_id'])
        op.create_index('idx_product_category_name', 'product_category', ['name'])
        op.create_index('idx_product_category_pipeline_run_id', 'product_category', ['pipeline_run_id'])

    # Create hotel table if it doesn't exist
    if 'hotel' not in existing_tables:
        op.create_table('hotel',
            sa.Column('id', sa.Integer(), nullable=False),
            sa.Column('migrated_id', sa.String(255), nullable=True),
            sa.Column('name', sa.String(255), nullable=True),
            sa.Column('destination_id', sa.Integer(), nullable=True),
            sa.Column('category_id', sa.Integer(), nullable=True),
            sa.Column('pipeline_run_id', sa.Integer(), nullable=True),
            sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
            sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
            sa.PrimaryKeyConstraint('id'),
            sa.ForeignKeyConstraint(['destination_id'], ['destination.id'], ondelete='SET NULL'),
            sa.ForeignKeyConstraint(['category_id'], ['product_category.id'], ondelete='SET NULL'),
            sa.ForeignKeyConstraint(['pipeline_run_id'], ['etl_pipeline_runs.id'], ondelete='SET NULL')
        )

        # Create indexes for hotel
        op.create_index('idx_hotel_migrated_id', 'hotel', ['migrated_id'])
        op.create_index('idx_hotel_name', 'hotel', ['name'])
        op.create_index('idx_hotel_destination_id', 'hotel', ['destination_id'])
        op.create_index('idx_hotel_category_id', 'hotel', ['category_id'])
        op.create_index('idx_hotel_pipeline_run_id', 'hotel', ['pipeline_run_id'])


def downgrade() -> None:
    # Drop tables in reverse order
    op.drop_table('hotel')
    op.drop_table('product_category')
    
    # Remove migrated_id from destination table
    op.drop_index('idx_destination_migrated_id', table_name='destination')
    op.drop_column('destination', 'migrated_id')
