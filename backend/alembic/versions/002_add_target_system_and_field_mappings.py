"""Add target_system to target_schemas and create field_mappings table

Revision ID: 002
Revises: 001
Create Date: 2024-01-15 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Drop the enum type if it exists (to handle any conflicts)
    op.execute("DROP TYPE IF EXISTS targetsystem CASCADE")

    # Create the enum type
    target_system_enum = postgresql.ENUM('ops', 'crm', name='targetsystem')
    target_system_enum.create(op.get_bind())

    # Add target_system column to target_schemas table
    op.add_column('target_schemas', sa.Column('target_system', target_system_enum, nullable=True))

    # Create index on target_system
    op.create_index(op.f('ix_target_schemas_target_system'), 'target_schemas', ['target_system'], unique=False)

    # Update existing records to have a default target_system (ops)
    op.execute("UPDATE target_schemas SET target_system = 'ops' WHERE target_system IS NULL")

    # Make target_system non-nullable
    op.alter_column('target_schemas', 'target_system', nullable=False)
    
    # Create field_mappings table
    op.create_table('field_mappings',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('target_schema_id', sa.String(length=100), nullable=False),
        sa.Column('mapping_name', sa.String(length=200), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('source_object', sa.String(length=100), nullable=False),
        sa.Column('field_mappings', postgresql.JSON(astext_type=sa.Text()), nullable=False),
        sa.Column('remove_unmapped_fields', sa.Boolean(), nullable=False),
        sa.Column('created_by', sa.String(length=100), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['target_schema_id'], ['target_schemas.id'], )
    )
    
    # Create indexes for field_mappings
    op.create_index(op.f('ix_field_mappings_target_schema_id'), 'field_mappings', ['target_schema_id'], unique=False)
    op.create_index(op.f('ix_field_mappings_source_object'), 'field_mappings', ['source_object'], unique=False)


def downgrade() -> None:
    # Drop field_mappings table and indexes
    op.drop_index(op.f('ix_field_mappings_source_object'), table_name='field_mappings')
    op.drop_index(op.f('ix_field_mappings_target_schema_id'), table_name='field_mappings')
    op.drop_table('field_mappings')

    # Drop target_system column and index from target_schemas
    op.drop_index(op.f('ix_target_schemas_target_system'), table_name='target_schemas')
    op.drop_column('target_schemas', 'target_system')

    # Drop the enum type
    target_system_enum = postgresql.ENUM(name='targetsystem')
    target_system_enum.drop(op.get_bind(), checkfirst=True)
