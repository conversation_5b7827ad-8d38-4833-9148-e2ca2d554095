"""Create product_variant table for Room__c objects

Revision ID: 010
Revises: 009
Create Date: 2025-08-11 19:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '010'
down_revision = '009'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """
    Create product_variant table for handling Salesforce Room__c objects
    with many-to-one relationship to product (room types)
    """
    
    print("Creating product_variant table for Room__c objects...")
    
    # Create product_variant table
    op.create_table(
        'product_variant',
        # Primary key with pvar_ prefix
        sa.Column('id', sa.String(26), primary_key=True, index=True),
        
        # Salesforce Room__c mapping fields
        sa.Column('migrated_id', sa.String(255), nullable=False, unique=True, index=True),
        sa.Column('name', sa.String(255), nullable=False, index=True),
        
        # Foreign key to product table (Room_Type__c relationship)
        sa.Column('product_id', sa.String(26), sa.<PERSON>ey('product.id'), nullable=True, index=True),
        
        # Room-specific fields from Room__c
        sa.Column('room_config_name', sa.String(255), nullable=True),
        sa.Column('is_connected', sa.Boolean, nullable=True, default=False),
        
        # Standard ETL pipeline fields
        sa.Column('pipeline_run_id', sa.Integer, sa.ForeignKey('etl_pipeline_runs.id'), nullable=False, index=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('source_created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('source_updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('extracted_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('source_system', sa.String(50), nullable=False, default='salesforce'),
        sa.Column('external_id', sa.String(255), nullable=True, index=True),
        sa.Column('is_active', sa.Boolean, nullable=False, default=True),
    )
    
    # Create indexes for performance
    op.create_index('idx_product_variant_migrated_id', 'product_variant', ['migrated_id'])
    op.create_index('idx_product_variant_product_id', 'product_variant', ['product_id'])
    op.create_index('idx_product_variant_pipeline_run_id', 'product_variant', ['pipeline_run_id'])
    op.create_index('idx_product_variant_name', 'product_variant', ['name'])
    op.create_index('idx_product_variant_is_active', 'product_variant', ['is_active'])
    
    # Create foreign key constraints
    op.create_foreign_key(
        'fk_product_variant_product_id',
        'product_variant', 'product',
        ['product_id'], ['id'],
        ondelete='SET NULL'  # Allow orphaned variants if product is deleted
    )
    
    op.create_foreign_key(
        'fk_product_variant_pipeline_run_id',
        'product_variant', 'etl_pipeline_runs',
        ['pipeline_run_id'], ['id'],
        ondelete='CASCADE'  # Delete variants if pipeline run is deleted
    )
    
    print("Product variants table created successfully!")
    print("- Primary key: id (pvar_ prefix)")
    print("- Foreign key: product_id → product.id (many-to-one relationship)")
    print("- Foreign key: pipeline_run_id → etl_pipeline_runs.id")
    print("- Indexes created for performance optimization")
    print("- Supports Room__c to Room_Type__c relationship mapping")


def downgrade() -> None:
    """Drop the product_variant table"""
    
    print("Dropping product_variant table...")
    
    # Drop foreign key constraints
    op.drop_constraint('fk_product_variant_pipeline_run_id', 'product_variant', type_='foreignkey')
    op.drop_constraint('fk_product_variant_product_id', 'product_variant', type_='foreignkey')
    
    # Drop indexes
    op.drop_index('idx_product_variant_is_active', 'product_variant')
    op.drop_index('idx_product_variant_name', 'product_variant')
    op.drop_index('idx_product_variant_pipeline_run_id', 'product_variant')
    op.drop_index('idx_product_variant_product_id', 'product_variant')
    op.drop_index('idx_product_variant_migrated_id', 'product_variant')
    
    # Drop the table
    op.drop_table('product_variant')
    
    print("Product variants table dropped successfully!")
