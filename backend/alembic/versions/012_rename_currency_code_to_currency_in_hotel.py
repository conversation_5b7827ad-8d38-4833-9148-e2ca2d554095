"""Rename currency_code to currency in hotel table

Revision ID: 012
Revises: 011
Create Date: 2025-08-12 15:11:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '012'
down_revision = '011'
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column('hotel', 'currency_code', new_column_name='currency', existing_type=sa.String())


def downgrade():
    op.alter_column('hotel', 'currency', new_column_name='currency_code', existing_type=sa.String())