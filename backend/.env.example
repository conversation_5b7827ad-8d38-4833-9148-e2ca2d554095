# Database Connection
# For production: Use Azure PostgreSQL or other cloud database
# For development: Use local PostgreSQL or the docker-compose postgres service
DATABASE_URL=********************************************************/your-database-name

# Salesforce Configuration
# Get these from your Salesforce Connected App
SALESFORCE_CLIENT_ID=your_salesforce_client_id
SALESFORCE_CLIENT_SECRET=your_salesforce_client_secret
SALESFORCE_USERNAME=your_salesforce_username
SALESFORCE_PASSWORD=your_salesforce_password
SALESFORCE_SECURITY_TOKEN=your_salesforce_security_token

# OPS Configuration
OPS_URL=https://your-ops-server.com
OPS_API_KEY=your_ops_api_key

# Storage Configuration
# Set to "minio" for local development or "azure_blob" for production
STORAGE_TYPE=azure_blob

# MinIO Configuration (Local Development Only)
# MINIO_ENDPOINT=localhost:9002
# MINIO_ACCESS_KEY=minioadmin
# MINIO_SECRET_KEY=minioadmin123
# MINIO_BUCKET_NAME=etl-pipeline-data
# MINIO_SECURE=false

# Azure Blob Storage Configuration (Production)
AZURE_STORAGE_ACCOUNT_NAME=your-azure-storage-account-name
AZURE_STORAGE_ACCOUNT_KEY=your-azure-storage-account-key
AZURE_STORAGE_CONTAINER_NAME=etl-pipeline-data

# Application Settings
SECRET_KEY=your-super-secret-key-for-jwt-tokens-change-this-in-production
ENVIRONMENT=production

# API Configuration
CORS_ORIGINS=https://your-frontend-domain.com

# Database Configuration (for docker-compose local development)
# POSTGRES_DB=transfer_hub
# POSTGRES_USER=postgres
# POSTGRES_PASSWORD=your-postgres-password
