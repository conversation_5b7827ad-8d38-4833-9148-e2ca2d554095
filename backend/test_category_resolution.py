#!/usr/bin/env python3
"""
Test script to verify the category resolution transaction handling fix.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.etl.pipelines.pipeline_strategies.room_type_pipeline import RoomTypePipelineStrategy
from app.models.etl_pipeline import ETLPipelineRun
from datetime import datetime
import logging
from sqlalchemy import text

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_category_resolution():
    """Test category resolution with various scenarios to ensure transaction handling works."""
    
    db = SessionLocal()
    try:
        # Create a test pipeline run
        test_run = ETLPipelineRun(
            pipeline_id=3,  # Room Type Pipeline
            status='running',
            started_at=datetime.utcnow(),
            logs=[]
        )
        db.add(test_run)
        db.commit()
        
        # Create the room type pipeline strategy
        strategy = RoomTypePipelineStrategy(db, logger)
        
        print("Testing category resolution with transaction handling...")
        
        # Test case 1: Valid hotel_id
        print("\n1. Testing with valid hotel_id...")
        test_record_1 = {
            'hotel_id': 'a0NQP0000006cdh2AA',  # From the error log
            'hotel_name': 'Test Hotel',
            'title': 'Test Room Type'
        }
        
        try:
            category_id = strategy._resolve_category_id(test_record_1)
            print(f"   Result: {category_id}")
            
            # Test that we can still perform database operations after category resolution
            print("   Testing database operations after category resolution...")
            result = db.execute(text("SELECT COUNT(*) FROM product_category WHERE deleted_at IS NULL")).fetchone()
            print(f"   Database query successful: {result[0]} categories found")
            
        except Exception as e:
            print(f"   Error: {e}")
        
        # Test case 2: Invalid data that might cause transaction abort
        print("\n2. Testing with invalid category_id...")
        test_record_2 = {
            'category_id': 'invalid-category-id-12345',
            'hotel_name': 'Test Hotel 2',
            'title': 'Test Room Type 2'
        }
        
        try:
            category_id = strategy._resolve_category_id(test_record_2)
            print(f"   Result: {category_id}")
            
            # Test that we can still perform database operations after failed category resolution
            print("   Testing database operations after failed category resolution...")
            result = db.execute(text("SELECT COUNT(*) FROM hotel WHERE deleted_at IS NULL")).fetchone()
            print(f"   Database query successful: {result[0]} hotels found")
            
        except Exception as e:
            print(f"   Error: {e}")
        
        # Test case 3: No matching data (should use fallback)
        print("\n3. Testing with no matching data (fallback scenario)...")
        test_record_3 = {
            'hotel_name': 'Non-existent Hotel XYZ',
            'title': 'Test Room Type 3'
        }
        
        try:
            category_id = strategy._resolve_category_id(test_record_3)
            print(f"   Result: {category_id}")
            
            # Test that we can still perform database operations
            print("   Testing database operations after fallback...")
            result = db.execute(text("SELECT COUNT(*) FROM destination WHERE deleted_at IS NULL")).fetchone()
            print(f"   Database query successful: {result[0]} destinations found")
            
        except Exception as e:
            print(f"   Error: {e}")
        
        print("\n✅ Category resolution transaction handling test completed successfully!")
        print("   All database operations remained functional after category resolution attempts.")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        db.close()

if __name__ == "__main__":
    test_category_resolution()
