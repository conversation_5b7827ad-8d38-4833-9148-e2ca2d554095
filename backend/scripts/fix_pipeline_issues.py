#!/usr/bin/env python3
"""
Fix pipeline execution issues and restart stuck pipelines
"""
import sys
import os
import time
import requests
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipelineRun


def cancel_stuck_pipelines():
    """Cancel all stuck pipeline runs"""
    print("🛑 CANCELLING STUCK PIPELINES")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        # Find all running pipelines
        running_pipelines = db.query(ETLPipelineRun).filter(
            ETLPipelineRun.status == 'running'
        ).all()
        
        if not running_pipelines:
            print("✅ No running pipelines found")
            return True
        
        print(f"Found {len(running_pipelines)} stuck pipeline(s)")
        
        for run in running_pipelines:
            try:
                # Calculate runtime
                if run.started_at:
                    runtime = datetime.utcnow() - run.started_at
                    print(f"   Cancelling Run {run.id}: {runtime} runtime")
                
                # Update status to cancelled
                run.status = 'cancelled'
                run.completed_at = datetime.utcnow()
                run.error_message = "Cancelled due to hanging execution"
                
            except Exception as e:
                print(f"   Error cancelling run {run.id}: {e}")
        
        # Commit all changes
        db.commit()
        print(f"✅ Cancelled {len(running_pipelines)} stuck pipeline(s)")
        return True
        
    except Exception as e:
        print(f"❌ Error cancelling pipelines: {e}")
        db.rollback()
        return False
    finally:
        db.close()


def restart_server():
    """Restart the application server"""
    print("\n🔄 RESTARTING APPLICATION SERVER")
    print("=" * 50)
    
    try:
        # Kill existing server processes
        import subprocess
        
        print("🛑 Stopping existing server...")
        try:
            result = subprocess.run(['lsof', '-ti:8000'], capture_output=True, text=True)
            if result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                for pid in pids:
                    if pid:
                        subprocess.run(['kill', '-9', pid])
                        print(f"   Killed process {pid}")
            else:
                print("   No server processes found")
        except Exception as e:
            print(f"   Error stopping server: {e}")
        
        # Wait a moment
        time.sleep(2)
        
        # Start new server
        print("🚀 Starting new server...")
        server_process = subprocess.Popen([
            'uvicorn', 'app.main:app', '--reload', '--host', '0.0.0.0', '--port', '8000'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait for server to start
        print("⏳ Waiting for server to start...")
        time.sleep(5)
        
        # Test server health
        try:
            response = requests.get('http://localhost:8000/health', timeout=10)
            if response.status_code == 200:
                print("✅ Server started successfully")
                return True
            else:
                print(f"⚠️ Server responded with status {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Server health check failed: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Error restarting server: {e}")
        return False


def fix_database_connections():
    """Fix database connection issues"""
    print("\n🔧 FIXING DATABASE CONNECTION ISSUES")
    print("=" * 50)
    
    try:
        from sqlalchemy import text
        
        db = SessionLocal()
        try:
            # Test basic connection with proper text() wrapper
            result = db.execute(text("SELECT 1")).fetchone()
            print("✅ Database connection working")
            
            # Check for locks and kill them
            locks_result = db.execute(text("""
                SELECT pid, query 
                FROM pg_stat_activity 
                WHERE state = 'active' 
                AND query NOT LIKE '%pg_stat_activity%'
                AND pid != pg_backend_pid()
            """)).fetchall()
            
            if locks_result:
                print(f"🔒 Found {len(locks_result)} active queries")
                for pid, query in locks_result:
                    if 'etl_pipeline' in query.lower():
                        print(f"   Terminating ETL query PID {pid}")
                        try:
                            db.execute(text(f"SELECT pg_terminate_backend({pid})"))
                        except Exception as e:
                            print(f"   Error terminating {pid}: {e}")
            else:
                print("✅ No problematic active queries found")
            
            db.commit()
            return True
            
        except Exception as e:
            print(f"❌ Database connection error: {e}")
            return False
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Error fixing database connections: {e}")
        return False


def verify_pipeline_system():
    """Verify pipeline system is working"""
    print("\n✅ VERIFYING PIPELINE SYSTEM")
    print("=" * 50)
    
    try:
        # Check server health
        response = requests.get('http://localhost:8000/health', timeout=10)
        if response.status_code != 200:
            print(f"❌ Server health check failed: {response.status_code}")
            return False
        
        print("✅ Server is healthy")
        
        # Check pipeline endpoints
        response = requests.get('http://localhost:8000/api/v1/pipelines', timeout=10)
        if response.status_code != 200:
            print(f"❌ Pipeline API failed: {response.status_code}")
            return False
        
        pipelines = response.json()
        print(f"✅ Found {len(pipelines)} pipelines available")
        
        # Check database state
        db = SessionLocal()
        try:
            from sqlalchemy import text
            result = db.execute(text("SELECT COUNT(*) FROM etl_pipeline_runs WHERE status = 'running'")).fetchone()
            running_count = result[0]
            
            if running_count > 0:
                print(f"⚠️ Still {running_count} running pipeline(s) - may need manual intervention")
            else:
                print("✅ No running pipelines - system ready")
            
            return running_count == 0
            
        finally:
            db.close()
        
    except Exception as e:
        print(f"❌ Error verifying system: {e}")
        return False


def test_simple_pipeline():
    """Test a simple pipeline execution"""
    print("\n🧪 TESTING SIMPLE PIPELINE EXECUTION")
    print("=" * 50)
    
    try:
        # Try executing Resort Pipeline (simplest one)
        print("🚀 Testing Resort Pipeline execution...")
        response = requests.post('http://localhost:8000/api/v1/pipelines/1/execute', timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Pipeline execution started: {result}")
            
            # Monitor for a short time
            print("⏳ Monitoring execution for 30 seconds...")
            time.sleep(30)
            
            # Check status
            status_response = requests.get('http://localhost:8000/api/v1/pipelines/1/runs', timeout=10)
            if status_response.status_code == 200:
                runs = status_response.json()
                if runs:
                    latest_run = runs[0]
                    print(f"📊 Latest run status: {latest_run.get('status')}")
                    print(f"📊 Records: Extract={latest_run.get('records_extracted')}, Load={latest_run.get('records_loaded')}")
                    
                    if latest_run.get('status') == 'completed':
                        print("✅ Test pipeline completed successfully!")
                        return True
                    elif latest_run.get('status') == 'running':
                        print("⏳ Test pipeline still running - this is normal")
                        return True
                    else:
                        print(f"⚠️ Test pipeline status: {latest_run.get('status')}")
                        return False
            
        else:
            print(f"❌ Failed to start test pipeline: {response.status_code} - {response.text}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing pipeline: {e}")
        return False


def main():
    """Main fix function"""
    print("🔧 PIPELINE SYSTEM REPAIR")
    print("=" * 50)
    print(f"Timestamp: {datetime.utcnow()}")
    print()
    
    # Step 1: Cancel stuck pipelines
    cancel_success = cancel_stuck_pipelines()
    
    # Step 2: Fix database connections
    db_fix_success = fix_database_connections()
    
    # Step 3: Restart server
    server_success = restart_server()
    
    # Step 4: Verify system
    verify_success = verify_pipeline_system()
    
    # Step 5: Test simple pipeline
    test_success = test_simple_pipeline()
    
    print("\n" + "=" * 50)
    print("📊 REPAIR RESULTS")
    print("=" * 50)
    
    fixes = [
        ("Cancel Stuck Pipelines", cancel_success),
        ("Fix Database Connections", db_fix_success),
        ("Restart Server", server_success),
        ("Verify System", verify_success),
        ("Test Pipeline", test_success)
    ]
    
    passed_fixes = sum(1 for _, success in fixes if success)
    total_fixes = len(fixes)
    
    for fix_name, success in fixes:
        status = "✅" if success else "❌"
        print(f"{status} {fix_name}")
    
    print(f"\nRepair Success Rate: {passed_fixes}/{total_fixes} ({(passed_fixes/total_fixes)*100:.1f}%)")
    
    if passed_fixes >= 4:
        print("\n🎉 PIPELINE SYSTEM REPAIRED!")
        print("\n✅ System Status:")
        print("   - Stuck pipelines cancelled")
        print("   - Server restarted and healthy")
        print("   - Database connections fixed")
        print("   - Pipeline execution tested")
        print("\n🚀 Ready to execute pipelines normally!")
        
        print("\n💡 Recommended Next Steps:")
        print("   1. Execute Resort Pipeline first")
        print("   2. Execute Hotel Pipeline second")
        print("   3. Execute Room Type Pipeline third")
        print("   4. Execute Room Pipeline last")
        print("   5. Monitor execution progress carefully")
        
    else:
        print("\n💥 REPAIR INCOMPLETE")
        print("   Some issues remain - manual intervention may be required")
        print("   Check server logs and database connectivity")
    
    return passed_fixes >= 4


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
