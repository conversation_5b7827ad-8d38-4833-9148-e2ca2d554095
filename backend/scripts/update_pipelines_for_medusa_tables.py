#!/usr/bin/env python3
"""
Update Room Type and Room Pipeline configurations to use Medusa tables
"""
import sys
import os
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import Session<PERSON>ocal
from app.models.etl_pipeline import ETLPipeline


def update_room_type_pipeline_for_medusa():
    """Update Room Type Pipeline to use Medusa product table"""
    print("🔧 UPDATING ROOM TYPE PIPELINE FOR MEDUSA PRODUCT TABLE")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Get Room Type Pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Room Type Pipeline").first()
        if not pipeline:
            print("❌ Room Type Pipeline not found")
            return False
        
        print(f"✅ Found pipeline: {pipeline.name}")
        
        # Update to use Medusa product table
        pipeline.destination_table = "product"
        
        # Map Salesforce Room_Type__c fields to Medusa product table columns
        pipeline.destination_fields = {
            # Core Medusa product fields
            "title": "Name",                    # Room_Type__c.Name → product.title
            "description": "Description__c",    # Room_Type__c.Description__c → product.description
            "external_id": "Id",               # Room_Type__c.Id → product.external_id
            "status": "_default_published",    # Set to 'published' by default
            "handle": "_auto_generate",        # Auto-generate from title
            "is_giftcard": "_default_false",   # Set to false by default
            "discountable": "_default_true",   # Set to true by default
            
            # Store additional Salesforce data in metadata
            "metadata": {
                "salesforce_id": "Id",
                "currency_code": "CurrencyIsoCode",
                "hotel_id": "Hotel__c",
                "source_created_at": "CreatedDate",
                "source_updated_at": "LastModifiedDate",
                "source_system": "_default_salesforce",
                "is_active": "IsDeleted"  # Will be inverted (IsDeleted=false means active)
            }
        }
        
        # Ensure source fields include all needed fields
        pipeline.source_fields = [
            "Id",
            "Name",
            "Description__c",
            "CurrencyIsoCode",
            "Hotel__c",
            "CreatedDate",
            "LastModifiedDate",
            "IsDeleted"
        ]
        
        db.commit()
        print("✅ Room Type Pipeline updated for Medusa product table")
        return True
        
    except Exception as e:
        print(f"❌ Error updating Room Type Pipeline: {e}")
        db.rollback()
        return False
    finally:
        db.close()


def update_room_pipeline_for_medusa():
    """Update Room Pipeline to use Medusa product_variant table"""
    print("\n🔧 UPDATING ROOM PIPELINE FOR MEDUSA PRODUCT_VARIANT TABLE")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Get Room Pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Room Pipeline").first()
        if not pipeline:
            print("❌ Room Pipeline not found")
            return False
        
        print(f"✅ Found pipeline: {pipeline.name}")
        
        # Update to use Medusa product_variant table
        pipeline.destination_table = "product_variant"
        
        # Map Salesforce Room__c fields to Medusa product_variant table columns
        pipeline.destination_fields = {
            # Core Medusa product_variant fields
            "title": "Name",                      # Room__c.Name → product_variant.title
            "product_id": "Room_Type__c",         # Room_Type__c relationship → product_variant.product_id (will be resolved)
            "allow_backorder": "_default_false",  # Set to false by default
            "manage_inventory": "_default_false", # Set to false by default
            
            # Store additional Salesforce data in metadata
            "metadata": {
                "salesforce_id": "Id",
                "room_type_id": "Room_Type__c",
                "room_type_name": "Room_Type__r.Name",
                "is_connected": "Is_Connected__c",
                "source_created_at": "CreatedDate",
                "source_updated_at": "LastModifiedDate",
                "source_system": "_default_salesforce",
                "is_active": "IsDeleted"  # Will be inverted (IsDeleted=false means active)
            }
        }
        
        # Ensure source fields include all needed fields
        pipeline.source_fields = [
            "Id",
            "Name",
            "Room_Type__c",
            "Room_Type__r.Name",
            "Is_Connected__c",
            "CreatedDate",
            "LastModifiedDate",
            "IsDeleted"
        ]
        
        db.commit()
        print("✅ Room Pipeline updated for Medusa product_variant table")
        return True
        
    except Exception as e:
        print(f"❌ Error updating Room Pipeline: {e}")
        db.rollback()
        return False
    finally:
        db.close()


def verify_medusa_configurations():
    """Verify the updated configurations"""
    print("\n🔍 VERIFYING MEDUSA TABLE CONFIGURATIONS")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Check Room Type Pipeline
        room_type_pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Room Type Pipeline").first()
        if room_type_pipeline:
            print(f"Room Type Pipeline:")
            print(f"  Destination table: {room_type_pipeline.destination_table}")
            print(f"  Source fields: {room_type_pipeline.source_fields}")
            print(f"  Field mappings:")
            for field, source in room_type_pipeline.destination_fields.items():
                if field == "metadata":
                    print(f"    {field}: (metadata object with {len(source)} fields)")
                else:
                    print(f"    {field} ← {source}")
        
        # Check Room Pipeline
        room_pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Room Pipeline").first()
        if room_pipeline:
            print(f"\nRoom Pipeline:")
            print(f"  Destination table: {room_pipeline.destination_table}")
            print(f"  Source fields: {room_pipeline.source_fields}")
            print(f"  Field mappings:")
            for field, source in room_pipeline.destination_fields.items():
                if field == "metadata":
                    print(f"    {field}: (metadata object with {len(source)} fields)")
                else:
                    print(f"    {field} ← {source}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error verifying configurations: {e}")
        return False
    finally:
        db.close()


if __name__ == "__main__":
    print("🚀 UPDATING PIPELINES FOR MEDUSA TABLES")
    print("=" * 70)
    
    room_type_ok = update_room_type_pipeline_for_medusa()
    room_ok = update_room_pipeline_for_medusa()
    
    if room_type_ok and room_ok:
        verify_medusa_configurations()
        print(f"\n✅ MEDUSA TABLE CONFIGURATION COMPLETE")
        print("   Both pipelines updated to use Medusa tables:")
        print("   - Room Type Pipeline → product table")
        print("   - Room Pipeline → product_variant table")
        print("   - Salesforce data mapped to Medusa schema")
        print("   - Additional data stored in metadata fields")
    else:
        print(f"\n❌ MEDUSA TABLE CONFIGURATION FAILED")
        print("   Some pipelines could not be updated")
