#!/usr/bin/env python3
"""
Debug script to check current database state before running Hotel Pipeline
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.config.settings import settings

def check_database_state():
    """Check current database state"""
    print("🔍 Checking Database State")
    print("=" * 60)
    
    # Create database connection
    engine = create_engine(settings.database_url)
    
    with engine.connect() as conn:
        try:
            # Check hotel count
            hotel_count = conn.execute(text('SELECT COUNT(*) FROM hotel')).scalar()
            print(f"📊 Current hotel count: {hotel_count}")
            
            # Check existing hotels
            existing_hotels = conn.execute(text('SELECT name FROM hotel ORDER BY name LIMIT 10')).fetchall()
            print(f"🏨 Sample existing hotels: {[h[0] for h in existing_hotels]}")
            
            # Check for specific hotels mentioned with full details
            maiensee = conn.execute(text("""
                SELECT h.name, h.is_active, h.website, d.name as destination_name, pc.name as category_name
                FROM hotel h
                LEFT JOIN destination d ON h.destination_id = d.id
                LEFT JOIN product_category pc ON h.category_id = pc.id
                WHERE h.name ILIKE '%maiensee%'
            """)).fetchall()

            royal_chambers = conn.execute(text("""
                SELECT h.name, h.is_active, h.website, d.name as destination_name, pc.name as category_name
                FROM hotel h
                LEFT JOIN destination d ON h.destination_id = d.id
                LEFT JOIN product_category pc ON h.category_id = pc.id
                WHERE h.name ILIKE '%royal chambers%'
            """)).fetchall()

            print("🔍 Hotel Maiensee details:")
            for row in maiensee:
                print(f"   Name: {row[0]}")
                print(f"   Active: {row[1]}")
                print(f"   Website: {row[2]}")
                print(f"   Destination: {row[3]}")
                print(f"   Category: {row[4]}")

            print("🔍 Royal Chambers details:")
            for row in royal_chambers:
                print(f"   Name: {row[0]}")
                print(f"   Active: {row[1]}")
                print(f"   Website: {row[2]}")
                print(f"   Destination: {row[3]}")
                print(f"   Category: {row[4]}")
            
            # Check product category count
            category_count = conn.execute(text('SELECT COUNT(*) FROM product_category')).scalar()
            print(f"📂 Current product category count: {category_count}")
            
            # Check destination count
            destination_count = conn.execute(text('SELECT COUNT(*) FROM destination')).scalar()
            print(f"🌍 Current destination count: {destination_count}")
            
            # Check database connection
            print("✅ Database connection successful")
            
        except Exception as e:
            print(f"❌ Database error: {e}")
            return False
    
    return True

if __name__ == "__main__":
    check_database_state()
