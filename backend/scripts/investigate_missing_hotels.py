#!/usr/bin/env python3
"""
Investigation script to identify missing hotel records between Salesforce and PostgreSQL
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.config.settings import settings
from app.etl.extractors.salesforce_extractor import SalesforceExtractor
from app.database import get_db
import json

def extract_salesforce_hotels():
    """Extract all Hotel__c records from Salesforce using the pipeline's last run data"""
    print("🔍 Getting Salesforce Hotel__c data from last pipeline run...")

    # Instead of directly querying Salesforce, let's use the pipeline's transformation logic
    # to get the expected 241 records that should be in the database
    try:
        from app.etl.pipelines.pipeline_strategies.salesforce_to_postgresql_strategy import SalesforceToPostgreSQLStrategy
        from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun
        from app.database import get_db

        # Get the Hotel Pipeline configuration
        db = next(get_db())
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Hotel Pipeline").first()

        if not pipeline:
            print("❌ Hotel Pipeline not found")
            return []

        # Create strategy instance and extract data
        strategy = SalesforceToPostgreSQLStrategy(db)

        # Create a temporary run for extraction
        temp_run = ETLPipelineRun(pipeline_id=pipeline.id, status="running")

        # Extract the data using the same method as the pipeline
        salesforce_data = strategy._extract_data(pipeline, temp_run)

        print(f"✅ Extracted {len(salesforce_data)} hotels from Salesforce (via pipeline)")
        return salesforce_data

    except Exception as e:
        print(f"❌ Error extracting from Salesforce: {e}")
        print("🔄 Falling back to manual count verification...")
        return []

def get_database_hotels():
    """Get all hotel records from PostgreSQL database"""
    print("🔍 Extracting hotel records from PostgreSQL database...")
    
    engine = create_engine(settings.database_url)
    
    with engine.connect() as conn:
        try:
            # Get all hotels with key fields (using correct column names)
            result = conn.execute(text("""
                SELECT h.id, h.name, h.destination_id, h.website, h.email, h.phone_number,
                       h.description, h.is_active, h.is_featured, h.is_pets_allowed,
                       d.name as destination_name
                FROM hotel h
                LEFT JOIN destination d ON h.destination_id = d.id
                ORDER BY h.name
            """)).fetchall()
            
            hotels = []
            for row in result:
                hotels.append({
                    'id': row[0],
                    'name': row[1],
                    'destination_id': row[2],
                    'website': row[3],
                    'email': row[4],
                    'phone': row[5],
                    'description': row[6],
                    'is_active': row[7],
                    'is_featured': row[8],
                    'is_pets_allowed': row[9],
                    'destination_name': row[10]
                })
            
            print(f"✅ Found {len(hotels)} hotels in PostgreSQL database")
            return hotels
            
        except Exception as e:
            print(f"❌ Error querying database: {e}")
            return []

def compare_hotels(salesforce_hotels, database_hotels):
    """Compare Salesforce and database hotels to find missing records"""
    print("\n🔍 Comparing Salesforce and Database records...")
    
    # Create sets of hotel names for comparison
    sf_names = {hotel['Name'].strip().lower() for hotel in salesforce_hotels}
    db_names = {hotel['name'].strip().lower() for hotel in database_hotels}
    
    # Find missing hotels
    missing_in_db = sf_names - db_names
    extra_in_db = db_names - sf_names
    
    print(f"\n📊 Comparison Results:")
    print(f"   Salesforce hotels: {len(salesforce_hotels)}")
    print(f"   Database hotels: {len(database_hotels)}")
    print(f"   Missing in database: {len(missing_in_db)}")
    print(f"   Extra in database: {len(extra_in_db)}")
    
    if missing_in_db:
        print(f"\n❌ Hotels missing from database ({len(missing_in_db)}):")
        for i, name in enumerate(sorted(missing_in_db), 1):
            # Find the full Salesforce record
            sf_record = next((h for h in salesforce_hotels if h['Name'].strip().lower() == name), None)
            if sf_record:
                print(f"   {i}. {sf_record['Name']} (ID: {sf_record['Id']})")
                print(f"      Resort: {sf_record.get('Resort__c', 'N/A')}")
                print(f"      Active: {sf_record.get('Is_Active__c', 'N/A')}")
                print(f"      Website: {sf_record.get('Website__c', 'N/A')}")
    
    if extra_in_db:
        print(f"\n➕ Extra hotels in database ({len(extra_in_db)}):")
        for i, name in enumerate(sorted(extra_in_db), 1):
            # Find the full database record
            db_record = next((h for h in database_hotels if h['name'].strip().lower() == name), None)
            if db_record:
                print(f"   {i}. {db_record['name']} (ID: {db_record['id']})")
                print(f"      Destination: {db_record.get('destination_name', 'N/A')}")
    
    return missing_in_db, extra_in_db

def save_detailed_comparison(salesforce_hotels, database_hotels, missing_in_db):
    """Save detailed comparison data to files for further analysis"""
    print("\n💾 Saving detailed comparison data...")
    
    # Save Salesforce data
    with open('salesforce_hotels.json', 'w') as f:
        json.dump(salesforce_hotels, f, indent=2, default=str)
    
    # Save database data
    with open('database_hotels.json', 'w') as f:
        json.dump(database_hotels, f, indent=2, default=str)
    
    # Save missing records details
    missing_records = []
    for name in missing_in_db:
        sf_record = next((h for h in salesforce_hotels if h['Name'].strip().lower() == name), None)
        if sf_record:
            missing_records.append(sf_record)
    
    with open('missing_hotels.json', 'w') as f:
        json.dump(missing_records, f, indent=2, default=str)
    
    print("✅ Saved comparison files:")
    print("   - salesforce_hotels.json")
    print("   - database_hotels.json") 
    print("   - missing_hotels.json")

def main():
    """Main investigation function"""
    print("🔍 Hotel Records Investigation")
    print("=" * 60)
    
    # Extract data from both sources
    salesforce_hotels = extract_salesforce_hotels()
    database_hotels = get_database_hotels()
    
    if not salesforce_hotels or not database_hotels:
        print("❌ Failed to extract data from one or both sources")
        return
    
    # Compare the data
    missing_in_db, extra_in_db = compare_hotels(salesforce_hotels, database_hotels)
    
    # Save detailed comparison
    save_detailed_comparison(salesforce_hotels, database_hotels, missing_in_db)
    
    print(f"\n🎯 Investigation Summary:")
    print(f"   Expected records: {len(salesforce_hotels)}")
    print(f"   Actual records: {len(database_hotels)}")
    print(f"   Missing records: {len(missing_in_db)}")
    print(f"   Data integrity: {((len(database_hotels) - len(extra_in_db)) / len(salesforce_hotels)) * 100:.1f}%")

if __name__ == "__main__":
    main()
