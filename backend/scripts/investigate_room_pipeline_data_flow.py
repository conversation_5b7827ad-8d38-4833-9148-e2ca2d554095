#!/usr/bin/env python3
"""
Comprehensive investigation of Room Pipeline data flow to identify why 
product_variant table remains empty despite pipeline executions
"""
import sys
import os
import time
import requests
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun, ProductVariants, Product
from app.services.minio_service import minio_service


def investigate_room_pipeline_configuration():
    """Investigate Room Pipeline configuration and recent runs"""
    print("🔍 ROOM PIPELINE CONFIGURATION INVESTIGATION")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Get Room Pipeline
        room_pipeline = db.query(ETLPipeline).filter(
            ETLPipeline.name == "Room Pipeline"
        ).first()
        
        if not room_pipeline:
            print("❌ Room Pipeline not found")
            return False
        
        print(f"✅ Room Pipeline found (ID: {room_pipeline.id})")
        print(f"   Source: {room_pipeline.source_object}")
        print(f"   Destination: {room_pipeline.destination_table}")
        print(f"   Active: {room_pipeline.is_active}")
        
        # Get recent runs
        recent_runs = db.query(ETLPipelineRun).filter(
            ETLPipelineRun.pipeline_id == room_pipeline.id
        ).order_by(ETLPipelineRun.started_at.desc()).limit(5).all()
        
        print(f"\n📊 Recent Room Pipeline Runs:")
        if not recent_runs:
            print("   No runs found")
            return False
        
        for run in recent_runs:
            duration = "Unknown"
            if run.started_at and run.completed_at:
                duration = str(run.completed_at - run.started_at)
            elif run.started_at:
                duration = f"{datetime.utcnow() - run.started_at} (running/stuck)"
            
            print(f"\n   Run {run.id}:")
            print(f"      Status: {run.status}")
            print(f"      Duration: {duration}")
            print(f"      Extract: {run.records_extracted}")
            print(f"      Transform: {run.records_transformed}")
            print(f"      Load: {run.records_loaded}")
            print(f"      Raw Data: {run.raw_data_path}")
            print(f"      Processed Data: {run.processed_data_path}")
            
            if run.error_message:
                print(f"      Error: {run.error_message}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error investigating configuration: {e}")
        return False
    finally:
        db.close()


def check_minio_data_storage():
    """Check if Room Pipeline data is being stored in MinIO"""
    print("\n💾 MINIO DATA STORAGE INVESTIGATION")
    print("=" * 60)
    
    try:
        # Check MinIO connectivity
        print("🔗 Testing MinIO connectivity...")
        
        # List buckets
        buckets = minio_service.list_buckets()
        print(f"✅ MinIO connected - Found {len(buckets)} buckets")
        
        # Check for Room Pipeline data
        room_data_found = False
        
        for bucket in buckets:
            bucket_name = bucket.name
            print(f"\n📂 Checking bucket: {bucket_name}")
            
            try:
                # List objects in bucket
                objects = list(minio_service.list_objects(bucket_name))
                
                room_objects = [obj for obj in objects if 'room' in obj.object_name.lower()]
                
                if room_objects:
                    room_data_found = True
                    print(f"   Found {len(room_objects)} Room-related objects:")
                    
                    for obj in room_objects[:5]:  # Show first 5
                        print(f"      - {obj.object_name} ({obj.size} bytes, {obj.last_modified})")
                    
                    if len(room_objects) > 5:
                        print(f"      ... and {len(room_objects) - 5} more objects")
                else:
                    print(f"   No Room-related objects found")
                    
            except Exception as e:
                print(f"   Error listing objects: {e}")
        
        if not room_data_found:
            print("\n❌ No Room Pipeline data found in MinIO")
            print("   This suggests the pipeline is not reaching the storage phase")
        else:
            print("\n✅ Room Pipeline data found in MinIO")
        
        return room_data_found
        
    except Exception as e:
        print(f"❌ Error checking MinIO: {e}")
        return False


def trace_pipeline_execution_flow():
    """Trace the exact execution flow of Room Pipeline"""
    print("\n🔍 PIPELINE EXECUTION FLOW TRACING")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Get the most recent Room Pipeline run
        latest_run = db.query(ETLPipelineRun).filter(
            ETLPipelineRun.pipeline_id == 4  # Room Pipeline ID
        ).order_by(ETLPipelineRun.started_at.desc()).first()
        
        if not latest_run:
            print("❌ No Room Pipeline runs found")
            return False
        
        print(f"📊 Analyzing Run {latest_run.id}:")
        print(f"   Status: {latest_run.status}")
        print(f"   Started: {latest_run.started_at}")
        print(f"   Completed: {latest_run.completed_at}")
        
        # Analyze each phase
        phases = {
            "Extraction": latest_run.records_extracted,
            "Transformation": latest_run.records_transformed,
            "Loading": latest_run.records_loaded
        }
        
        print(f"\n📋 Phase Analysis:")
        for phase, count in phases.items():
            if count is None:
                print(f"   ❌ {phase}: Not reached (None)")
            elif count == 0:
                print(f"   ⚠️ {phase}: Reached but no records ({count})")
            else:
                print(f"   ✅ {phase}: {count} records")
        
        # Check data paths
        print(f"\n📁 Data Storage Paths:")
        if latest_run.raw_data_path:
            print(f"   Raw Data: {latest_run.raw_data_path}")
        else:
            print(f"   Raw Data: Not stored")
            
        if latest_run.processed_data_path:
            print(f"   Processed Data: {latest_run.processed_data_path}")
        else:
            print(f"   Processed Data: Not stored")
        
        # Identify failure point
        print(f"\n🎯 Failure Point Analysis:")
        if latest_run.records_extracted is None:
            print("   🚨 FAILURE: Pipeline stuck in extraction phase")
            print("   Likely cause: Salesforce API timeout/authentication")
        elif latest_run.records_extracted == 0:
            print("   🚨 FAILURE: No data extracted from Salesforce")
            print("   Likely cause: No Room__c records or query issues")
        elif latest_run.records_transformed is None:
            print("   🚨 FAILURE: Pipeline stuck in transformation phase")
            print("   Likely cause: Data processing issues")
        elif latest_run.records_loaded is None:
            print("   🚨 FAILURE: Pipeline stuck in loading phase")
            print("   Likely cause: Database connection or MinIO retrieval issues")
        elif latest_run.records_loaded == 0:
            print("   🚨 FAILURE: No data loaded to database")
            print("   Likely cause: Loading logic not called or database transaction issues")
        else:
            print("   ✅ All phases completed successfully")
        
        return latest_run.status == 'completed' and latest_run.records_loaded and latest_run.records_loaded > 0
        
    except Exception as e:
        print(f"❌ Error tracing execution: {e}")
        return False
    finally:
        db.close()


def verify_loading_method_integration():
    """Verify that _load_to_product_variant method is properly integrated"""
    print("\n🔧 LOADING METHOD INTEGRATION VERIFICATION")
    print("=" * 60)
    
    try:
        # Check the pipeline strategy code
        from app.etl.pipelines.pipeline_strategies.salesforce_to_postgresql_strategy import SalesforceToPostgreSQLStrategy
        
        # Verify the method exists
        strategy = SalesforceToPostgreSQLStrategy(None)
        
        if hasattr(strategy, '_load_to_product_variant'):
            print("✅ _load_to_product_variant method exists")
        else:
            print("❌ _load_to_product_variant method not found")
            return False
        
        # Check if the method is called in the main loading logic
        print("\n🔍 Checking integration in main loading logic...")
        
        # Read the strategy file to check integration
        strategy_file_path = "app/services/pipeline_strategies/salesforce_to_postgresql_strategy.py"
        
        with open(strategy_file_path, 'r') as f:
            content = f.read()
        
        # Check for product_variant destination handling
        if 'destination_table == "product_variant"' in content:
            print("✅ product_variant destination handling found")
        else:
            print("❌ product_variant destination handling not found")
            return False
        
        # Check for _load_to_product_variant call
        if '_load_to_product_variant' in content:
            print("✅ _load_to_product_variant method call found")
        else:
            print("❌ _load_to_product_variant method call not found")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error verifying integration: {e}")
        return False


def execute_test_room_pipeline():
    """Execute a test Room Pipeline run with detailed monitoring"""
    print("\n🧪 TEST ROOM PIPELINE EXECUTION")
    print("=" * 60)
    
    try:
        # Cancel any stuck runs first
        db = SessionLocal()
        try:
            stuck_runs = db.query(ETLPipelineRun).filter(
                ETLPipelineRun.pipeline_id == 4,
                ETLPipelineRun.status == 'running'
            ).all()
            
            if stuck_runs:
                for run in stuck_runs:
                    run.status = 'cancelled'
                    run.completed_at = datetime.utcnow()
                    run.error_message = "Cancelled for test execution"
                db.commit()
                print(f"🛑 Cancelled {len(stuck_runs)} stuck run(s)")
        finally:
            db.close()
        
        # Get initial state
        db = SessionLocal()
        try:
            initial_variants = db.query(ProductVariants).count()
            print(f"📊 Initial product variants: {initial_variants}")
        finally:
            db.close()
        
        # Execute Room Pipeline
        print("📡 Starting Room Pipeline test execution...")
        start_time = time.time()
        
        response = requests.post("http://localhost:8000/api/v1/pipelines/4/execute", timeout=10)
        
        if response.status_code != 200:
            print(f"❌ Failed to start: {response.status_code}")
            return False
        
        result = response.json()
        run_id = result.get('run_id')
        print(f"✅ Started: Run ID {run_id}")
        
        # Monitor with detailed phase tracking
        print("⏳ Monitoring execution phases...")
        
        max_wait = 90  # 1.5 minutes
        check_interval = 5
        
        while time.time() - start_time < max_wait:
            try:
                elapsed = time.time() - start_time
                
                # Get detailed status
                status_response = requests.get("http://localhost:8000/api/v1/pipelines/4/runs", timeout=5)
                
                if status_response.status_code == 200:
                    runs = status_response.json()
                    if runs:
                        latest_run = runs[0]
                        status = latest_run.get('status')
                        extracted = latest_run.get('records_extracted')
                        transformed = latest_run.get('records_transformed')
                        loaded = latest_run.get('records_loaded')
                        
                        print(f"   [{elapsed:3.0f}s] {status} | E:{extracted} T:{transformed} L:{loaded}")
                        
                        # Check database changes
                        db = SessionLocal()
                        try:
                            current_variants = db.query(ProductVariants).count()
                            if current_variants != initial_variants:
                                print(f"   [{elapsed:3.0f}s] 🎉 Database: {current_variants} variants (+{current_variants - initial_variants})")
                        finally:
                            db.close()
                        
                        if status == 'completed':
                            print(f"✅ COMPLETED in {elapsed:.1f} seconds!")
                            return True
                        elif status == 'failed':
                            error = latest_run.get('error_message', 'Unknown')
                            print(f"❌ FAILED: {error}")
                            return False
                        elif elapsed > 60 and extracted is None:
                            print(f"⏰ TIMEOUT: Stuck in extraction for 60+ seconds")
                            return False
                
                time.sleep(check_interval)
                
            except Exception as e:
                print(f"⚠️ Monitoring error: {e}")
                time.sleep(check_interval)
        
        print(f"⏰ TIMEOUT after {max_wait} seconds")
        return False
        
    except Exception as e:
        print(f"❌ Error executing test: {e}")
        return False


def main():
    """Main investigation function"""
    print("🔍 COMPREHENSIVE ROOM PIPELINE DATA FLOW INVESTIGATION")
    print("=" * 70)
    print(f"Timestamp: {datetime.utcnow()}")
    print()
    
    # Step 1: Investigate configuration
    config_ok = investigate_room_pipeline_configuration()
    
    # Step 2: Check MinIO storage
    minio_ok = check_minio_data_storage()
    
    # Step 3: Trace execution flow
    execution_ok = trace_pipeline_execution_flow()
    
    # Step 4: Verify method integration
    integration_ok = verify_loading_method_integration()
    
    # Step 5: Execute test pipeline
    test_ok = execute_test_room_pipeline()
    
    print("\n" + "=" * 70)
    print("📊 COMPREHENSIVE INVESTIGATION RESULTS")
    print("=" * 70)
    
    checks = [
        ("Configuration", config_ok),
        ("MinIO Storage", minio_ok),
        ("Execution Flow", execution_ok),
        ("Method Integration", integration_ok),
        ("Test Execution", test_ok)
    ]
    
    passed = sum(1 for _, success in checks if success)
    total = len(checks)
    
    for check_name, success in checks:
        status = "✅" if success else "❌"
        print(f"{status} {check_name}")
    
    success_rate = (passed / total) * 100
    print(f"\nInvestigation Success Rate: {passed}/{total} ({success_rate:.1f}%)")
    
    # Provide specific diagnosis
    print(f"\n🎯 SPECIFIC DIAGNOSIS:")
    
    if not config_ok:
        print("   🔧 Room Pipeline configuration issues")
    
    if not minio_ok:
        print("   🔧 Data not reaching MinIO storage - extraction phase failure")
    
    if not execution_ok:
        print("   🔧 Pipeline execution not completing successfully")
    
    if not integration_ok:
        print("   🔧 Loading method not properly integrated")
    
    if not test_ok:
        print("   🔧 Test execution failed - likely Salesforce connectivity")
    
    # Final recommendation
    print(f"\n💡 RECOMMENDED ACTION:")
    if not minio_ok and not execution_ok:
        print("   🎯 PRIMARY ISSUE: Pipeline not reaching data storage phase")
        print("   🔧 FIX: Resolve Salesforce API connectivity/authentication")
    elif minio_ok and not execution_ok:
        print("   🎯 PRIMARY ISSUE: Data stored but not loaded to database")
        print("   🔧 FIX: Check MinIO retrieval and database loading logic")
    elif integration_ok and not test_ok:
        print("   🎯 PRIMARY ISSUE: Integration correct but execution failing")
        print("   🔧 FIX: Resolve runtime execution issues")
    
    return passed >= 3


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
