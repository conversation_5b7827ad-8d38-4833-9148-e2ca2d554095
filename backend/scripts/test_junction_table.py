#!/usr/bin/env python3
"""
Test script to verify product_category_product junction table functionality
"""
import sys
import os
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun, Product, ProductCategory, ProductCategoryProduct, Hotel
from app.etl.pipelines.pipeline_strategies.salesforce_to_postgresql_strategy import SalesforceToPostgreSQLStrategy


def test_junction_table_creation():
    """Test that the junction table was created correctly"""
    print("🔍 Testing Junction Table Creation...")
    
    db = SessionLocal()
    try:
        # Test that we can query the junction table
        junction_count = db.query(ProductCategoryproduct).count()
        print(f"✅ Junction table accessible - Current entries: {junction_count}")
        
        # Test table structure by attempting to create a test entry
        test_entry = ProductCategoryproduct(
            product_category_id="test_category",
            product_id="test_product"
        )
        print("✅ Junction table model structure is correct")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing junction table: {e}")
        return False
    finally:
        db.close()


def test_junction_table_with_mock_data():
    """Test junction table population with mock data"""
    print("\n🧪 Testing Junction Table with Mock Data...")
    
    db = SessionLocal()
    try:
        # Get the Room Type Pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Room Type Pipeline").first()
        if not pipeline:
            print("❌ Room Type Pipeline not found")
            return False
        
        # Get a sample hotel and category for testing
        sample_hotel = db.query(Hotel).first()
        if not sample_hotel or not sample_hotel.category_id:
            print("❌ No hotel with categories found for testing")
            return False
        
        print(f"✅ Using test hotel: {sample_hotel.name} (Category: {sample_hotel.category_id})")
        
        # Create a test pipeline run
        run = ETLPipelineRun(
            pipeline_id=pipeline.id,
            status="running",
            logs=[]
        )
        db.add(run)
        db.commit()
        db.refresh(run)
        
        # Create mock data with category
        mock_data = [
            {
                "migrated_id": "test_junction_001",
                "title": "Test Room with Category",
                "description": "Test room for junction table verification",
                "hotel_id": sample_hotel.id,
                "currency": "USD",
                "source_created_at": datetime.utcnow(),
                "source_updated_at": datetime.utcnow(),
                "extracted_at": datetime.utcnow(),
                "source_system": "salesforce",
                "external_id": "test_junction_001",
                "is_active": True
            }
        ]
        
        # Get initial junction table count
        initial_junction_count = db.query(ProductCategoryProduct).count()
        initial_product_count = db.query(Product).count()

        print(f"📊 Initial counts - product: {initial_product_count}, Junction entries: {initial_junction_count}")

        # Test the product loading logic with junction table population
        strategy = SalesforceToPostgreSQLStrategy(db)
        loaded_count = strategy._load_to_product(run, mock_data)

        # Check final counts
        final_junction_count = db.query(ProductCategoryProduct).count()
        final_product_count = db.query(Product).count()
        
        print(f"📊 Final counts - product: {final_product_count}, Junction entries: {final_junction_count}")
        print(f"✅ Loaded {loaded_count} product")
        
        # Verify junction table entry was created
        new_junction_entries = final_junction_count - initial_junction_count
        new_product = final_product_count - initial_product_count
        
        if new_junction_entries > 0 and new_product > 0:
            print(f"✅ Junction table populated correctly - {new_junction_entries} new entries for {new_product} new product")
            
            # Get the created product and verify its junction table entry
            test_product = db.query(Product).filter(
                Product.pipeline_run_id == run.id,
                Product.migrated_id == "test_junction_001"
            ).first()
            
            if test_product:
                junction_entry = db.query(ProductCategoryproduct).filter(
                    ProductCategoryproduct.product_id == test_product.id
                ).first()
                
                if junction_entry:
                    print(f"✅ Junction entry verified: Product {test_product.id} → Category {junction_entry.product_category_id}")
                    return True
                else:
                    print("❌ No junction entry found for test product")
                    return False
            else:
                print("❌ Test product not found")
                return False
        else:
            print("❌ Junction table was not populated")
            return False
        
        # Update run status
        run.status = "completed"
        run.completed_at = datetime.utcnow()
        run.records_loaded = loaded_count
        db.commit()
        
    except Exception as e:
        print(f"❌ Error testing junction table with mock data: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()


def test_existing_product_junction_entries():
    """Test junction table entries for existing product"""
    print("\n📊 Analyzing Existing product and Junction Table...")
    
    db = SessionLocal()
    try:
        # Count existing product
        total_product = db.query(Product).count()
        product_with_categories = db.query(Product).filter(Product.category_id.isnot(None)).count()
        
        # Count junction table entries
        total_junction_entries = db.query(ProductCategoryproduct).count()
        
        print(f"📊 Current Database State:")
        print(f"   Total product: {total_product}")
        print(f"   product with categories: {product_with_categories}")
        print(f"   Junction table entries: {total_junction_entries}")
        
        if total_product > 0:
            # Show sample product and their junction table status
            sample_product = db.query(Product).filter(Product.category_id.isnot(None)).limit(5).all()
            
            print(f"\n📋 Sample product with Categories:")
            for product in sample_product:
                junction_entry = db.query(ProductCategoryproduct).filter(
                    ProductCategoryproduct.product_id == product.id
                ).first()
                
                junction_status = "✅ Has junction entry" if junction_entry else "❌ Missing junction entry"
                print(f"   - {product.title} (ID: {product.id})")
                print(f"     Category: {product.category_id}")
                print(f"     Junction: {junction_status}")
                print()
        
        return True
        
    except Exception as e:
        print(f"❌ Error analyzing existing product: {e}")
        return False
    finally:
        db.close()


def main():
    """Main test function"""
    print("🚀 Testing Product Category product Junction Table")
    print("=" * 60)
    
    # Test junction table creation
    creation_success = test_junction_table_creation()
    
    # Test with mock data
    mock_data_success = test_junction_table_with_mock_data()
    
    # Analyze existing product
    analysis_success = test_existing_product_junction_entries()
    
    print("\n" + "=" * 60)
    print("📊 Junction Table Test Results:")
    print(f"   Table Creation: {'✅ PASS' if creation_success else '❌ FAIL'}")
    print(f"   Mock Data Test: {'✅ PASS' if mock_data_success else '❌ FAIL'}")
    print(f"   Existing Data Analysis: {'✅ PASS' if analysis_success else '❌ FAIL'}")
    
    if all([creation_success, mock_data_success, analysis_success]):
        print("\n🎉 All junction table tests passed!")
        print("\n💡 Next Steps:")
        print("1. Run Room Type Pipeline to test with real Salesforce data")
        print("2. Verify that junction table is populated automatically")
        print("3. Check that existing product get junction table entries")
        return True
    else:
        print("\n💥 Some junction table tests failed.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
