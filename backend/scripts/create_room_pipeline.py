#!/usr/bin/env python3
"""
Create Room Pipeline configuration for extracting Room__c data from Salesforce
"""
import sys
import os
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import Session<PERSON>ocal
from app.models.etl_pipeline import ETLPipeline


def create_room_pipeline():
    """Create Room Pipeline configuration in the database"""
    print("🚀 Creating Room Pipeline Configuration")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        # Check if Room Pipeline already exists
        existing_pipeline = db.query(ETLPipeline).filter(
            ETLPipeline.name == "Room Pipeline"
        ).first()
        
        if existing_pipeline:
            print(f"⚠️ Room Pipeline already exists (ID: {existing_pipeline.id})")
            print("Updating existing configuration...")
            pipeline = existing_pipeline
        else:
            print("✅ Creating new Room Pipeline configuration...")
            pipeline = ETLPipeline()
        
        # Configure Room Pipeline for Room__c extraction
        pipeline.name = "Room Pipeline"
        pipeline.description = "Extract Room__c data from Salesforce and load into product_variant table"
        
        # Source configuration - Salesforce Room__c object
        pipeline.source_type = "salesforce"
        pipeline.source_object = "Room__c"
        pipeline.source_fields = [
            "Id",
            "Name", 
            "Room_Type__c",
            "Room_Type__r.Name",
            "Is_Connected__c",
            "CreatedDate",
            "LastModifiedDate"
        ]
        
        # Destination configuration - product_variant table
        pipeline.destination_type = "postgresql"
        pipeline.destination_table = "product_variant"
        pipeline.destination_fields = {
            "migrated_id": "Id",
            "name": "Name",
            "product_id": "Room_Type__c",  # Will be resolved to product.id
            "room_config_name": "Room_Type__r.Name",
            "is_connected": "Is_Connected__c",
            "source_created_at": "CreatedDate",
            "source_updated_at": "LastModifiedDate",
            "source_system": "salesforce",
            "external_id": "Id",
            "is_active": "IsDeleted"  # Use actual Salesforce field, will be inverted
        }
        
        # Transformation configuration
        pipeline.transformation_config = {
            "room_type_lookup": {
                "enabled": True,
                "source_field": "Room_Type__c",
                "target_table": "product",
                "target_field": "migrated_id",
                "result_field": "id"
            },
            "data_validation": {
                "required_fields": ["Id", "Name"],
                "optional_fields": ["Room_Type__c", "Is_Connected__c"]
            }
        }
        
        # Schedule configuration (manual execution for now)
        pipeline.schedule_config = {
            "enabled": False,
            "cron_expression": None,
            "timezone": "UTC"
        }
        
        # Pipeline settings
        pipeline.is_active = True
        
        if not existing_pipeline:
            db.add(pipeline)
        
        db.commit()
        db.refresh(pipeline)
        
        print(f"✅ Room Pipeline created successfully!")
        print(f"   Pipeline ID: {pipeline.id}")
        print(f"   Name: {pipeline.name}")
        print(f"   Source: {pipeline.source_type} - {pipeline.source_object}")
        print(f"   Destination: {pipeline.destination_type} - {pipeline.destination_table}")
        print(f"   Fields: {len(pipeline.source_fields)} source fields configured")
        print(f"   Active: {pipeline.is_active}")
        
        return pipeline.id
        
    except Exception as e:
        print(f"❌ Error creating Room Pipeline: {e}")
        db.rollback()
        import traceback
        traceback.print_exc()
        return None
    finally:
        db.close()


def verify_room_pipeline():
    """Verify Room Pipeline configuration"""
    print("\n🔍 Verifying Room Pipeline Configuration")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        pipeline = db.query(ETLPipeline).filter(
            ETLPipeline.name == "Room Pipeline"
        ).first()
        
        if not pipeline:
            print("❌ Room Pipeline not found")
            return False
        
        print(f"✅ Pipeline Found: {pipeline.name} (ID: {pipeline.id})")
        print(f"📊 Configuration Details:")
        print(f"   Source Object: {pipeline.source_object}")
        print(f"   Source Fields: {pipeline.source_fields}")
        print(f"   Destination Table: {pipeline.destination_table}")
        print(f"   Field Mappings: {len(pipeline.destination_fields)} mappings")
        print(f"   Transformation Config: {bool(pipeline.transformation_config)}")
        print(f"   Active: {pipeline.is_active}")
        
        # Verify all required components
        checks = [
            ("Source Type", pipeline.source_type == "salesforce"),
            ("Source Object", pipeline.source_object == "Room__c"),
            ("Destination Type", pipeline.destination_type == "postgresql"),
            ("Destination Table", pipeline.destination_table == "product_variant"),
            ("Source Fields", len(pipeline.source_fields) >= 5),
            ("Field Mappings", len(pipeline.destination_fields) >= 8),
            ("Active Status", pipeline.is_active == True)
        ]
        
        print(f"\n📋 Configuration Checks:")
        all_passed = True
        for check_name, passed in checks:
            status = "✅" if passed else "❌"
            print(f"   {status} {check_name}")
            if not passed:
                all_passed = False
        
        if all_passed:
            print(f"\n🎉 Room Pipeline configuration is complete and ready!")
        else:
            print(f"\n⚠️ Some configuration checks failed")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error verifying Room Pipeline: {e}")
        return False
    finally:
        db.close()


def main():
    """Main function"""
    print("🏗️ ROOM PIPELINE SETUP")
    print("=" * 50)
    
    # Create Room Pipeline
    pipeline_id = create_room_pipeline()
    
    if pipeline_id:
        # Verify configuration
        verification_success = verify_room_pipeline()
        
        print(f"\n" + "=" * 50)
        print("📊 ROOM PIPELINE SETUP RESULTS:")
        print(f"   Creation: {'✅ SUCCESS' if pipeline_id else '❌ FAILED'}")
        print(f"   Verification: {'✅ PASSED' if verification_success else '❌ FAILED'}")
        
        if pipeline_id and verification_success:
            print(f"\n🎉 Room Pipeline is ready for execution!")
            print(f"   Pipeline ID: {pipeline_id}")
            print(f"   Execute via: POST /api/v1/pipelines/{pipeline_id}/execute")
            print(f"\n💡 Next Steps:")
            print(f"   1. Ensure Room Type Pipeline has run (to populate product table)")
            print(f"   2. Execute Room Pipeline to extract Room__c data")
            print(f"   3. Verify product_variant table population")
        else:
            print(f"\n💥 Room Pipeline setup incomplete - please check errors above")
        
        return pipeline_id and verification_success
    else:
        print(f"\n💥 Failed to create Room Pipeline")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
