#!/usr/bin/env python3
"""
Comprehensive diagnostic script for Room Pipeline data persistence and performance issues
"""
import sys
import os
import time
import requests
from datetime import datetime, timed<PERSON>ta

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun, Product, ProductVariant
from sqlalchemy import text


def check_room_pipeline_configuration():
    """Check Room Pipeline configuration and prerequisites"""
    print("🔍 ROOM PIPELINE CONFIGURATION ANALYSIS")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Get Room Pipeline
        room_pipeline = db.query(ETLPipeline).filter(
            ETLPipeline.name == "Room Pipeline"
        ).first()
        
        if not room_pipeline:
            print("❌ Room Pipeline not found")
            return False, None
        
        print(f"✅ Room Pipeline found (ID: {room_pipeline.id})")
        print(f"   Source: {room_pipeline.source_object}")
        print(f"   Destination: {room_pipeline.destination_table}")
        print(f"   Active: {room_pipeline.is_active}")
        
        # Check source fields configuration
        if room_pipeline.source_fields:
            print(f"   Source fields: {len(room_pipeline.source_fields)} configured")
            for field in room_pipeline.source_fields:
                print(f"     - {field}")
        else:
            print("   ⚠️ No source fields configured")
        
        # Check destination fields mapping
        if room_pipeline.destination_fields:
            print(f"   Field mappings: {len(room_pipeline.destination_fields)} configured")
        else:
            print("   ⚠️ No field mappings configured")
        
        # Check prerequisites - product table
        product_count = db.query(Product).count()
        print(f"   Prerequisites: {product_count} product available")
        
        if product_count == 0:
            print("   ⚠️ No product found - Room Type Pipeline should run first")
            return False, room_pipeline.id
        
        return True, room_pipeline.id
        
    except Exception as e:
        print(f"❌ Error checking configuration: {e}")
        return False, None
    finally:
        db.close()


def analyze_recent_room_pipeline_runs():
    """Analyze recent Room Pipeline execution attempts"""
    print("\n📊 RECENT ROOM PIPELINE RUNS ANALYSIS")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Get Room Pipeline ID
        room_pipeline = db.query(ETLPipeline).filter(
            ETLPipeline.name == "Room Pipeline"
        ).first()
        
        if not room_pipeline:
            print("❌ Room Pipeline not found")
            return []
        
        # Get recent runs (last 24 hours)
        cutoff_time = datetime.utcnow() - timedelta(hours=24)
        recent_runs = db.query(ETLPipelineRun).filter(
            ETLPipelineRun.pipeline_id == room_pipeline.id,
            ETLPipelineRun.started_at >= cutoff_time
        ).order_by(ETLPipelineRun.started_at.desc()).limit(10).all()
        
        if not recent_runs:
            print("ℹ️ No recent Room Pipeline runs found")
            return []
        
        print(f"Found {len(recent_runs)} recent runs:")
        
        running_runs = []
        
        for run in recent_runs:
            duration = "Unknown"
            if run.started_at:
                if run.completed_at:
                    duration = str(run.completed_at - run.started_at)
                else:
                    duration = f"{datetime.utcnow() - run.started_at} (RUNNING)"
            
            status_icon = {
                'running': '🔄',
                'completed': '✅',
                'failed': '❌',
                'cancelled': '⏹️'
            }.get(run.status, '❓')
            
            print(f"\n   {status_icon} Run {run.id}:")
            print(f"      Status: {run.status}")
            print(f"      Started: {run.started_at}")
            print(f"      Duration: {duration}")
            print(f"      Records: Extract={run.records_extracted}, Transform={run.records_transformed}, Load={run.records_loaded}")
            
            if run.error_message:
                print(f"      Error: {run.error_message}")
            
            if run.raw_data_path:
                print(f"      Raw data: {run.raw_data_path}")
            
            if run.processed_data_path:
                print(f"      Processed data: {run.processed_data_path}")
            
            # Analyze specific issues
            if run.status == 'running':
                runtime = datetime.utcnow() - run.started_at if run.started_at else timedelta(0)
                if runtime > timedelta(minutes=5):
                    print(f"      🚨 ISSUE: Running for {runtime} - likely stuck")
                running_runs.append(run)
            
            elif run.status == 'completed':
                if run.records_extracted and not run.records_loaded:
                    print(f"      🚨 ISSUE: Data extracted but not loaded")
                elif run.records_loaded == 0:
                    print(f"      🚨 ISSUE: No records loaded to database")
            
            elif run.status == 'failed':
                print(f"      🚨 ISSUE: Pipeline failed - check error message")
        
        return running_runs
        
    except Exception as e:
        print(f"❌ Error analyzing runs: {e}")
        return []
    finally:
        db.close()


def check_product_variant_table_state():
    """Check current state of product_variant table"""
    print("\n🗄️ product_variant TABLE ANALYSIS")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Get current count
        total_variants = db.query(ProductVariants).count()
        print(f"📊 Total product variants: {total_variants}")
        
        if total_variants == 0:
            print("❌ No product variants found - data persistence issue confirmed")
            return False
        
        # Check recent variants (last hour)
        recent_cutoff = datetime.utcnow() - timedelta(hours=1)
        recent_variants = db.query(ProductVariants).filter(
            ProductVariants.created_at >= recent_cutoff
        ).count()
        
        print(f"📊 Recent variants (last hour): {recent_variants}")
        
        # Check variants with product relationships
        variants_with_product = db.query(ProductVariants).filter(
            ProductVariants.product_id.isnot(None)
        ).count()
        
        variants_without_product = total_variants - variants_with_product
        
        print(f"🔗 Variants with product links: {variants_with_product}")
        print(f"🔗 Variants without product links: {variants_without_product}")
        
        if total_variants > 0:
            link_percentage = (variants_with_product / total_variants) * 100
            print(f"🔗 Link success rate: {link_percentage:.1f}%")
        
        # Show sample variants
        if total_variants > 0:
            print(f"\n📋 Sample Product Variants:")
            sample_variants = db.query(ProductVariants).limit(3).all()
            
            for variant in sample_variants:
                product_name = "No Product"
                if variant.product:
                    product_name = variant.product.title
                
                print(f"   - {variant.name} (ID: {variant.id})")
                print(f"     Product: {product_name}")
                print(f"     Connected: {variant.is_connected}")
                print(f"     Created: {variant.created_at}")
        
        return total_variants > 0
        
    except Exception as e:
        print(f"❌ Error checking product_variant table: {e}")
        return False
    finally:
        db.close()


def test_room_pipeline_loading_logic():
    """Test the Room Pipeline loading logic directly"""
    print("\n🧪 ROOM PIPELINE LOADING LOGIC TEST")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Clear existing test data
        test_variants = db.query(ProductVariants).filter(
            ProductVariants.name.like('TEST_%')
        ).delete()
        db.commit()
        
        # Create test Room__c data
        test_room_data = [
            {
                "Id": "a0c000000TEST001",
                "Name": "TEST_Room_101",
                "Room_Type__c": None,  # No product link
                "Room_Type__r": {"Name": "Test Room Type"},
                "Is_Connected__c": True,
                "CreatedDate": "2023-01-01T10:00:00.000+0000",
                "LastModifiedDate": "2023-01-01T10:00:00.000+0000"
            },
            {
                "Id": "a0c000000TEST002",
                "Name": "TEST_Room_102",
                "Room_Type__c": "invalid_product_id",  # Invalid product link
                "Room_Type__r": {"Name": "Test Room Type 2"},
                "Is_Connected__c": False,
                "CreatedDate": "2023-01-01T10:00:00.000+0000",
                "LastModifiedDate": "2023-01-01T10:00:00.000+0000"
            }
        ]
        
        # Get a real product for valid test
        real_product = db.query(Product).first()
        if real_product:
            test_room_data.append({
                "Id": "a0c000000TEST003",
                "Name": "TEST_Room_103",
                "Room_Type__c": real_product.migrated_id,  # Valid product link
                "Room_Type__r": {"Name": real_product.title},
                "Is_Connected__c": True,
                "CreatedDate": "2023-01-01T10:00:00.000+0000",
                "LastModifiedDate": "2023-01-01T10:00:00.000+0000"
            })
        
        print(f"🧪 Testing with {len(test_room_data)} test Room__c records")
        
        # Create a test pipeline run
        room_pipeline = db.query(ETLPipeline).filter(
            ETLPipeline.name == "Room Pipeline"
        ).first()
        
        test_run = ETLPipelineRun(
            pipeline_id=room_pipeline.id,
            status="running",
            logs=[]
        )
        db.add(test_run)
        db.commit()
        db.refresh(test_run)
        
        # Test the loading logic
        from app.etl.pipelines.pipeline_strategies.salesforce_to_postgresql_strategy import SalesforceToPostgreSQLStrategy
        
        strategy = SalesforceToPostgreSQLStrategy(db)
        loaded_count = strategy._load_to_product_variant(test_run, test_room_data)
        
        print(f"✅ Loading method returned: {loaded_count} records")
        
        # Verify test data was loaded
        test_variants_loaded = db.query(ProductVariant).filter(
            ProductVariant.name.like('TEST_%')
        ).count()
        
        print(f"📊 Test variants in database: {test_variants_loaded}")
        
        if test_variants_loaded > 0:
            print("✅ Loading logic is working - data persistence confirmed")
            
            # Show test variants
            test_variants = db.query(ProductVariants).filter(
                ProductVariants.name.like('TEST_%')
            ).all()
            
            for variant in test_variants:
                product_status = "✅ Linked" if variant.product_id else "❌ No link"
                print(f"   - {variant.name}: {product_status}")
            
            return True
        else:
            print("❌ Loading logic failed - no test data persisted")
            return False
        
    except Exception as e:
        print(f"❌ Error testing loading logic: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Clean up test data
        try:
            db.query(ProductVariants).filter(
                ProductVariants.name.like('TEST_%')
            ).delete()
            db.commit()
        except:
            pass
        db.close()


def execute_room_pipeline_with_monitoring():
    """Execute Room Pipeline with detailed monitoring"""
    print("\n🚀 ROOM PIPELINE EXECUTION WITH MONITORING")
    print("=" * 60)
    
    try:
        # Clear existing product variants for clean test
        db = SessionLocal()
        try:
            initial_count = db.query(ProductVariants).count()
            print(f"📊 Initial product variants count: {initial_count}")
        finally:
            db.close()
        
        # Execute Room Pipeline
        print("📡 Starting Room Pipeline execution...")
        start_time = time.time()
        
        response = requests.post("http://localhost:8000/api/v1/pipelines/4/execute", timeout=10)
        
        if response.status_code != 200:
            print(f"❌ Failed to start Room Pipeline: {response.status_code} - {response.text}")
            return False
        
        result = response.json()
        run_id = result.get('run_id')
        print(f"✅ Room Pipeline started: Run ID {run_id}")
        
        # Monitor execution with detailed logging
        print("⏳ Monitoring execution (max 3 minutes)...")
        
        max_wait_time = 180  # 3 minutes
        check_interval = 10  # 10 seconds
        
        while time.time() - start_time < max_wait_time:
            try:
                # Get pipeline status
                status_response = requests.get("http://localhost:8000/api/v1/pipelines/4/runs", timeout=5)
                
                if status_response.status_code == 200:
                    runs = status_response.json()
                    if runs:
                        latest_run = runs[0]
                        status = latest_run.get('status')
                        extracted = latest_run.get('records_extracted', 0)
                        transformed = latest_run.get('records_transformed', 0)
                        loaded = latest_run.get('records_loaded', 0)
                        
                        elapsed = time.time() - start_time
                        print(f"   [{elapsed:.0f}s] Status: {status} | Extract: {extracted} | Transform: {transformed} | Load: {loaded}")
                        
                        # Check database state
                        db = SessionLocal()
                        try:
                            current_variants = db.query(ProductVariants).count()
                            print(f"   [{elapsed:.0f}s] Database variants: {current_variants}")
                        finally:
                            db.close()
                        
                        if status == 'completed':
                            print(f"✅ Room Pipeline completed in {elapsed:.1f} seconds!")
                            return True
                        elif status == 'failed':
                            error_msg = latest_run.get('error_message', 'Unknown error')
                            print(f"❌ Room Pipeline failed: {error_msg}")
                            return False
                
                time.sleep(check_interval)
                
            except Exception as e:
                print(f"⚠️ Error monitoring: {e}")
                time.sleep(check_interval)
        
        print(f"⏰ Room Pipeline timeout after {max_wait_time} seconds")
        return False
        
    except Exception as e:
        print(f"❌ Error executing Room Pipeline: {e}")
        return False


def main():
    """Main diagnostic function"""
    print("🔍 ROOM PIPELINE DIAGNOSTIC INVESTIGATION")
    print("=" * 60)
    print(f"Timestamp: {datetime.utcnow()}")
    print()
    
    # Step 1: Check configuration
    config_ok, pipeline_id = check_room_pipeline_configuration()
    
    # Step 2: Analyze recent runs
    running_runs = analyze_recent_room_pipeline_runs()
    
    # Step 3: Check database state
    db_has_data = check_product_variant_table_state()
    
    # Step 4: Test loading logic
    loading_logic_ok = test_room_pipeline_loading_logic()
    
    # Step 5: Execute with monitoring (if not already running)
    execution_ok = False
    if not running_runs:  # Only if no pipelines currently running
        execution_ok = execute_room_pipeline_with_monitoring()
    else:
        print(f"\n⚠️ Skipping execution test - {len(running_runs)} pipeline(s) already running")
    
    print("\n" + "=" * 60)
    print("📊 ROOM PIPELINE DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    checks = [
        ("Configuration", config_ok),
        ("Database State", db_has_data),
        ("Loading Logic", loading_logic_ok),
        ("Execution Test", execution_ok or len(running_runs) > 0)
    ]
    
    passed_checks = sum(1 for _, passed in checks if passed)
    total_checks = len(checks)
    
    for check_name, passed in checks:
        status = "✅" if passed else "❌"
        print(f"{status} {check_name}")
    
    print(f"\nDiagnostic Success Rate: {passed_checks}/{total_checks} ({(passed_checks/total_checks)*100:.1f}%)")
    
    # Provide specific recommendations
    print(f"\n💡 SPECIFIC ISSUES IDENTIFIED:")
    
    if not config_ok:
        print("   🔧 Configuration issues - check Room Pipeline setup")
    
    if not db_has_data and loading_logic_ok:
        print("   🔧 Data persistence issue - pipeline not completing load phase")
    
    if not loading_logic_ok:
        print("   🔧 Loading logic issue - _load_to_product_variant method problems")
    
    if running_runs:
        print(f"   🔧 Performance issue - {len(running_runs)} pipeline(s) running too long")
    
    if not execution_ok and not running_runs:
        print("   🔧 Execution issue - pipeline failing to start or complete")
    
    print(f"\n🎯 RECOMMENDED ACTIONS:")
    print("   1. Check Salesforce API connectivity and Room__c object access")
    print("   2. Verify database transaction handling in loading logic")
    print("   3. Monitor pipeline execution logs for specific error messages")
    print("   4. Ensure MinIO storage is accessible for data persistence")
    print("   5. Check for memory/resource constraints during execution")
    
    return passed_checks >= 3


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
