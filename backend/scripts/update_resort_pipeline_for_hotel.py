#!/usr/bin/env python3
"""
Update Resort Pipeline for Hotel Pipeline compatibility.
This script updates the Resort Pipeline configuration to include the migrated_id field
mapping, which is required for the Hotel Pipeline foreign key relationships.
"""
import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import <PERSON><PERSON>ocal
from app.models.etl_pipeline import ET<PERSON>ipeline


def update_resort_pipeline_for_hotel():
    """Update Resort Pipeline to include migrated_id field mapping"""
    print("🏖️  Updating Resort Pipeline for Hotel Pipeline Compatibility")
    print("=" * 60)
    
    db = SessionLocal()
    
    try:
        # Find the Resort Pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Resort Pipeline").first()
        if not pipeline:
            print("❌ Resort Pipeline not found in database")
            print("💡 You may need to create the Resort Pipeline first")
            return False
        
        print(f"🔍 Found Resort Pipeline with ID: {pipeline.id}")
        print(f"   Current source fields: {pipeline.source_fields}")
        print(f"   Current destination fields: {pipeline.destination_fields}")
        
        # Check if migrated_id is already mapped
        current_dest_fields = pipeline.destination_fields or {}
        if "migrated_id" in current_dest_fields:
            print("✅ Resort Pipeline already includes migrated_id mapping")
            print(f"   Current mapping: {current_dest_fields['migrated_id']} → migrated_id")
            return True
        
        # Updated destination fields to include migrated_id
        new_destination_fields = dict(current_dest_fields)
        new_destination_fields["migrated_id"] = "Id"  # Map Salesforce Id to migrated_id
        
        # Ensure Id is in source fields
        current_source_fields = pipeline.source_fields or []
        if "Id" not in current_source_fields:
            new_source_fields = ["Id"] + current_source_fields
            pipeline.source_fields = new_source_fields
            print("✅ Added 'Id' to source fields")
        
        # Update destination fields
        pipeline.destination_fields = new_destination_fields
        
        db.commit()
        db.refresh(pipeline)
        
        print("✅ Resort Pipeline updated successfully!")
        print(f"   Updated source fields ({len(pipeline.source_fields)}): {pipeline.source_fields}")
        print(f"   Updated destination fields ({len(new_destination_fields)}): {list(new_destination_fields.keys())}")
        
        print("\n📋 New Field Mapping:")
        print("   Id → migrated_id (NEW - for Hotel Pipeline compatibility)")
        
        print("\n📋 All Field Mappings:")
        for dest_field, source_field in new_destination_fields.items():
            transformation = ""
            if dest_field == "is_active":
                transformation = " (inverted from IsDeleted)"
            elif dest_field == "migrated_id":
                transformation = " (NEW - Salesforce Resort__c.Id)"
            print(f"   {source_field} → {dest_field}{transformation}")
        
        return True
        
    except Exception as e:
        db.rollback()
        print(f"❌ Error updating Resort Pipeline: {e}")
        return False
    finally:
        db.close()


def verify_resort_pipeline_update():
    """Verify the Resort Pipeline update"""
    print("\n🔍 Verifying Resort Pipeline Update")
    print("=" * 50)
    
    db = SessionLocal()
    
    try:
        # Find the Resort Pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Resort Pipeline").first()
        if not pipeline:
            print("❌ Resort Pipeline not found")
            return False
        
        # Check source fields
        source_fields = pipeline.source_fields or []
        if "Id" not in source_fields:
            print("❌ 'Id' field missing from source fields")
            return False
        else:
            print("✅ 'Id' field present in source fields")
        
        # Check destination fields
        dest_fields = pipeline.destination_fields or {}
        if "migrated_id" not in dest_fields:
            print("❌ 'migrated_id' mapping missing from destination fields")
            return False
        elif dest_fields["migrated_id"] != "Id":
            print(f"❌ 'migrated_id' mapping incorrect: {dest_fields['migrated_id']} (should be 'Id')")
            return False
        else:
            print("✅ 'migrated_id' mapping correct: Id → migrated_id")
        
        print("✅ Resort Pipeline update verified successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error verifying Resort Pipeline update: {e}")
        return False
    finally:
        db.close()


def check_destination_table_compatibility():
    """Check if destination table has migrated_id column"""
    print("\n🗄️  Checking destination Table Compatibility")
    print("=" * 50)
    
    try:
        from sqlalchemy import text
        
        db = SessionLocal()
        
        # Check if migrated_id column exists
        result = db.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'destination' 
            AND column_name = 'migrated_id'
        """))
        
        migrated_id_exists = result.fetchone() is not None
        
        if migrated_id_exists:
            print("✅ destination.migrated_id column exists")
            
            # Check if any destination already have migrated_id values
            result = db.execute(text("""
                SELECT COUNT(*) as total, COUNT(migrated_id) as with_migrated_id
                FROM destination
            """))
            
            row = result.fetchone()
            total_destination = row[0]
            destination_with_migrated_id = row[1]
            
            print(f"📊 destination table status:")
            print(f"   Total destination: {total_destination}")
            print(f"   With migrated_id: {destination_with_migrated_id}")
            print(f"   Missing migrated_id: {total_destination - destination_with_migrated_id}")
            
            if destination_with_migrated_id == 0 and total_destination > 0:
                print("⚠️  Existing destination need migrated_id values")
                print("💡 Re-run Resort Pipeline to populate migrated_id for existing records")
            elif destination_with_migrated_id == total_destination:
                print("✅ All destination have migrated_id values")
            
            return True
        else:
            print("❌ destination.migrated_id column missing")
            print("💡 Run database migration: alembic upgrade head")
            return False
        
    except Exception as e:
        print(f"❌ Error checking destination table: {e}")
        return False
    finally:
        db.close()


if __name__ == "__main__":
    print("🏖️  Resort Pipeline Update for Hotel Pipeline")
    print("=" * 60)
    print("This script updates the Resort Pipeline to be compatible with the Hotel Pipeline")
    print("by adding the migrated_id field mapping required for foreign key relationships.")
    print()
    
    success = True
    
    # Step 1: Update Resort Pipeline configuration
    if not update_resort_pipeline_for_hotel():
        success = False
    
    # Step 2: Verify the update
    if success and not verify_resort_pipeline_update():
        success = False
    
    # Step 3: Check destination table compatibility
    if success and not check_destination_table_compatibility():
        success = False
    
    # Final summary
    if success:
        print("\n🎉 Resort Pipeline update completed successfully!")
        print("=" * 60)
        print("✅ Pipeline configuration: Updated")
        print("✅ Field mappings: Updated")
        print("✅ Verification: Complete")
        print("✅ Database compatibility: Verified")
        
        print("\n📋 Updated Resort Pipeline Field Mappings:")
        print("   Id → migrated_id (NEW)")
        print("   IsDeleted → is_active (inverted)")
        print("   Name → name")
        print("   CurrencyIsoCode → currency")
        print("   CreatedDate → created_at")
        print("   LastModifiedDate → updated_at")
        print("   Country__c → country")
        print("   Description__c → description")
        
        print("\n🚀 Next Steps:")
        print("1. Re-run Resort Pipeline to populate migrated_id for existing destination")
        print("2. Set up Hotel Pipeline using setup_hotel_pipeline.py")
        print("3. Run Hotel Pipeline to migrate hotel data")
        
        print("\n💡 Important:")
        print("The migrated_id field is essential for Hotel Pipeline foreign key lookups.")
        print("Existing destination will need migrated_id values before running Hotel Pipeline.")
        
    else:
        print("\n❌ Resort Pipeline update failed!")
        print("Please check the error messages above and resolve any issues.")
        sys.exit(1)
