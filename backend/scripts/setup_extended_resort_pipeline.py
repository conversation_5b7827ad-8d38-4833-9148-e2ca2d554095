#!/usr/bin/env python3
"""
Complete setup script for the extended Resort Pipeline.
This script:
1. Runs the database migration to add new fields
2. Updates the pipeline configuration with new field mappings
3. Verifies the setup is complete
"""
import sys
import os
import subprocess

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from update_resort_pipeline_fields import update_resort_pipeline, verify_update


def run_database_migration():
    """Run the database migration to add new fields to destination table"""
    print("🔄 Running database migration...")
    
    try:
        # Run alembic upgrade
        result = subprocess.run(
            ["alembic", "upgrade", "head"],
            cwd=os.path.dirname(os.path.abspath(__file__)),
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✅ Database migration completed successfully")
            if result.stdout:
                print(f"   Output: {result.stdout.strip()}")
            return True
        else:
            print("❌ Database migration failed")
            if result.stderr:
                print(f"   Error: {result.stderr.strip()}")
            return False
            
    except FileNotFoundError:
        print("❌ Alembic not found. Make sure you're in the backend directory and alembic is installed")
        return False
    except Exception as e:
        print(f"❌ Error running migration: {e}")
        return False


def check_database_schema():
    """Check if the new fields exist in the destination table"""
    print("🔍 Checking database schema...")
    
    try:
        from app.core.database import SessionLocal
        from sqlalchemy import text
        
        db = SessionLocal()
        
        # Check if new columns exist
        result = db.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'destination' 
            AND column_name IN ('is_active', 'currency', 'description')
        """))
        
        existing_columns = [row[0] for row in result.fetchall()]
        expected_columns = ['is_active', 'currency', 'description']
        missing_columns = set(expected_columns) - set(existing_columns)
        
        if missing_columns:
            print(f"❌ Missing columns in destination table: {missing_columns}")
            return False
        else:
            print("✅ All new columns exist in destination table")
            return True
            
    except Exception as e:
        print(f"❌ Error checking database schema: {e}")
        return False
    finally:
        if 'db' in locals():
            db.close()


def main():
    """Main setup function"""
    print("🚀 Setting up Extended Resort Pipeline...")
    print("=" * 60)
    
    # Step 1: Run database migration
    migration_success = run_database_migration()
    if not migration_success:
        print("\n💥 Setup failed at migration step!")
        sys.exit(1)
    
    # Step 2: Check database schema
    schema_success = check_database_schema()
    if not schema_success:
        print("\n💥 Setup failed at schema verification step!")
        sys.exit(1)
    
    # Step 3: Update pipeline configuration
    print("\n🔧 Updating pipeline configuration...")
    pipeline_success = update_resort_pipeline()
    if not pipeline_success:
        print("\n💥 Setup failed at pipeline update step!")
        sys.exit(1)
    
    # Step 4: Verify the complete setup
    print("\n🔍 Verifying complete setup...")
    verification_success = verify_update()
    if not verification_success:
        print("\n⚠️ Setup completed but verification found issues!")
        sys.exit(1)
    
    # Success!
    print("\n🎉 Extended Resort Pipeline setup completed successfully!")
    print("=" * 60)
    print("✅ Database migration: Complete")
    print("✅ Schema verification: Complete") 
    print("✅ Pipeline configuration: Complete")
    print("✅ Setup verification: Complete")
    
    print(f"\n📋 New Field Mappings:")
    print("   IsDeleted → is_active (inverted)")
    print("   Name → name")
    print("   CurrencyIsoCode → currency")
    print("   CreatedDate → created_at")
    print("   LastModifiedDate → updated_at")
    print("   Country__c → country")
    print("   Description__c → description")
    
    print(f"\n🚀 Next Steps:")
    print("1. Test the pipeline execution with new field mappings")
    print("2. Verify data transformation is working correctly")
    print("3. Check that all fields are being populated in the destination table")
    
    print(f"\n💡 To test the pipeline:")
    print("   - Use the web interface to run the Resort Pipeline")
    print("   - Check the pipeline logs for any transformation errors")
    print("   - Verify the destination table contains all expected fields")


if __name__ == "__main__":
    main()
