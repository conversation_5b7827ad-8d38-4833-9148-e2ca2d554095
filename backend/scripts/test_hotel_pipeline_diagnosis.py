#!/usr/bin/env python3
"""
Diagnose Hotel Pipeline issues by testing each component separately.
"""
import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipeline
from app.etl.extractors.salesforce_extractor import SalesforceExtractor
from app.etl.pipelines.pipeline_strategies.salesforce_to_postgresql_strategy import SalesforceToPostgreSQLStrategy
import json


def test_salesforce_connection():
    """Test basic Salesforce connection"""
    print("🔍 Testing Salesforce Connection...")
    
    try:
        extractor = SalesforceExtractor()
        if extractor.authenticate():
            print("   ✅ Salesforce authentication successful")
            return True
        else:
            print("   ❌ Salesforce authentication failed")
            return False
    except Exception as e:
        print(f"   ❌ Salesforce connection error: {e}")
        return False


def test_hotel_data_extraction():
    """Test Hotel__c data extraction"""
    print("🔍 Testing Hotel__c Data Extraction...")
    
    try:
        extractor = SalesforceExtractor()
        if not extractor.authenticate():
            print("   ❌ Cannot authenticate with Salesforce")
            return False
        
        # Test with a small limit first
        fields = ['Id', 'Name', 'Resort__c', 'CurrencyIsoCode', 'Current__c', 'H_Website__c', 'Notes__c', 'H_Email__c', 'CreatedDate', 'LastModifiedDate', 'IsDeleted']
        
        print(f"   📥 Extracting Hotel__c records with fields: {fields}")
        data = extractor.extract_records(
            object_name="Hotel__c",
            fields=fields,
            limit=5  # Small test first
        )
        
        print(f"   ✅ Extracted {len(data)} Hotel__c records")
        
        if data:
            print("   📋 Sample record:")
            sample = data[0]
            for key, value in sample.items():
                print(f"      {key}: {value}")
        
        return len(data) > 0
        
    except Exception as e:
        print(f"   ❌ Hotel__c extraction error: {e}")
        return False


def test_hotel_pipeline_configuration():
    """Test Hotel Pipeline configuration"""
    print("🔍 Testing Hotel Pipeline Configuration...")
    
    try:
        db = SessionLocal()
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Hotel Pipeline").first()
        
        if not pipeline:
            print("   ❌ Hotel Pipeline not found in database")
            return False
        
        print(f"   ✅ Hotel Pipeline found (ID: {pipeline.id})")
        print(f"   📋 Source Object: {pipeline.source_object}")
        print(f"   📋 Source Fields: {len(pipeline.source_fields)} fields")
        print(f"   📋 Destination Table: {pipeline.destination_table}")
        print(f"   📋 Is Active: {pipeline.is_active}")
        
        # Validate configuration
        strategy = SalesforceToPostgreSQLStrategy(db)
        is_valid, errors = strategy.validate_configuration(pipeline)
        
        if is_valid:
            print("   ✅ Pipeline configuration is valid")
        else:
            print("   ❌ Pipeline configuration errors:")
            for error in errors:
                print(f"      - {error}")
        
        db.close()
        return is_valid
        
    except Exception as e:
        print(f"   ❌ Configuration test error: {e}")
        return False


def test_hotel_pipeline_dry_run():
    """Test Hotel Pipeline with dry run approach"""
    print("🔍 Testing Hotel Pipeline Dry Run...")
    
    try:
        db = SessionLocal()
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Hotel Pipeline").first()
        
        if not pipeline:
            print("   ❌ Hotel Pipeline not found")
            return False
        
        strategy = SalesforceToPostgreSQLStrategy(db)
        
        # Test extraction
        print("   📥 Testing data extraction...")
        extractor = SalesforceExtractor()
        if not extractor.authenticate():
            print("   ❌ Salesforce authentication failed")
            return False
        
        data = extractor.extract_records(
            object_name=pipeline.source_object,
            fields=pipeline.source_fields or [],
            limit=3  # Small test
        )
        
        print(f"   ✅ Extracted {len(data)} records")
        
        if not data:
            print("   ⚠️  No data extracted - this might be the issue!")
            return False
        
        # Test transformation
        print("   🔄 Testing data transformation...")
        transformed_data = []
        for record in data:
            transformed_record = strategy._transform_record(record, pipeline.destination_fields, pipeline.transformation_config or {})
            transformed_data.append(transformed_record)
        
        print(f"   ✅ Transformed {len(transformed_data)} records")
        
        if transformed_data:
            print("   📋 Sample transformed record:")
            sample = transformed_data[0]
            for key, value in sample.items():
                print(f"      {key}: {value}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Dry run error: {e}")
        return False


def main():
    """Run all diagnostic tests"""
    print("🏨 Hotel Pipeline Diagnostic Tool")
    print("=" * 60)
    
    tests = [
        ("Salesforce Connection", test_salesforce_connection),
        ("Hotel Data Extraction", test_hotel_data_extraction),
        ("Pipeline Configuration", test_hotel_pipeline_configuration),
        ("Pipeline Dry Run", test_hotel_pipeline_dry_run),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n==================== {test_name} ====================")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Diagnostic Results Summary:")
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {status} {test_name}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! Hotel Pipeline should be working.")
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
    
    return all_passed


if __name__ == "__main__":
    main()
