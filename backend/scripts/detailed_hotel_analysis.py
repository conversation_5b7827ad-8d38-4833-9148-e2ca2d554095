#!/usr/bin/env python3
"""
Detailed analysis of hotel records to understand the 241 vs 235 discrepancy
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.config.settings import settings
from collections import Counter
import json

def analyze_salesforce_duplicates():
    """Check for duplicate hotel names in Salesforce data"""
    print("🔍 Analyzing Salesforce data for duplicates...")
    
    try:
        from app.etl.pipelines.pipeline_strategies.salesforce_to_postgresql_strategy import SalesforceToPostgreSQLStrategy
        from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun
        from sqlalchemy.orm import sessionmaker
        
        engine = create_engine(settings.database_url)
        Session = sessionmaker(bind=engine)
        session = Session()
        
        try:
            # Get the Hotel Pipeline configuration
            pipeline = session.query(ETLPipeline).filter(ETLPipeline.name == "Hotel Pipeline").first()
            
            if not pipeline:
                print("❌ Hotel Pipeline not found")
                return [], []
            
            # Create strategy instance and extract data
            strategy = SalesforceToPostgreSQLStrategy(session)
            
            # Create a temporary run for extraction
            temp_run = ETLPipelineRun(pipeline_id=pipeline.id, status="running")
            
            # Extract the raw data from Salesforce
            print("📥 Extracting raw Salesforce data...")
            salesforce_data = strategy._extract_data(pipeline, temp_run)
            print(f"✅ Extracted {len(salesforce_data)} records from Salesforce")
            
            # Transform the data
            print("🔄 Transforming data...")
            transformed_data = strategy._transform_data(pipeline, temp_run, salesforce_data)
            print(f"✅ Transformed {len(transformed_data)} records")
            
            # Analyze for duplicates
            hotel_names = [record.get('name', '').strip() for record in transformed_data if record.get('name')]
            name_counts = Counter(hotel_names)
            
            duplicates = {name: count for name, count in name_counts.items() if count > 1}
            
            print(f"\n📊 Salesforce Data Analysis:")
            print(f"   Total records: {len(salesforce_data)}")
            print(f"   Transformed records: {len(transformed_data)}")
            print(f"   Unique hotel names: {len(set(hotel_names))}")
            print(f"   Duplicate names: {len(duplicates)}")
            
            if duplicates:
                print(f"\n⚠️  Duplicate hotel names in Salesforce:")
                for name, count in duplicates.items():
                    print(f"   '{name}': {count} occurrences")
                    
                    # Find the actual records with this name
                    matching_records = [r for r in transformed_data if r.get('name', '').strip() == name]
                    for i, record in enumerate(matching_records):
                        print(f"     Record {i+1}: ID={record.get('id', 'N/A')}, Destination={record.get('destination_id', 'N/A')}")
            
            return transformed_data, duplicates
            
        finally:
            session.close()
            
    except Exception as e:
        print(f"❌ Error analyzing Salesforce data: {e}")
        return [], {}

def analyze_database_state():
    """Analyze current database state"""
    print("\n🔍 Analyzing database state...")
    
    engine = create_engine(settings.database_url)
    
    with engine.connect() as conn:
        try:
            # Get all hotels with details
            result = conn.execute(text("""
                SELECT h.id, h.name, h.destination_id, d.name as destination_name,
                       h.category_id, pc.name as category_name
                FROM hotel h
                LEFT JOIN destination d ON h.destination_id = d.id
                LEFT JOIN product_category pc ON h.category_id = pc.id
                ORDER BY h.name
            """)).fetchall()
            
            hotels = []
            for row in result:
                hotels.append({
                    'id': row[0],
                    'name': row[1],
                    'destination_id': row[2],
                    'destination_name': row[3],
                    'category_id': row[4],
                    'category_name': row[5]
                })
            
            # Check for duplicates in database
            hotel_names = [h['name'] for h in hotels]
            name_counts = Counter(hotel_names)
            db_duplicates = {name: count for name, count in name_counts.items() if count > 1}
            
            print(f"📊 Database Analysis:")
            print(f"   Total hotels: {len(hotels)}")
            print(f"   Unique names: {len(set(hotel_names))}")
            print(f"   Duplicate names: {len(db_duplicates)}")
            
            if db_duplicates:
                print(f"\n⚠️  Duplicate hotel names in database:")
                for name, count in db_duplicates.items():
                    print(f"   '{name}': {count} occurrences")
            
            return hotels, db_duplicates
            
        except Exception as e:
            print(f"❌ Error analyzing database: {e}")
            return [], {}

def analyze_upsert_behavior(salesforce_data, database_hotels):
    """Analyze how the upsert logic behaves with the data"""
    print("\n🔍 Analyzing upsert behavior...")
    
    # Create lookup dictionaries
    sf_by_name = {}
    for record in salesforce_data:
        name = record.get('name', '').strip()
        if name:
            if name in sf_by_name:
                # Multiple Salesforce records with same name
                if not isinstance(sf_by_name[name], list):
                    sf_by_name[name] = [sf_by_name[name]]
                sf_by_name[name].append(record)
            else:
                sf_by_name[name] = record
    
    db_by_name = {}
    for hotel in database_hotels:
        name = hotel['name'].strip()
        if name:
            if name in db_by_name:
                # Multiple database records with same name
                if not isinstance(db_by_name[name], list):
                    db_by_name[name] = [db_by_name[name]]
                db_by_name[name].append(hotel)
            else:
                db_by_name[name] = hotel
    
    print(f"📊 Upsert Analysis:")
    print(f"   Salesforce unique names: {len(sf_by_name)}")
    print(f"   Database unique names: {len(db_by_name)}")
    
    # Find names that exist in Salesforce but not in database
    sf_names = set(sf_by_name.keys())
    db_names = set(db_by_name.keys())
    
    missing_in_db = sf_names - db_names
    extra_in_db = db_names - sf_names
    
    print(f"   Missing in database: {len(missing_in_db)}")
    print(f"   Extra in database: {len(extra_in_db)}")
    
    if missing_in_db:
        print(f"\n❌ Hotels in Salesforce but not in database:")
        for name in sorted(missing_in_db):
            sf_record = sf_by_name[name]
            if isinstance(sf_record, list):
                print(f"   '{name}': {len(sf_record)} Salesforce records")
            else:
                print(f"   '{name}': 1 Salesforce record")
    
    if extra_in_db:
        print(f"\n➕ Hotels in database but not in Salesforce:")
        for name in sorted(extra_in_db):
            db_record = db_by_name[name]
            if isinstance(db_record, list):
                print(f"   '{name}': {len(db_record)} database records")
            else:
                print(f"   '{name}': 1 database record")
    
    # Calculate the expected vs actual counts
    total_sf_records = len(salesforce_data)
    unique_sf_names = len(sf_by_name)
    total_db_records = len(database_hotels)
    
    print(f"\n🎯 Count Analysis:")
    print(f"   Total Salesforce records: {total_sf_records}")
    print(f"   Unique Salesforce names: {unique_sf_names}")
    print(f"   Total database records: {total_db_records}")
    print(f"   Expected after upsert: {unique_sf_names} (if no duplicates)")
    print(f"   Actual after upsert: {total_db_records}")
    print(f"   Discrepancy: {unique_sf_names - total_db_records}")

def main():
    """Main analysis function"""
    print("🔍 Detailed Hotel Records Analysis")
    print("=" * 60)
    
    # Analyze Salesforce data
    salesforce_data, sf_duplicates = analyze_salesforce_duplicates()
    
    # Analyze database state
    database_hotels, db_duplicates = analyze_database_state()
    
    if salesforce_data and database_hotels:
        # Analyze upsert behavior
        analyze_upsert_behavior(salesforce_data, database_hotels)
        
        print(f"\n🎯 Final Summary:")
        print(f"   Pipeline extracts: {len(salesforce_data)} records")
        print(f"   Pipeline reports loaded: 241 records")
        print(f"   Database contains: {len(database_hotels)} records")
        print(f"   Discrepancy: {241 - len(database_hotels)} records")
        
        if sf_duplicates:
            total_duplicates = sum(count - 1 for count in sf_duplicates.values())
            print(f"   Duplicate records in Salesforce: {total_duplicates}")
            print(f"   This explains the discrepancy: {len(salesforce_data)} - {total_duplicates} = {len(salesforce_data) - total_duplicates}")

if __name__ == "__main__":
    main()
