#!/usr/bin/env python3
"""
Test the data transformation and loading phases of the Resort Pipeline.
"""
import os
import sys
import traceback
from typing import Dict, Any, List
from datetime import datetime

def test_data_transformation():
    """Test the data transformation logic."""
    print("🔍 Testing data transformation logic...")
    
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app.core.database import SessionLocal
        from app.models.etl_pipeline import ET<PERSON>ipeline, ETLPipelineRun
        from app.etl.pipelines.pipeline_strategies.salesforce_to_postgresql_strategy import SalesforceToPostgreSQLStrategy
        
        # Get Resort Pipeline configuration
        db = SessionLocal()
        try:
            resort_pipeline = db.query(ETLPipeline).filter(
                ETLPipeline.name == "Resort Pipeline"
            ).first()
            
            if not resort_pipeline:
                print("   ❌ Resort Pipeline not found")
                return False
            
            print("   ✅ Resort Pipeline found")
            print(f"      Destination fields: {resort_pipeline.destination_fields}")
            print(f"      Transformation config: {resort_pipeline.transformation_config}")
            
            # Create a test pipeline run
            test_run = ETLPipelineRun(
                pipeline_id=resort_pipeline.id,
                status="running",
                started_at=datetime.utcnow()
            )
            db.add(test_run)
            db.commit()
            db.refresh(test_run)
            
            # Create strategy instance
            strategy = SalesforceToPostgreSQLStrategy(db)
            
            # Sample Salesforce data (from our diagnostics)
            sample_data = [
                {
                    'Id': 'a0P300000042oWpEAI',
                    'IsDeleted': False,
                    'Name': 'Marrakech',
                    'CurrencyIsoCode': 'GBP',
                    'CreatedDate': '2011-06-09T08:57:48.000+0000',
                    'LastModifiedDate': '2011-06-09T08:57:48.000+0000',
                    'Country__c': 'Morocco',
                    'Description__c': None
                },
                {
                    'Id': 'a0P300000042oWhEAI',
                    'IsDeleted': False,
                    'Name': 'Southampton',
                    'CurrencyIsoCode': 'GBP',
                    'CreatedDate': '2011-06-09T08:57:48.000+0000',
                    'LastModifiedDate': '2011-06-09T08:57:48.000+0000',
                    'Country__c': 'England',
                    'Description__c': None
                }
            ]
            
            print(f"   Testing transformation with {len(sample_data)} sample records...")
            
            # Test transformation
            transformed_data = strategy._transform_data(resort_pipeline, test_run, sample_data)
            
            print(f"   ✅ Transformation successful - {len(transformed_data)} records transformed")
            
            if transformed_data:
                print("   📋 Sample transformed data:")
                for i, record in enumerate(transformed_data[:2], 1):
                    print(f"      Record {i}: {record}")
            
            # Clean up test run
            db.delete(test_run)
            db.commit()
            
            return True
            
        except Exception as transform_error:
            print(f"   ❌ Transformation failed: {transform_error}")
            traceback.print_exc()
            return False
        finally:
            db.close()
            
    except Exception as e:
        print(f"   ❌ Error testing transformation: {e}")
        traceback.print_exc()
        return False

def test_destination_model_compatibility():
    """Test if transformed data is compatible with the destination model."""
    print("\n🔍 Testing destination model compatibility...")
    
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app.core.database import SessionLocal
        from app.models.etl_pipeline import Destination
        
        # Sample transformed data (what we expect from transformation)
        sample_transformed_data = {
            'name': 'Test Resort',
            'country': 'Test Country',
            'is_active': True,
            'currency': 'USD',
            'description': 'Test Description',
            'migrated_id': 'test_id_123'
        }
        
        print(f"   Testing with sample data: {sample_transformed_data}")
        
        # Try to create a destination record
        db = SessionLocal()
        try:
            # Generate required fields for powderbyrne schema
            handle = sample_transformed_data['name'].lower().replace(' ', '-')
            
            test_destination = Destination(
                id=sample_transformed_data['migrated_id'],
                name=sample_transformed_data['name'],
                handle=handle,
                description=sample_transformed_data.get('description', ''),
                is_active=sample_transformed_data.get('is_active', True),
                country=sample_transformed_data['country'],
                location=None,
                tags=[],
                website=None,
                is_featured=False,
                ai_content=None,
                metadata_={'test': True},  # ETL tracking info
                internal_web_link=None,
                external_web_link=None,
                currency=None,
                margin=None,
                currency=sample_transformed_data.get('currency'),
            )
            
            print("   ✅ Destination model creation successful")
            print(f"      ID: {test_destination.id}")
            print(f"      Name: {test_destination.name}")
            print(f"      Handle: {test_destination.handle}")
            print(f"      Country: {test_destination.country}")
            print(f"      Currency Code: {test_destination.currency}")
            
            # Don't actually save to avoid conflicts
            return True
            
        except Exception as model_error:
            print(f"   ❌ Model creation failed: {model_error}")
            traceback.print_exc()
            return False
        finally:
            db.close()
            
    except Exception as e:
        print(f"   ❌ Error testing model compatibility: {e}")
        traceback.print_exc()
        return False

def test_data_loading_logic():
    """Test the data loading logic without actually inserting data."""
    print("\n🔍 Testing data loading logic...")
    
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app.core.database import SessionLocal
        from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun
        from app.etl.pipelines.pipeline_strategies.salesforce_to_postgresql_strategy import SalesforceToPostgreSQLStrategy
        
        db = SessionLocal()
        try:
            # Get Resort Pipeline
            resort_pipeline = db.query(ETLPipeline).filter(
                ETLPipeline.name == "Resort Pipeline"
            ).first()
            
            if not resort_pipeline:
                print("   ❌ Resort Pipeline not found")
                return False
            
            # Create test run
            test_run = ETLPipelineRun(
                pipeline_id=resort_pipeline.id,
                status="running",
                started_at=datetime.utcnow()
            )
            db.add(test_run)
            db.commit()
            db.refresh(test_run)
            
            # Create strategy
            strategy = SalesforceToPostgreSQLStrategy(db)
            
            # Sample transformed data
            sample_transformed_data = [
                {
                    'name': 'Test Resort Loading',
                    'country': 'Test Country',
                    'is_active': True,
                    'currency': 'USD',
                    'description': 'Test Description for Loading',
                    'migrated_id': 'test_loading_123',
                    '_pipeline_run_id': test_run.id,
                    '_extracted_at': datetime.utcnow().isoformat()
                }
            ]
            
            print(f"   Testing loading logic with {len(sample_transformed_data)} records...")
            
            # Test the loading logic (but rollback to avoid actual insertion)
            try:
                # Start a transaction
                db.begin()
                
                # Test the loading method
                loaded_count = strategy._load_to_destination(test_run, sample_transformed_data)
                
                print(f"   ✅ Loading logic successful - {loaded_count} records processed")
                
                # Rollback to avoid actual data insertion
                db.rollback()
                
                # Clean up test run
                db.delete(test_run)
                db.commit()
                
                return True
                
            except Exception as loading_error:
                db.rollback()
                print(f"   ❌ Loading logic failed: {loading_error}")
                traceback.print_exc()
                return False
            
        except Exception as setup_error:
            print(f"   ❌ Setup error: {setup_error}")
            traceback.print_exc()
            return False
        finally:
            db.close()
            
    except Exception as e:
        print(f"   ❌ Error testing loading logic: {e}")
        traceback.print_exc()
        return False

def test_complete_pipeline_flow():
    """Test the complete pipeline flow from extraction to loading."""
    print("\n🔍 Testing complete pipeline flow...")
    
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app.core.database import SessionLocal
        from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun
        from app.etl.pipelines.pipeline_strategies.salesforce_to_postgresql_strategy import SalesforceToPostgreSQLStrategy
        from app.etl.extractors.salesforce_extractor import SalesforceExtractor
        
        db = SessionLocal()
        try:
            # Get Resort Pipeline
            resort_pipeline = db.query(ETLPipeline).filter(
                ETLPipeline.name == "Resort Pipeline"
            ).first()
            
            if not resort_pipeline:
                print("   ❌ Resort Pipeline not found")
                return False
            
            print("   ✅ Resort Pipeline found")
            
            # Create test run
            test_run = ETLPipelineRun(
                pipeline_id=resort_pipeline.id,
                status="running",
                started_at=datetime.utcnow()
            )
            db.add(test_run)
            db.commit()
            db.refresh(test_run)
            
            # Create strategy and extractor
            strategy = SalesforceToPostgreSQLStrategy(db)
            extractor = SalesforceExtractor()
            
            # Test authentication
            if not extractor.authenticate():
                print("   ❌ Salesforce authentication failed")
                return False
            
            print("   ✅ Salesforce authentication successful")
            
            # Extract limited data
            print("   Extracting sample data...")
            extracted_data = extractor.extract_records(
                object_name=resort_pipeline.source_object,
                fields=resort_pipeline.source_fields or [],
                limit=2  # Just 2 records for testing
            )
            
            print(f"   ✅ Extracted {len(extracted_data)} records")
            
            # Transform data
            print("   Transforming data...")
            transformed_data = strategy._transform_data(resort_pipeline, test_run, extracted_data)
            
            print(f"   ✅ Transformed {len(transformed_data)} records")
            
            # Test loading (with rollback)
            print("   Testing loading (with rollback)...")
            db.begin()
            try:
                loaded_count = strategy._load_to_destination(test_run, transformed_data)
                print(f"   ✅ Loading test successful - {loaded_count} records would be loaded")
                
                # Rollback to avoid actual insertion
                db.rollback()
                
            except Exception as loading_error:
                db.rollback()
                print(f"   ❌ Loading test failed: {loading_error}")
                traceback.print_exc()
                return False
            
            # Clean up
            db.delete(test_run)
            db.commit()
            
            print("   ✅ Complete pipeline flow test successful!")
            return True
            
        except Exception as flow_error:
            print(f"   ❌ Pipeline flow test failed: {flow_error}")
            traceback.print_exc()
            return False
        finally:
            db.close()
            
    except Exception as e:
        print(f"   ❌ Error testing complete flow: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all transformation and loading tests."""
    print("🚀 Testing Resort Pipeline Transformation and Loading")
    print("=" * 70)
    
    tests = [
        ("Data Transformation", test_data_transformation),
        ("Destination Model Compatibility", test_destination_model_compatibility),
        ("Data Loading Logic", test_data_loading_logic),
        ("Complete Pipeline Flow", test_complete_pipeline_flow)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 70)
    print("📊 Test Results Summary:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All transformation and loading tests passed!")
        print("The Resort Pipeline should work correctly.")
    else:
        print("\n⚠️  Some tests failed. The pipeline needs fixes before it will work.")
    
    return all_passed

if __name__ == "__main__":
    main()
