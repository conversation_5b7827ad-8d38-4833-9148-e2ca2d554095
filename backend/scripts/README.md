# ETL Scripts Directory

This directory contains standalone ETL scripts for pipeline management, testing, diagnostics, and maintenance operations. These scripts are designed to be run independently for various ETL-related tasks.

## Script Categories

### Pipeline Creation Scripts
Scripts for creating and configuring ETL pipelines:

- `create_hotel_pipeline.py` - Creates Hotel Pipeline configuration
- `create_room_pipeline.py` - Creates Room Pipeline configuration  
- `create_room_type_pipeline.py` - Creates Room Type Pipeline configuration
- `setup_hotel_pipeline.py` - Complete Hotel Pipeline setup with migration
- `setup_extended_resort_pipeline.py` - Extended Resort Pipeline setup

### Pipeline Testing Scripts
Scripts for testing pipeline functionality:

- `test_hotel_pipeline_execution.py` - Tests Hotel Pipeline execution
- `test_room_pipeline.py` - Tests Room Pipeline functionality
- `test_room_type_pipeline.py` - Tests Room Type Pipeline
- `test_complete_pipeline_sequence.py` - Tests complete pipeline sequence
- `test_actual_pipeline_execution.py` - Tests actual pipeline execution
- `test_full_pipeline_execution.py` - Comprehensive pipeline execution test

### Data Processing Scripts
Scripts for direct data processing and creation:

- `create_all_room_data_directly.py` - Direct Room data creation bypassing pipeline
- `test_transformation_and_loading.py` - Tests transformation and loading logic
- `test_medusa_loading.py` - Tests Medusa platform loading
- `test_products_loading.py` - Tests product data loading

### Diagnostic Scripts
Scripts for diagnosing and troubleshooting issues:

- `diagnose_pipeline_issues.py` - General pipeline diagnostic tool
- `diagnose_room_pipeline_issues.py` - Room Pipeline specific diagnostics
- `diagnose_room_pipeline_execution_issues.py` - Room Pipeline execution diagnostics
- `diagnose_salesforce_connection.py` - Salesforce connection diagnostics
- `debug_database_state.py` - Database state debugging

### Analysis Scripts
Scripts for data analysis and investigation:

- `analyze_hotel_discrepancy.py` - Analyzes hotel data discrepancies
- `detailed_hotel_analysis.py` - Detailed hotel data analysis
- `investigate_missing_hotels.py` - Investigates missing hotel records
- `investigate_room_pipeline_data_flow.py` - Investigates Room Pipeline data flow
- `identify_missing_hotels.py` - Identifies missing hotel records

### Maintenance Scripts
Scripts for system maintenance and cleanup:

- `cleanup_stale_pipelines.py` - Cleans up stale pipeline runs
- `update_pipeline_configurations.py` - Updates pipeline configurations
- `update_pipelines_for_medusa_tables.py` - Updates pipelines for Medusa tables
- `update_resort_pipeline_fields.py` - Updates Resort Pipeline fields
- `update_resort_pipeline_for_hotel.py` - Updates Resort Pipeline for hotels

### Fix and Solution Scripts
Scripts for fixing specific issues:

- `fix_pipeline_issues.py` - General pipeline issue fixes
- `fix_room_pipeline_extraction.py` - Fixes Room Pipeline extraction issues
- `fix_room_pipeline_complete_extraction.py` - Complete Room Pipeline extraction fix
- `fix_room_pipeline_salesforce_extraction.py` - Salesforce extraction fixes
- `final_room_pipeline_solution.py` - Final comprehensive Room Pipeline solution
- `final_room_pipeline_execution_fix.py` - Final Room Pipeline execution fix

### Verification Scripts
Scripts for verifying system state and data integrity:

- `verify_junction_table_success.py` - Verifies junction table operations
- `verify_room_pipeline_complete.py` - Verifies complete Room Pipeline state
- `check_destination_schema.py` - Checks destination schema integrity

### Integration Testing Scripts
Scripts for testing system integrations:

- `test_salesforce_integration.py` - Tests Salesforce integration
- `test_complete_medusa_integration.py` - Tests complete Medusa integration
- `test_medusa_pipeline_execution.py` - Tests Medusa pipeline execution
- `test_medusa_transformation.py` - Tests Medusa data transformation
- `test_powderbyrne_connection.py` - Tests Powderbyrne connection
- `test_destination_model.py` - Tests destination model functionality

## Usage Guidelines

### Running Scripts
All scripts are designed to be run from the backend directory:

```bash
cd backend
python scripts/script_name.py
```

### Script Dependencies
Scripts automatically add the backend directory to the Python path:
```python
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
```

### Common Patterns
Most scripts follow these patterns:

1. **Setup**: Import required modules and setup database connections
2. **Execution**: Perform the main script functionality
3. **Reporting**: Provide detailed status and results
4. **Cleanup**: Clean up resources and connections

### Error Handling
Scripts include comprehensive error handling:
- Try-catch blocks for database operations
- Detailed error messages and logging
- Graceful failure handling
- Status reporting for success/failure

## Script Categories by Purpose

### Development and Testing
Use these scripts during development:
- `test_*` scripts for functionality testing
- `diagnose_*` scripts for troubleshooting
- `debug_*` scripts for detailed debugging

### Production Maintenance
Use these scripts for production maintenance:
- `cleanup_*` scripts for system cleanup
- `update_*` scripts for configuration updates
- `verify_*` scripts for system verification

### Data Operations
Use these scripts for data operations:
- `create_*` scripts for data creation
- `analyze_*` scripts for data analysis
- `investigate_*` scripts for data investigation

### Issue Resolution
Use these scripts for resolving issues:
- `fix_*` scripts for specific fixes
- `final_*` scripts for comprehensive solutions

## Best Practices

1. **Backup**: Always backup data before running maintenance scripts
2. **Testing**: Test scripts in development environment first
3. **Logging**: Review script output and logs carefully
4. **Documentation**: Read script documentation before running
5. **Monitoring**: Monitor system performance during script execution

## Adding New Scripts

When adding new scripts:

1. **Naming**: Use descriptive names following existing patterns
2. **Documentation**: Add docstrings and comments
3. **Error Handling**: Implement proper error handling
4. **Logging**: Include appropriate logging
5. **Path Setup**: Include the standard path setup code
6. **README**: Update this README with the new script

## Common Issues and Solutions

### Import Errors
If you encounter import errors:
1. Ensure you're running from the backend directory
2. Check that all required dependencies are installed
3. Verify the Python path setup in the script

### Database Connection Issues
If database connections fail:
1. Check environment variables in `.env` file
2. Verify database is running and accessible
3. Check database credentials and permissions

### Salesforce Connection Issues
If Salesforce connections fail:
1. Verify Salesforce credentials in `.env` file
2. Check Salesforce security token
3. Ensure IP address is whitelisted in Salesforce
