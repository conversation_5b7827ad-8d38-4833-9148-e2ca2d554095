#!/usr/bin/env python3
"""
Comprehensive diagnostic script for Room Pipeline execution issues:
- "Already running" false warnings
- Data persistence verification
- Process management investigation
"""
import sys
import os
import time
import requests
from datetime import datetime, timedelta

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun, ProductVariants


def investigate_pipeline_run_statuses():
    """Investigate current pipeline run statuses in the database"""
    print("🔍 PIPELINE RUN STATUS INVESTIGATION")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Get Room Pipeline
        room_pipeline = db.query(ETLPipeline).filter(
            ETLPipeline.name == "Room Pipeline"
        ).first()
        
        if not room_pipeline:
            print("❌ Room Pipeline not found")
            return False, None
        
        print(f"✅ Room Pipeline found (ID: {room_pipeline.id})")
        
        # Check for running pipelines
        running_runs = db.query(ETLPipelineRun).filter(
            ETLPipelineRun.pipeline_id == room_pipeline.id,
            ETLPipelineRun.status == 'running'
        ).all()
        
        print(f"\n🔄 Currently Running Pipelines: {len(running_runs)}")
        
        if running_runs:
            print("🚨 FOUND STUCK RUNNING PIPELINES:")
            for run in running_runs:
                runtime = datetime.utcnow() - run.started_at if run.started_at else timedelta(0)
                print(f"   Run {run.id}:")
                print(f"      Started: {run.started_at}")
                print(f"      Runtime: {runtime}")
                print(f"      Extract: {run.records_extracted}")
                print(f"      Transform: {run.records_transformed}")
                print(f"      Load: {run.records_loaded}")
                
                if runtime > timedelta(minutes=2):
                    print(f"      🚨 STUCK: Running for {runtime} - should be cancelled")
        
        # Check recent completed runs
        recent_runs = db.query(ETLPipelineRun).filter(
            ETLPipelineRun.pipeline_id == room_pipeline.id
        ).order_by(ETLPipelineRun.started_at.desc()).limit(10).all()
        
        print(f"\n📊 Recent Pipeline Runs (Last 10):")
        for run in recent_runs:
            duration = "Unknown"
            if run.started_at and run.completed_at:
                duration = str(run.completed_at - run.started_at)
            elif run.started_at:
                duration = f"{datetime.utcnow() - run.started_at} (ongoing)"
            
            status_icon = {
                'running': '🔄',
                'completed': '✅',
                'failed': '❌',
                'cancelled': '⏹️'
            }.get(run.status, '❓')
            
            print(f"   {status_icon} Run {run.id}: {run.status} | Duration: {duration}")
            print(f"      Records: E:{run.records_extracted} T:{run.records_transformed} L:{run.records_loaded}")
        
        return True, running_runs
        
    except Exception as e:
        print(f"❌ Error investigating pipeline statuses: {e}")
        return False, []
    finally:
        db.close()


def verify_data_persistence():
    """Verify current data persistence in product_variant table"""
    print("\n📊 DATA PERSISTENCE VERIFICATION")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Get current count
        total_variants = db.query(ProductVariants).count()
        print(f"📊 Total product variants: {total_variants}")
        
        if total_variants == 0:
            print("❌ NO DATA FOUND - Data persistence issue confirmed")
            return False
        
        # Check recent variants (last hour)
        recent_cutoff = datetime.utcnow() - timedelta(hours=1)
        recent_variants = db.query(ProductVariants).filter(
            ProductVariants.created_at >= recent_cutoff
        ).count()
        
        print(f"📊 Recent variants (last hour): {recent_variants}")
        
        # Check variants with product relationships
        variants_with_product = db.query(ProductVariants).filter(
            ProductVariants.product_id.isnot(None)
        ).count()
        
        variants_without_product = total_variants - variants_with_product
        
        print(f"🔗 Variants with product links: {variants_with_product}")
        print(f"🔗 Variants without product links: {variants_without_product}")
        
        if total_variants > 0:
            link_percentage = (variants_with_product / total_variants) * 100
            print(f"🔗 Link success rate: {link_percentage:.1f}%")
        
        # Show recent variants
        print(f"\n📋 Recent Product Variants:")
        recent_variants = db.query(ProductVariants).order_by(
            ProductVariants.created_at.desc()
        ).limit(5).all()
        
        for variant in recent_variants:
            product_name = "No Product"
            if variant.product:
                product_name = variant.product.title
            
            print(f"   - {variant.name} (ID: {variant.id})")
            print(f"     Product: {product_name}")
            print(f"     Connected: {variant.is_connected}")
            print(f"     Created: {variant.created_at}")
            print(f"     Pipeline Run: {variant.pipeline_run_id}")
        
        # Check pipeline run associations
        variants_by_run = db.query(ProductVariants.pipeline_run_id, 
                                 db.func.count(ProductVariants.id).label('count')).group_by(
            ProductVariants.pipeline_run_id
        ).all()
        
        print(f"\n📊 Variants by Pipeline Run:")
        for run_id, count in variants_by_run:
            print(f"   Run {run_id}: {count} variants")
        
        return total_variants > 0
        
    except Exception as e:
        print(f"❌ Error verifying data persistence: {e}")
        return False
    finally:
        db.close()


def cancel_stuck_pipeline_runs():
    """Cancel any stuck or orphaned pipeline runs"""
    print("\n🛑 CANCELLING STUCK PIPELINE RUNS")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Find stuck running pipelines
        stuck_runs = db.query(ETLPipelineRun).filter(
            ETLPipelineRun.pipeline_id == 4,  # Room Pipeline ID
            ETLPipelineRun.status == 'running'
        ).all()
        
        if not stuck_runs:
            print("✅ No stuck pipeline runs found")
            return True
        
        print(f"Found {len(stuck_runs)} stuck pipeline run(s)")
        
        cancelled_count = 0
        for run in stuck_runs:
            runtime = datetime.utcnow() - run.started_at if run.started_at else timedelta(0)
            
            # Cancel runs that have been running for more than 2 minutes
            if runtime > timedelta(minutes=2) or run.records_extracted is None:
                print(f"   Cancelling Run {run.id}: {runtime} runtime")
                
                run.status = 'cancelled'
                run.completed_at = datetime.utcnow()
                run.error_message = f"Cancelled - stuck for {runtime} (auto-cleanup)"
                cancelled_count += 1
            else:
                print(f"   Keeping Run {run.id}: {runtime} runtime (recent)")
        
        if cancelled_count > 0:
            db.commit()
            print(f"✅ Cancelled {cancelled_count} stuck pipeline run(s)")
        else:
            print("✅ No runs needed cancellation")
        
        return True
        
    except Exception as e:
        print(f"❌ Error cancelling stuck runs: {e}")
        db.rollback()
        return False
    finally:
        db.close()


def test_pipeline_execution():
    """Test Room Pipeline execution after cleanup"""
    print("\n🧪 TESTING PIPELINE EXECUTION")
    print("=" * 60)
    
    try:
        # Get initial state
        db = SessionLocal()
        try:
            initial_variants = db.query(ProductVariants).count()
            print(f"📊 Initial product variants: {initial_variants}")
        finally:
            db.close()
        
        # Test pipeline execution
        print("📡 Attempting Room Pipeline execution...")
        start_time = time.time()
        
        response = requests.post("http://localhost:8000/api/v1/pipelines/4/execute", timeout=10)
        
        if response.status_code != 200:
            print(f"❌ Failed to start pipeline: {response.status_code}")
            print(f"Response: {response.text}")
            
            if "already running" in response.text.lower():
                print("🚨 CONFIRMED: 'Already running' issue detected")
                return False
            return False
        
        result = response.json()
        run_id = result.get('run_id')
        print(f"✅ Pipeline started successfully: Run ID {run_id}")
        
        # Monitor execution
        print("⏳ Monitoring execution (60 second timeout)...")
        
        max_wait = 60
        check_interval = 5
        
        while time.time() - start_time < max_wait:
            try:
                elapsed = time.time() - start_time
                
                # Get status
                status_response = requests.get("http://localhost:8000/api/v1/pipelines/4/runs", timeout=5)
                
                if status_response.status_code == 200:
                    runs = status_response.json()
                    if runs:
                        latest_run = runs[0]
                        status = latest_run.get('status')
                        extracted = latest_run.get('records_extracted')
                        transformed = latest_run.get('records_transformed')
                        loaded = latest_run.get('records_loaded')
                        
                        print(f"   [{elapsed:3.0f}s] {status} | E:{extracted} T:{transformed} L:{loaded}")
                        
                        # Check database changes
                        db = SessionLocal()
                        try:
                            current_variants = db.query(ProductVariants).count()
                            if current_variants != initial_variants:
                                print(f"   [{elapsed:3.0f}s] 🎉 Database: {current_variants} variants (+{current_variants - initial_variants})")
                        finally:
                            db.close()
                        
                        if status == 'completed':
                            print(f"✅ COMPLETED in {elapsed:.1f} seconds!")
                            return True
                        elif status == 'failed':
                            error = latest_run.get('error_message', 'Unknown')
                            print(f"❌ FAILED: {error}")
                            return False
                
                time.sleep(check_interval)
                
            except Exception as e:
                print(f"⚠️ Monitoring error: {e}")
                time.sleep(check_interval)
        
        print(f"⏰ TIMEOUT after {max_wait} seconds")
        return False
        
    except Exception as e:
        print(f"❌ Error testing pipeline execution: {e}")
        return False


def investigate_process_management():
    """Investigate pipeline execution environment and process management"""
    print("\n🔧 PROCESS MANAGEMENT INVESTIGATION")
    print("=" * 60)
    
    try:
        # Check pipeline status endpoint
        print("📡 Checking pipeline status endpoint...")
        
        try:
            response = requests.get("http://localhost:8000/api/v1/pipelines/4", timeout=5)
            if response.status_code == 200:
                pipeline_info = response.json()
                print(f"✅ Pipeline endpoint accessible")
                print(f"   Name: {pipeline_info.get('name', 'Unknown')}")
                print(f"   Active: {pipeline_info.get('is_active', 'Unknown')}")
            else:
                print(f"⚠️ Pipeline endpoint returned: {response.status_code}")
        except Exception as e:
            print(f"❌ Pipeline endpoint error: {e}")
        
        # Check recent runs endpoint
        print("\n📊 Checking recent runs endpoint...")
        
        try:
            response = requests.get("http://localhost:8000/api/v1/pipelines/4/runs", timeout=5)
            if response.status_code == 200:
                runs = response.json()
                print(f"✅ Runs endpoint accessible - {len(runs)} runs found")
                
                if runs:
                    latest = runs[0]
                    print(f"   Latest run: {latest.get('status')} (ID: {latest.get('id')})")
            else:
                print(f"⚠️ Runs endpoint returned: {response.status_code}")
        except Exception as e:
            print(f"❌ Runs endpoint error: {e}")
        
        # Check for background processes
        print("\n🔍 Checking for background processes...")
        
        db = SessionLocal()
        try:
            # Check for any running pipelines across all pipeline types
            all_running = db.query(ETLPipelineRun).filter(
                ETLPipelineRun.status == 'running'
            ).all()
            
            print(f"📊 Total running pipelines (all types): {len(all_running)}")
            
            if all_running:
                for run in all_running:
                    pipeline = db.query(ETLPipeline).filter(
                        ETLPipeline.id == run.pipeline_id
                    ).first()
                    pipeline_name = pipeline.name if pipeline else "Unknown"
                    runtime = datetime.utcnow() - run.started_at if run.started_at else timedelta(0)
                    
                    print(f"   {pipeline_name} (Run {run.id}): {runtime}")
            
            return len(all_running) == 0
            
        finally:
            db.close()
        
    except Exception as e:
        print(f"❌ Error investigating process management: {e}")
        return False


def main():
    """Main diagnostic and resolution function"""
    print("🔍 ROOM PIPELINE EXECUTION ISSUES DIAGNOSTIC")
    print("=" * 70)
    print(f"Timestamp: {datetime.utcnow()}")
    print()
    
    # Step 1: Investigate pipeline run statuses
    status_ok, stuck_runs = investigate_pipeline_run_statuses()
    
    # Step 2: Verify data persistence
    data_ok = verify_data_persistence()
    
    # Step 3: Cancel stuck pipeline runs
    cleanup_ok = cancel_stuck_pipeline_runs()
    
    # Step 4: Investigate process management
    process_ok = investigate_process_management()
    
    # Step 5: Test pipeline execution
    execution_ok = test_pipeline_execution()
    
    print("\n" + "=" * 70)
    print("📊 DIAGNOSTIC RESULTS SUMMARY")
    print("=" * 70)
    
    checks = [
        ("Pipeline Status Investigation", status_ok),
        ("Data Persistence Verification", data_ok),
        ("Stuck Runs Cleanup", cleanup_ok),
        ("Process Management", process_ok),
        ("Pipeline Execution Test", execution_ok)
    ]
    
    passed = sum(1 for _, success in checks if success)
    total = len(checks)
    
    for check_name, success in checks:
        status = "✅" if success else "❌"
        print(f"{status} {check_name}")
    
    success_rate = (passed / total) * 100
    print(f"\nDiagnostic Success Rate: {passed}/{total} ({success_rate:.1f}%)")
    
    # Provide specific recommendations
    print(f"\n💡 SPECIFIC FINDINGS:")
    
    if stuck_runs:
        print(f"   🔧 Found {len(stuck_runs)} stuck pipeline runs - cleaned up")
    
    if not data_ok:
        print("   🔧 Data persistence issue - no records in product_variant table")
    
    if not process_ok:
        print("   🔧 Process management issue - background processes interfering")
    
    if not execution_ok:
        print("   🔧 Pipeline execution issue - 'already running' or other errors")
    
    print(f"\n🎯 RESOLUTION STATUS:")
    
    if execution_ok:
        print("   ✅ Room Pipeline execution working correctly")
        print("   ✅ 'Already running' issue resolved")
        print("   ✅ Data persistence functioning")
    elif cleanup_ok and not execution_ok:
        print("   ⚠️ Cleanup successful but execution still has issues")
        print("   🔧 May need server restart or deeper investigation")
    else:
        print("   ❌ Multiple issues detected - manual intervention required")
    
    print(f"\n📋 NEXT STEPS:")
    if execution_ok:
        print("   🎉 Room Pipeline is fully operational!")
    else:
        print("   1. Restart the application server")
        print("   2. Clear any remaining background processes")
        print("   3. Re-run this diagnostic script")
        print("   4. Check server logs for detailed error messages")
    
    return execution_ok


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
