#!/usr/bin/env python3
"""
Test Hotel Pipeline execution with real Salesforce data.
"""
import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun, ProductCategory, Destination
from app.etl.pipelines.pipeline_strategies.salesforce_to_postgresql_strategy import SalesforceToPostgreSQLStrategy
from app.services.websocket_manager import websocket_manager
from sqlalchemy import text
import asyncio
from datetime import datetime


def test_hotel_pipeline():
    """Test Hotel Pipeline execution"""
    print("🏨 Testing Hotel Pipeline Execution")
    print("=" * 60)
    
    db = SessionLocal()
    
    try:
        # Get Hotel Pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Hotel Pipeline").first()
        if not pipeline:
            print("❌ Hotel Pipeline not found!")
            return False
        
        print(f"✅ Found Hotel Pipeline (ID: {pipeline.id})")
        print(f"   Source Object: {pipeline.source_object}")
        print(f"   Destination Table: {pipeline.destination_table}")
        
        # Check current data counts using raw SQL to avoid model mismatch
        hotel_count_result = db.execute(text("SELECT COUNT(*) FROM hotel"))
        hotel_count_before = hotel_count_result.scalar()

        category_count_result = db.execute(text("SELECT COUNT(*) FROM product_category"))
        category_count_before = category_count_result.scalar()

        destination_count = db.query(Destination).count()

        print(f"\n📊 Current Data Counts:")
        print(f"   Hotels: {hotel_count_before}")
        print(f"   Product Categories: {category_count_before}")
        print(f"   Destinations: {destination_count}")
        
        if destination_count == 0:
            print("⚠️  No destinations found - you may want to run Resort Pipeline first")
        
        # Create pipeline run
        run = ETLPipelineRun(
            pipeline_id=pipeline.id,
            status="running",
            started_at=datetime.utcnow()
        )
        db.add(run)
        db.commit()
        
        print(f"\n🚀 Starting Hotel Pipeline execution (Run ID: {run.id})")
        
        # Execute pipeline
        strategy = SalesforceToPostgreSQLStrategy(db)
        
        # Run the pipeline
        result = strategy.execute(pipeline, run)
        
        # Check results
        print(f"\n📊 Pipeline Execution Results:")
        print(f"   Success: {result.success}")
        print(f"   Records Extracted: {result.records_extracted}")
        print(f"   Records Transformed: {result.records_transformed}")
        print(f"   Records Loaded: {result.records_loaded}")
        print(f"   Error Message: {result.error_message or 'None'}")
        
        # Check final data counts using raw SQL
        hotel_count_result = db.execute(text("SELECT COUNT(*) FROM hotel"))
        hotel_count_after = hotel_count_result.scalar()

        category_count_result = db.execute(text("SELECT COUNT(*) FROM product_category"))
        category_count_after = category_count_result.scalar()

        print(f"\n📊 Final Data Counts:")
        print(f"   Hotels: {hotel_count_after} (was {hotel_count_before})")
        print(f"   Product Categories: {category_count_after} (was {category_count_before})")

        # Show sample hotels using raw SQL
        if hotel_count_after > 0:
            print(f"\n🏨 Sample Hotels:")
            sample_result = db.execute(text("""
                SELECT h.name, d.name as destination_name, pc.name as category_name
                FROM hotel h
                LEFT JOIN destination d ON h.destination_id = d.id
                LEFT JOIN product_category pc ON h.category_id = pc.id
                LIMIT 5
            """))

            for row in sample_result:
                destination_name = row[1] if row[1] else "No Destination"
                category_name = row[2] if row[2] else "No Category"
                print(f"   • {row[0]} (Destination: {destination_name}, Category: {category_name})")
        
        success = result.success and result.records_loaded > 0
        
        if success:
            print(f"\n🎉 Hotel Pipeline executed successfully!")
            print(f"   ✅ Loaded {result.records_loaded} hotel records")
            print(f"   ✅ Created {hotel_count_after - hotel_count_before} new hotels")
            print(f"   ✅ Created {category_count_after - category_count_before} new categories")
        else:
            print(f"\n❌ Hotel Pipeline execution failed!")
            if result.error_message:
                print(f"   Error: {result.error_message}")
        
        return success
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        db.close()


def main():
    """Run the test"""
    success = test_hotel_pipeline()
    
    if success:
        print("\n🎉 Hotel Pipeline test completed successfully!")
    else:
        print("\n❌ Hotel Pipeline test failed!")
    
    return success


if __name__ == "__main__":
    main()
