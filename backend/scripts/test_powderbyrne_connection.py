#!/usr/bin/env python3
"""
Test script to verify ETL pipeline configuration with powderbyrne database.
"""
import os
import sys
import traceback
from urllib.parse import urlparse

def test_environment_variables():
    """Test that all required environment variables are set."""
    print("🔍 Testing environment variables...")
    
    required_vars = [
        'DATABASE_URL',
        'SECRET_KEY', 
        'ENVIRONMENT'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        return False
    else:
        print("✅ All required environment variables are set")
        return True

def test_database_connection():
    """Test direct database connection using psycopg2."""
    print("\n🔍 Testing direct database connection...")
    
    try:
        import psycopg2
        
        db_url = os.getenv('DATABASE_URL')
        parsed = urlparse(db_url)
        
        print(f"   Host: {parsed.hostname}")
        print(f"   Port: {parsed.port}")
        print(f"   Database: {parsed.path[1:]}")
        print(f"   User: {parsed.username}")
        
        # Connect to database
        conn = psycopg2.connect(
            host=parsed.hostname,
            port=parsed.port,
            database=parsed.path[1:],
            user=parsed.username,
            password=parsed.password,
            sslmode='require'
        )
        
        cursor = conn.cursor()
        
        # Test basic connection
        cursor.execute('SELECT 1')
        result = cursor.fetchone()
        print("✅ Basic database connection successful")
        
        # Test destination table
        cursor.execute("SELECT COUNT(*) FROM destination")
        count = cursor.fetchone()[0]
        print(f"✅ destination table exists with {count} records")
        
        # Get sample records
        if count > 0:
            cursor.execute("SELECT id, name, country FROM destination LIMIT 3")
            records = cursor.fetchall()
            print("📋 Sample destination records:")
            for record in records:
                print(f"   - ID: {record[0]}, Name: {record[1]}, Country: {record[2]}")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        traceback.print_exc()
        return False

def test_sqlalchemy_models():
    """Test SQLAlchemy models and configuration."""
    print("\n🔍 Testing SQLAlchemy models...")
    
    try:
        # Add current directory to Python path
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app.core.database import check_db_connection, SessionLocal
        from app.models.etl_pipeline import Destination, ETLPipeline
        
        print("✅ Successfully imported SQLAlchemy models")
        
        # Test database connection through SQLAlchemy
        if check_db_connection():
            print("✅ SQLAlchemy database connection successful")
        else:
            print("❌ SQLAlchemy database connection failed")
            return False
        
        # Test querying models
        db = SessionLocal()
        try:
            # Test Destination model
            destinations = db.query(Destination).limit(3).all()
            print(f"✅ Successfully queried Destination model: {len(destinations)} records")
            
            if destinations:
                print("📋 Sample Destination records via SQLAlchemy:")
                for dest in destinations:
                    print(f"   - ID: {dest.id}, Name: {dest.name}, Country: {dest.country}")
            
            # Test ETL Pipeline model
            pipelines = db.query(ETLPipeline).filter(
                ETLPipeline.destination_table == "destination"
            ).all()
            print(f"✅ Found {len(pipelines)} ETL pipelines targeting destination table")
            
            for pipeline in pipelines:
                print(f"   - Pipeline: {pipeline.name}")
                print(f"     Source: {pipeline.source_object}")
                print(f"     Destination: {pipeline.destination_table}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error querying models: {e}")
            traceback.print_exc()
            return False
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Error importing models: {e}")
        traceback.print_exc()
        return False

def test_etl_pipeline_configuration():
    """Test ETL pipeline configuration for destination table."""
    print("\n🔍 Testing ETL pipeline configuration...")
    
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app.core.database import SessionLocal
        from app.models.etl_pipeline import ETLPipeline
        
        db = SessionLocal()
        try:
            # Check Resort Pipeline configuration
            resort_pipeline = db.query(ETLPipeline).filter(
                ETLPipeline.name == "Resort Pipeline"
            ).first()
            
            if resort_pipeline:
                print("✅ Resort Pipeline found")
                print(f"   Source Object: {resort_pipeline.source_object}")
                print(f"   Destination Table: {resort_pipeline.destination_table}")
                print(f"   Source Fields: {resort_pipeline.source_fields}")
                print(f"   Destination Fields: {resort_pipeline.destination_fields}")
                
                if resort_pipeline.destination_table == "destination":
                    print("✅ Resort Pipeline correctly configured for destination table")
                else:
                    print(f"❌ Resort Pipeline destination table is '{resort_pipeline.destination_table}', should be 'destination'")
                    return False
            else:
                print("⚠️  Resort Pipeline not found - you may need to create it")
            
            return True
            
        except Exception as e:
            print(f"❌ Error checking pipeline configuration: {e}")
            traceback.print_exc()
            return False
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Error testing pipeline configuration: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 Testing ETL Pipeline Configuration for powderbyrne Database")
    print("=" * 70)
    
    tests = [
        test_environment_variables,
        test_database_connection,
        test_sqlalchemy_models,
        test_etl_pipeline_configuration
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            traceback.print_exc()
            results.append(False)
    
    print("\n" + "=" * 70)
    print("📊 Test Results Summary:")
    
    test_names = [
        "Environment Variables",
        "Database Connection", 
        "SQLAlchemy Models",
        "ETL Pipeline Configuration"
    ]
    
    all_passed = True
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {i+1}. {name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! Your ETL pipeline is correctly configured for the powderbyrne database.")
        print("\n📋 Next Steps:")
        print("   1. Run the Resort Pipeline to load destination data")
        print("   2. Monitor pipeline execution through the web interface")
        print("   3. Verify data integrity after pipeline completion")
    else:
        print("\n⚠️  Some tests failed. Please review the errors above and fix the issues.")
    
    return all_passed

if __name__ == "__main__":
    main()
