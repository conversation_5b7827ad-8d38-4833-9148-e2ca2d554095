#!/usr/bin/env python3
"""
Final test of Room Pipeline with proper authentication and monitoring
"""
import sys
import os
import time
import requests
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipelineRun, ProductVariants


def cancel_any_stuck_pipelines():
    """Cancel any stuck Room Pipeline runs"""
    print("🛑 CLEANING UP STUCK PIPELINES")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        stuck_runs = db.query(ETLPipelineRun).filter(
            ETLPipelineRun.pipeline_id == 4,
            ETLPipelineRun.status == 'running'
        ).all()
        
        if stuck_runs:
            for run in stuck_runs:
                run.status = 'cancelled'
                run.completed_at = datetime.utcnow()
                run.error_message = "Cancelled before final test"
            db.commit()
            print(f"✅ Cancelled {len(stuck_runs)} stuck run(s)")
        else:
            print("✅ No stuck runs found")
        
        return True
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        db.close()


def execute_room_pipeline_final_test():
    """Execute Room Pipeline with comprehensive monitoring"""
    print("\n🚀 ROOM PIPELINE FINAL TEST")
    print("=" * 50)
    
    try:
        # Get initial state
        db = SessionLocal()
        try:
            initial_variants = db.query(ProductVariants).count()
            print(f"📊 Initial product variants: {initial_variants}")
        finally:
            db.close()
        
        # Execute pipeline
        print("📡 Starting Room Pipeline...")
        start_time = time.time()
        
        response = requests.post("http://localhost:8000/api/v1/pipelines/4/execute", timeout=10)
        
        if response.status_code != 200:
            print(f"❌ Failed to start: {response.status_code}")
            print(f"Response: {response.text}")
            return False
        
        result = response.json()
        run_id = result.get('run_id')
        print(f"✅ Started successfully: Run ID {run_id}")
        
        # Monitor with detailed logging
        print("⏳ Monitoring execution (max 3 minutes)...")
        
        max_wait = 180  # 3 minutes
        check_interval = 10  # 10 seconds
        last_extracted = None
        extraction_stuck_count = 0
        
        while time.time() - start_time < max_wait:
            try:
                elapsed = time.time() - start_time
                
                # Get pipeline status
                status_response = requests.get("http://localhost:8000/api/v1/pipelines/4/runs", timeout=5)
                
                if status_response.status_code == 200:
                    runs = status_response.json()
                    if runs:
                        latest_run = runs[0]
                        status = latest_run.get('status')
                        extracted = latest_run.get('records_extracted', 0)
                        transformed = latest_run.get('records_transformed', 0)
                        loaded = latest_run.get('records_loaded', 0)
                        error_msg = latest_run.get('error_message', '')
                        
                        # Check for extraction progress
                        if extracted == last_extracted:
                            extraction_stuck_count += 1
                        else:
                            extraction_stuck_count = 0
                        last_extracted = extracted
                        
                        print(f"   [{elapsed:3.0f}s] {status} | Extract:{extracted} Transform:{transformed} Load:{loaded}")
                        
                        if error_msg:
                            print(f"   [{elapsed:3.0f}s] Error: {error_msg}")
                        
                        # Check database changes
                        db = SessionLocal()
                        try:
                            current_variants = db.query(ProductVariants).count()
                            if current_variants != initial_variants:
                                print(f"   [{elapsed:3.0f}s] 🎉 Database: {current_variants} variants (+{current_variants - initial_variants})")
                        finally:
                            db.close()
                        
                        # Check completion
                        if status == 'completed':
                            print(f"✅ COMPLETED in {elapsed:.1f} seconds!")
                            print(f"📊 Final stats: Extract:{extracted} Transform:{transformed} Load:{loaded}")
                            return True
                        
                        elif status == 'failed':
                            print(f"❌ FAILED after {elapsed:.1f} seconds")
                            print(f"Error: {error_msg}")
                            return False
                        
                        # Check if stuck in extraction
                        if extraction_stuck_count >= 6 and extracted == 0:  # 60 seconds no extraction
                            print(f"🚨 STUCK in extraction phase for 60+ seconds")
                            print("   Likely Salesforce API timeout or authentication issue")
                            return False
                        
                        # Check if stuck in loading
                        if extracted > 0 and transformed > 0 and loaded == 0 and extraction_stuck_count >= 3:
                            print(f"🚨 STUCK in loading phase")
                            print("   Data extracted and transformed but not loading to database")
                            return False
                
                time.sleep(check_interval)
                
            except Exception as e:
                print(f"⚠️ Monitoring error: {e}")
                time.sleep(check_interval)
        
        print(f"⏰ TIMEOUT after {max_wait} seconds")
        return False
        
    except Exception as e:
        print(f"❌ Execution error: {e}")
        return False


def verify_final_results():
    """Verify the final results"""
    print("\n🔍 FINAL RESULTS VERIFICATION")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        # Check product variants
        total_variants = db.query(ProductVariants).count()
        print(f"📊 Total product variants: {total_variants}")
        
        if total_variants > 0:
            print("✅ Data persistence SUCCESS!")
            
            # Show sample variants
            sample_variants = db.query(ProductVariants).limit(5).all()
            print(f"\n📋 Sample Product Variants:")
            
            for variant in sample_variants:
                product_name = "No Product"
                if variant.product:
                    product_name = variant.product.title
                
                print(f"   - {variant.name}")
                print(f"     Product: {product_name}")
                print(f"     Connected: {variant.is_connected}")
                print(f"     Created: {variant.created_at}")
            
            # Check relationships
            variants_with_product = db.query(ProductVariants).filter(
                ProductVariants.product_id.isnot(None)
            ).count()
            
            if total_variants > 0:
                link_rate = (variants_with_product / total_variants) * 100
                print(f"\n🔗 Relationship Success: {variants_with_product}/{total_variants} ({link_rate:.1f}%)")
            
            return True
        else:
            print("❌ No product variants found - data persistence FAILED")
            return False
        
    except Exception as e:
        print(f"❌ Error verifying results: {e}")
        return False
    finally:
        db.close()


def main():
    """Main test function"""
    print("🧪 ROOM PIPELINE FINAL COMPREHENSIVE TEST")
    print("=" * 60)
    print(f"Timestamp: {datetime.utcnow()}")
    print()
    
    # Step 1: Clean up
    cleanup_success = cancel_any_stuck_pipelines()
    
    # Step 2: Execute pipeline
    execution_success = execute_room_pipeline_final_test()
    
    # Step 3: Verify results
    verification_success = verify_final_results()
    
    print("\n" + "=" * 60)
    print("📊 FINAL TEST RESULTS")
    print("=" * 60)
    
    results = [
        ("Cleanup", cleanup_success),
        ("Execution", execution_success),
        ("Verification", verification_success)
    ]
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅" if success else "❌"
        print(f"{status} {test_name}")
    
    success_rate = (passed / total) * 100
    print(f"\nOverall Success Rate: {passed}/{total} ({success_rate:.1f}%)")
    
    if execution_success and verification_success:
        print("\n🎉 ROOM PIPELINE FULLY OPERATIONAL!")
        print("\n✅ Confirmed Working:")
        print("   - Room__c data extraction from Salesforce")
        print("   - Data transformation and processing")
        print("   - Database persistence to product_variant table")
        print("   - Foreign key relationships to product")
        print("   - Complete ETL workflow execution")
        print("\n🚀 Both data persistence and performance issues RESOLVED!")
        
    elif execution_success and not verification_success:
        print("\n⚠️ PIPELINE EXECUTES BUT DATA PERSISTENCE ISSUE")
        print("   - Pipeline completes successfully")
        print("   - But data not persisting to database")
        print("   - Check database transaction handling")
        
    elif not execution_success:
        print("\n💥 PIPELINE EXECUTION ISSUE")
        print("   - Pipeline not completing successfully")
        print("   - Check Salesforce API connectivity")
        print("   - Check server logs for detailed errors")
    
    return execution_success and verification_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
