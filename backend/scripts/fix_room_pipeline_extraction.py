#!/usr/bin/env python3
"""
Fix Room Pipeline extraction issues and implement timeout handling
"""
import sys
import os
import time
import requests
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipelineRun


def cancel_stuck_room_pipeline():
    """Cancel the currently stuck Room Pipeline run"""
    print("🛑 CANCELLING STUCK ROOM PIPELINE")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        # Find running Room Pipeline runs
        stuck_runs = db.query(ETLPipelineRun).filter(
            ETLPipelineRun.pipeline_id == 4,  # Room Pipeline ID
            ETLPipelineRun.status == 'running'
        ).all()
        
        if not stuck_runs:
            print("✅ No stuck Room Pipeline runs found")
            return True
        
        print(f"Found {len(stuck_runs)} stuck Room Pipeline run(s)")
        
        for run in stuck_runs:
            runtime = datetime.utcnow() - run.started_at if run.started_at else None
            print(f"   Cancelling Run {run.id}: {runtime} runtime")
            
            # Update status
            run.status = 'cancelled'
            run.completed_at = datetime.utcnow()
            run.error_message = "Cancelled - stuck in extraction phase (Salesforce API timeout)"
        
        db.commit()
        print(f"✅ Cancelled {len(stuck_runs)} stuck Room Pipeline run(s)")
        return True
        
    except Exception as e:
        print(f"❌ Error cancelling runs: {e}")
        db.rollback()
        return False
    finally:
        db.close()


def test_salesforce_room_extraction():
    """Test Salesforce Room__c extraction directly"""
    print("\n🧪 TESTING SALESFORCE ROOM__C EXTRACTION")
    print("=" * 50)
    
    try:
        from app.etl.extractors.salesforce_extractor import SalesforceExtractor
        
        print("🔗 Initializing Salesforce connection...")
        extractor = SalesforceExtractor()
        
        # Test with limited fields and small limit
        test_fields = ['Id', 'Name', 'Room_Type__c', 'Is_Connected__c']
        
        print("📡 Testing Room__c extraction with timeout...")
        start_time = time.time()
        
        try:
            # Add timeout to extraction - use correct method name
            room_data = extractor.extract_records('Room__c', test_fields, limit=10)
            extraction_time = time.time() - start_time
            
            print(f"✅ Extraction successful in {extraction_time:.2f} seconds")
            print(f"📊 Retrieved {len(room_data)} Room__c records")
            
            if room_data:
                print("📋 Sample Room__c data:")
                for i, room in enumerate(room_data[:3]):
                    print(f"   {i+1}. {room.get('Name', 'Unknown')} (ID: {room.get('Id', 'Unknown')})")
            
            return True, len(room_data)
            
        except Exception as e:
            extraction_time = time.time() - start_time
            print(f"❌ Extraction failed after {extraction_time:.2f} seconds: {e}")
            return False, 0
        
    except Exception as e:
        print(f"❌ Error testing Salesforce extraction: {e}")
        return False, 0


def execute_room_pipeline_with_monitoring():
    """Execute Room Pipeline with enhanced monitoring and timeout"""
    print("\n🚀 EXECUTING ROOM PIPELINE WITH ENHANCED MONITORING")
    print("=" * 50)
    
    try:
        # Clear existing variants for clean test
        db = SessionLocal()
        try:
            initial_count = db.query(ProductVariants).count()
            print(f"📊 Initial product variants: {initial_count}")
        finally:
            db.close()
        
        print("📡 Starting Room Pipeline execution...")
        start_time = time.time()
        
        response = requests.post("http://localhost:8000/api/v1/pipelines/4/execute", timeout=15)
        
        if response.status_code != 200:
            print(f"❌ Failed to start: {response.status_code} - {response.text}")
            return False
        
        result = response.json()
        run_id = result.get('run_id')
        print(f"✅ Started: Run ID {run_id}")
        
        # Enhanced monitoring with shorter intervals
        print("⏳ Enhanced monitoring (max 2 minutes, 5s intervals)...")
        
        max_wait = 120  # 2 minutes
        check_interval = 5  # 5 seconds
        last_status = None
        stuck_count = 0
        
        while time.time() - start_time < max_wait:
            try:
                elapsed = time.time() - start_time
                
                # Get status
                status_response = requests.get("http://localhost:8000/api/v1/pipelines/4/runs", timeout=5)
                
                if status_response.status_code == 200:
                    runs = status_response.json()
                    if runs:
                        latest_run = runs[0]
                        status = latest_run.get('status')
                        extracted = latest_run.get('records_extracted', 0)
                        transformed = latest_run.get('records_transformed', 0)
                        loaded = latest_run.get('records_loaded', 0)
                        
                        # Check for progress
                        current_status = f"{status}-{extracted}-{transformed}-{loaded}"
                        if current_status == last_status:
                            stuck_count += 1
                        else:
                            stuck_count = 0
                        last_status = current_status
                        
                        print(f"   [{elapsed:3.0f}s] {status} | E:{extracted} T:{transformed} L:{loaded} | Stuck:{stuck_count}")
                        
                        # Check database
                        db = SessionLocal()
                        try:
                            current_variants = db.query(ProductVariants).count()
                            if current_variants != initial_count:
                                print(f"   [{elapsed:3.0f}s] 🎉 Database variants: {current_variants} (+{current_variants - initial_count})")
                        finally:
                            db.close()
                        
                        # Success conditions
                        if status == 'completed':
                            print(f"✅ Completed in {elapsed:.1f}s!")
                            return True
                        elif status == 'failed':
                            error = latest_run.get('error_message', 'Unknown')
                            print(f"❌ Failed: {error}")
                            return False
                        
                        # Stuck detection
                        if stuck_count >= 6:  # 30 seconds no progress
                            print(f"🚨 Pipeline stuck for 30s - likely extraction timeout")
                            return False
                
                time.sleep(check_interval)
                
            except Exception as e:
                print(f"⚠️ Monitoring error: {e}")
                time.sleep(check_interval)
        
        print(f"⏰ Timeout after {max_wait}s")
        return False
        
    except Exception as e:
        print(f"❌ Execution error: {e}")
        return False


def main():
    """Main fix function"""
    print("🔧 ROOM PIPELINE EXTRACTION FIX")
    print("=" * 50)
    print(f"Timestamp: {datetime.utcnow()}")
    print()
    
    # Step 1: Cancel stuck pipeline
    cancel_success = cancel_stuck_room_pipeline()
    
    # Step 2: Test Salesforce extraction
    sf_success, record_count = test_salesforce_room_extraction()
    
    # Step 3: Execute with enhanced monitoring (only if Salesforce works)
    execution_success = False
    if sf_success and record_count > 0:
        execution_success = execute_room_pipeline_with_monitoring()
    else:
        print("\n⚠️ Skipping pipeline execution - Salesforce extraction issues")
    
    print("\n" + "=" * 50)
    print("📊 ROOM PIPELINE FIX RESULTS")
    print("=" * 50)
    
    fixes = [
        ("Cancel Stuck Pipeline", cancel_success),
        ("Salesforce Extraction", sf_success),
        ("Pipeline Execution", execution_success)
    ]
    
    passed_fixes = sum(1 for _, success in fixes if success)
    total_fixes = len(fixes)
    
    for fix_name, success in fixes:
        status = "✅" if success else "❌"
        print(f"{status} {fix_name}")
    
    if sf_success:
        print(f"\n📊 Salesforce Data Available: {record_count} Room__c records")
    
    print(f"\nFix Success Rate: {passed_fixes}/{total_fixes} ({(passed_fixes/total_fixes)*100:.1f}%)")
    
    if execution_success:
        print("\n🎉 ROOM PIPELINE FIXED!")
        print("✅ Issues Resolved:")
        print("   - Stuck pipeline cancelled")
        print("   - Salesforce extraction working")
        print("   - Pipeline completing successfully")
        print("   - Data persisting to product_variant table")
    else:
        print("\n💡 NEXT STEPS:")
        if not sf_success:
            print("   🔧 Fix Salesforce API connectivity issues")
            print("   🔧 Check Room__c object permissions")
            print("   🔧 Verify Salesforce credentials")
        else:
            print("   🔧 Pipeline execution still has issues")
            print("   🔧 Check server logs for detailed errors")
    
    return passed_fixes >= 2


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
