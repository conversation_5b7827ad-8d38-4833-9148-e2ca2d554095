#!/usr/bin/env python3
"""
Analyze the discrepancy between pipeline reported 241 loaded records and 235 actual database records
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.config.settings import settings
import j<PERSON>

def analyze_database_state():
    """Analyze the current database state and recent pipeline runs"""
    print("🔍 Analyzing Hotel Database State")
    print("=" * 60)
    
    engine = create_engine(settings.database_url)
    
    with engine.connect() as conn:
        try:
            # 1. Get current hotel count
            hotel_count = conn.execute(text("SELECT COUNT(*) FROM hotel")).scalar()
            print(f"📊 Current hotel count: {hotel_count}")
            
            # 2. Get recent pipeline runs for Hotel Pipeline
            recent_runs = conn.execute(text("""
                SELECT pr.id, pr.status, pr.records_extracted, pr.records_transformed, 
                       pr.records_loaded, pr.started_at, pr.completed_at, pr.error_message
                FROM etl_pipeline_runs pr
                JOIN etl_pipelines p ON pr.pipeline_id = p.id
                WHERE p.name = 'Hotel Pipeline'
                ORDER BY pr.started_at DESC
                LIMIT 5
            """)).fetchall()
            
            print(f"\n📈 Recent Hotel Pipeline Runs:")
            for run in recent_runs:
                print(f"   Run {run[0]}: {run[1]} - Extracted: {run[2]}, Transformed: {run[3]}, Loaded: {run[4]}")
                print(f"      Started: {run[5]}, Completed: {run[6]}")
                if run[7]:
                    print(f"      Error: {run[7]}")
            
            # 3. Check for duplicate hotel names
            duplicates = conn.execute(text("""
                SELECT name, COUNT(*) as count
                FROM hotel
                GROUP BY name
                HAVING COUNT(*) > 1
                ORDER BY count DESC
            """)).fetchall()
            
            if duplicates:
                print(f"\n⚠️  Duplicate hotel names found ({len(duplicates)}):")
                for dup in duplicates:
                    print(f"   '{dup[0]}': {dup[1]} records")
            else:
                print(f"\n✅ No duplicate hotel names found")
            
            # 4. Check for hotels with missing required fields
            missing_data = conn.execute(text("""
                SELECT 
                    COUNT(*) FILTER (WHERE name IS NULL OR name = '') as missing_name,
                    COUNT(*) FILTER (WHERE destination_id IS NULL OR destination_id = '') as missing_destination,
                    COUNT(*) FILTER (WHERE category_id IS NULL OR category_id = '') as missing_category
                FROM hotel
            """)).fetchone()
            
            print(f"\n🔍 Data Quality Check:")
            print(f"   Hotels missing name: {missing_data[0]}")
            print(f"   Hotels missing destination: {missing_data[1]}")
            print(f"   Hotels missing category: {missing_data[2]}")
            
            # 5. Get sample of hotel records to verify data
            sample_hotels = conn.execute(text("""
                SELECT h.id, h.name, h.destination_id, d.name as destination_name,
                       h.category_id, pc.name as category_name, h.is_active
                FROM hotel h
                LEFT JOIN destination d ON h.destination_id = d.id
                LEFT JOIN product_category pc ON h.category_id = pc.id
                ORDER BY h.name
                LIMIT 10
            """)).fetchall()
            
            print(f"\n🏨 Sample Hotel Records:")
            for hotel in sample_hotels:
                print(f"   {hotel[1]} (ID: {hotel[0][:8]}...)")
                print(f"      Destination: {hotel[3]} ({hotel[2][:8]}...)")
                print(f"      Category: {hotel[5]} ({hotel[4][:8]}...)")
                print(f"      Active: {hotel[6]}")
            
            # 6. Check for recently created/updated hotels
            recent_hotels = conn.execute(text("""
                SELECT name, created_at, updated_at
                FROM hotel
                WHERE updated_at > NOW() - INTERVAL '1 day'
                ORDER BY updated_at DESC
                LIMIT 10
            """)).fetchall()
            
            print(f"\n🕒 Recently Updated Hotels (last 24h): {len(recent_hotels)}")
            for hotel in recent_hotels[:5]:  # Show first 5
                print(f"   {hotel[0]} - Updated: {hotel[2]}")
            
            # 7. Check for any hotels that might be soft-deleted
            deleted_hotels = conn.execute(text("""
                SELECT COUNT(*) FROM hotel WHERE deleted_at IS NOT NULL
            """)).scalar()
            
            print(f"\n🗑️  Soft-deleted hotels: {deleted_hotels}")
            
            # 8. Analyze the discrepancy
            print(f"\n🎯 Discrepancy Analysis:")
            print(f"   Pipeline reports: 241 records loaded")
            print(f"   Database contains: {hotel_count} records")
            print(f"   Missing records: {241 - hotel_count}")
            
            if 241 - hotel_count > 0:
                print(f"\n❌ There is a discrepancy of {241 - hotel_count} records")
                print("   Possible causes:")
                print("   1. Transaction rollbacks during pipeline execution")
                print("   2. Constraint violations preventing some inserts")
                print("   3. Duplicate records being updated instead of inserted")
                print("   4. Records inserted but later deleted")
                print("   5. Pipeline counting logic error")
            else:
                print(f"\n✅ No discrepancy found")
            
            return hotel_count, recent_runs, duplicates
            
        except Exception as e:
            print(f"❌ Error analyzing database: {e}")
            return None, None, None

def check_pipeline_logs():
    """Check the most recent pipeline logs for clues"""
    print(f"\n🔍 Checking Pipeline Logs")
    print("=" * 40)
    
    engine = create_engine(settings.database_url)
    
    with engine.connect() as conn:
        try:
            # Get the most recent successful run
            latest_run = conn.execute(text("""
                SELECT pr.id, pr.logs, pr.records_loaded
                FROM etl_pipeline_runs pr
                JOIN etl_pipelines p ON pr.pipeline_id = p.id
                WHERE p.name = 'Hotel Pipeline' 
                  AND pr.status = 'completed'
                  AND pr.records_loaded = 241
                ORDER BY pr.started_at DESC
                LIMIT 1
            """)).fetchone()
            
            if latest_run:
                print(f"📋 Latest successful run (ID: {latest_run[0]}) loaded {latest_run[2]} records")
                
                # Parse logs if available
                if latest_run[1]:
                    try:
                        logs = json.loads(latest_run[1])
                        print(f"   Total log entries: {len(logs)}")
                        
                        # Look for error or warning messages
                        errors = [log for log in logs if log.get('level') in ['ERROR', 'WARNING']]
                        if errors:
                            print(f"   ⚠️  Found {len(errors)} error/warning messages:")
                            for error in errors[-5:]:  # Show last 5
                                print(f"      {error.get('level')}: {error.get('message', 'N/A')}")
                        else:
                            print(f"   ✅ No errors or warnings in logs")
                            
                    except json.JSONDecodeError:
                        print(f"   ❌ Could not parse logs")
                else:
                    print(f"   ❌ No logs available")
            else:
                print(f"❌ No recent successful runs found with 241 loaded records")
                
        except Exception as e:
            print(f"❌ Error checking logs: {e}")

def main():
    """Main analysis function"""
    hotel_count, recent_runs, duplicates = analyze_database_state()
    
    if hotel_count is not None:
        check_pipeline_logs()
        
        print(f"\n🎯 Summary:")
        print(f"   Expected: 241 hotel records")
        print(f"   Actual: {hotel_count} hotel records")
        print(f"   Discrepancy: {241 - hotel_count} records")
        
        if 241 - hotel_count == 6:
            print(f"\n🔍 Next Steps to Identify Missing Records:")
            print(f"   1. Run the Hotel Pipeline again with detailed logging")
            print(f"   2. Check for constraint violations in pipeline logs")
            print(f"   3. Verify Salesforce data hasn't changed")
            print(f"   4. Check for transaction rollback issues")

if __name__ == "__main__":
    main()
