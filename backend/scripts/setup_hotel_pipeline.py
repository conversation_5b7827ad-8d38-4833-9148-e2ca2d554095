#!/usr/bin/env python3
"""
Complete setup script for the Hotel Pipeline.
This script:
1. Runs the database migration to create hotel tables
2. Creates the pipeline configuration with field mappings
3. Verifies the complete setup
4. Provides comprehensive status reporting
"""
import sys
import os
import subprocess

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from create_hotel_pipeline import create_hotel_pipeline, verify_hotel_pipeline


def run_database_migration():
    """Run Alembic migration to create hotel tables"""
    print("🗄️  Running Database Migration")
    print("=" * 50)
    
    try:
        # Run alembic upgrade head
        result = subprocess.run(
            ["alembic", "upgrade", "head"],
            cwd=os.path.dirname(os.path.abspath(__file__)),
            capture_output=True,
            text=True,
            check=True
        )
        
        print("✅ Database migration completed successfully")
        if result.stdout:
            print("Migration output:")
            print(result.stdout)
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Database migration failed: {e}")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False
    except Exception as e:
        print(f"❌ Error running migration: {e}")
        return False


def verify_database_schema():
    """Verify that the hotel tables were created correctly"""
    print("\n🔍 Verifying Database Schema")
    print("=" * 50)
    
    try:
        from app.core.database import SessionLocal
        from sqlalchemy import text
        
        db = SessionLocal()
        
        # Check if new tables exist
        tables_to_check = ['hotel', 'product_category']
        existing_tables = []
        
        for table_name in tables_to_check:
            result = db.execute(text(f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = '{table_name}'
                );
            """))
            exists = result.fetchone()[0]
            if exists:
                existing_tables.append(table_name)
        
        missing_tables = set(tables_to_check) - set(existing_tables)
        
        if missing_tables:
            print(f"❌ Missing tables: {missing_tables}")
            return False
        else:
            print("✅ All hotel pipeline tables exist")
        
        # Check if migrated_id was added to destination table
        result = db.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'destination' 
            AND column_name = 'migrated_id'
        """))
        
        migrated_id_exists = result.fetchone() is not None
        
        if migrated_id_exists:
            print("✅ migrated_id field added to destination table")
        else:
            print("❌ migrated_id field missing from destination table")
            return False
        
        # Check table structures
        for table_name in ['hotel', 'product_category']:
            result = db.execute(text(f"""
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = '{table_name}' 
                ORDER BY ordinal_position
            """))
            
            columns = result.fetchall()
            print(f"\n📋 {table_name} table structure:")
            for col_name, data_type, is_nullable in columns:
                nullable = "NULL" if is_nullable == "YES" else "NOT NULL"
                print(f"   {col_name}: {data_type} {nullable}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Error verifying database schema: {e}")
        return False


def verify_pipeline_functionality():
    """Verify that the pipeline can be executed (dry run)"""
    print("\n🧪 Verifying Pipeline Functionality")
    print("=" * 50)
    
    try:
        from app.core.database import SessionLocal
        from app.models.etl_pipeline import ETLPipeline
        from app.etl.pipelines.pipeline_factory import PipelineFactory
        
        db = SessionLocal()
        
        # Find the Hotel Pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Hotel Pipeline").first()
        if not pipeline:
            print("❌ Hotel Pipeline not found")
            return False
        
        # Create pipeline strategy (this validates the configuration)
        factory = PipelineFactory(db)
        try:
            strategy = factory.create_strategy(pipeline)
            print("✅ Pipeline strategy created successfully")
            print(f"   Strategy type: {strategy.get_strategy_name()}")
        except Exception as e:
            print(f"❌ Failed to create pipeline strategy: {e}")
            return False
        
        # Validate pipeline configuration
        is_valid, errors = strategy.validate_configuration(pipeline)
        if is_valid:
            print("✅ Pipeline configuration is valid")
        else:
            print(f"❌ Pipeline configuration errors: {', '.join(errors)}")
            return False
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Error verifying pipeline functionality: {e}")
        return False


def print_setup_summary():
    """Print a comprehensive setup summary"""
    print("\n📊 Hotel Pipeline Setup Summary")
    print("=" * 60)
    
    try:
        from app.core.database import SessionLocal
        from app.models.etl_pipeline import ETLPipeline
        
        db = SessionLocal()
        
        # Get pipeline details
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Hotel Pipeline").first()
        if pipeline:
            print("🏨 Hotel Pipeline Configuration:")
            print(f"   Name: {pipeline.name}")
            print(f"   Source: Salesforce {pipeline.source_object}")
            print(f"   Destination: PostgreSQL {pipeline.destination_table}")
            print(f"   Status: {'Active' if pipeline.is_active else 'Inactive'}")
            print(f"   Created: {pipeline.created_at}")
            
            print("\n📋 Source Fields:")
            for field in pipeline.source_fields or []:
                print(f"   • {field}")
            
            print("\n🎯 Field Mappings:")
            for dest_field, source_field in (pipeline.destination_fields or {}).items():
                print(f"   {source_field} → {dest_field}")
            
            print("\n🗄️  Target Tables:")
            print("   • destination (via Resort__c relationship)")
            print("   • hotel (primary hotel data)")
            print("   • product_category (hotel categories)")
            
            print("\n🔗 Data Relationships:")
            print("   • Hotel.destination_id → Destination.id (string)")
            print("   • Hotel.category_id → ProductCategory.id (string)")
            print("   • Lookup via Resort__c.Id → Destination.migrated_id")
        
        db.close()
        
    except Exception as e:
        print(f"❌ Error generating setup summary: {e}")


if __name__ == "__main__":
    print("🏨 Hotel Pipeline Complete Setup")
    print("=" * 60)
    print("This script will set up the complete Hotel Pipeline infrastructure:")
    print("• Database schema (tables, indexes, relationships)")
    print("• Pipeline configuration (field mappings, transformations)")
    print("• Verification and validation")
    print()
    
    success = True
    
    # Step 1: Run database migration
    if not run_database_migration():
        success = False
    
    # Step 2: Verify database schema
    if success and not verify_database_schema():
        success = False
    
    # Step 3: Create pipeline configuration
    if success and not create_hotel_pipeline():
        success = False
    
    # Step 4: Verify pipeline configuration
    if success and not verify_hotel_pipeline():
        success = False
    
    # Step 5: Verify pipeline functionality
    if success and not verify_pipeline_functionality():
        success = False
    
    # Final summary
    if success:
        print("\n🎉 Hotel Pipeline setup completed successfully!")
        print("=" * 60)
        print("✅ Database migration: Complete")
        print("✅ Schema verification: Complete")
        print("✅ Pipeline configuration: Complete")
        print("✅ Configuration verification: Complete")
        print("✅ Functionality verification: Complete")
        
        print_setup_summary()
        
        print("\n🚀 Next Steps:")
        print("1. Access the web interface to test the pipeline")
        print("2. Configure Salesforce credentials if needed")
        print("3. Run the pipeline to migrate hotel data")
        print("4. Monitor execution logs and results")
        
    else:
        print("\n❌ Hotel Pipeline setup failed!")
        print("Please check the error messages above and resolve any issues.")
        print("You may need to:")
        print("• Check database connectivity")
        print("• Verify Alembic configuration")
        print("• Ensure all dependencies are installed")
        sys.exit(1)
