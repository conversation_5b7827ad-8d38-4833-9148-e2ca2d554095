#!/usr/bin/env python3
"""
Test script for Enhanced Room Type Pipeline with automatic dependency management
and Product Variants system
"""
import sys
import os
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import (
    ETLPipeline, ETLPipelineRun, Destination, Hotel, ProductCategory,
    Product, ProductCategoryProduct, ProductVariant
)


def test_enhanced_pipeline_system():
    """Test the enhanced Room Type Pipeline with dependency management and product variants"""
    print("🚀 TESTING ENHANCED ROOM TYPE PIPELINE")
    print("=" * 70)
    
    db = SessionLocal()
    try:
        # Clear existing data for clean test
        print("🧹 Clearing existing data for clean test...")
        db.query(ProductVariants).delete()
        db.query(ProductCategoryproduct).delete()
        db.query(Product).delete()
        db.query(ProductCategory).delete()
        db.query(Hotel).delete()
        db.query(Destination).delete()
        db.commit()
        
        # Get initial counts
        initial_counts = {
            'destination': db.query(Destination).count(),
            'hotel': db.query(Hotel).count(),
            'categories': db.query(ProductCategory).count(),
            'product': db.query(Product).count(),
            'junction_entries': db.query(ProductCategoryproduct).count(),
            'product_variant': db.query(ProductVariants).count()
        }
        
        print(f"📊 Initial State:")
        for table, count in initial_counts.items():
            print(f"   {table:20}: {count}")
        
        # Get Room Type Pipeline
        room_type_pipeline = db.query(ETLPipeline).filter(
            ETLPipeline.name == "Room Type Pipeline"
        ).first()
        
        if not room_type_pipeline:
            print("❌ Room Type Pipeline not found")
            return False
        
        print(f"\n✅ Found Room Type Pipeline (ID: {room_type_pipeline.id})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during test setup: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()


def execute_enhanced_pipeline():
    """Execute the enhanced Room Type Pipeline via API"""
    print("\n🔄 EXECUTING ENHANCED ROOM TYPE PIPELINE")
    print("=" * 70)
    
    import requests
    
    try:
        # Execute Room Type Pipeline
        print("📡 Triggering Room Type Pipeline execution...")
        response = requests.post("http://localhost:8000/api/v1/pipelines/3/execute")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Pipeline execution started: {result}")
            return True
        else:
            print(f"❌ Failed to start pipeline: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error executing pipeline: {e}")
        return False


def verify_enhanced_results():
    """Verify the results of the enhanced pipeline execution"""
    print("\n🔍 VERIFYING ENHANCED PIPELINE RESULTS")
    print("=" * 70)
    
    db = SessionLocal()
    try:
        # Wait for pipeline to complete (check status)
        print("⏳ Waiting for pipeline completion...")
        import time
        time.sleep(45)  # Wait for pipeline to complete
        
        # Get latest Room Type Pipeline run
        latest_run = db.query(ETLPipelineRun).filter(
            ETLPipelineRun.pipeline_id == 3
        ).order_by(ETLPipelineRun.started_at.desc()).first()
        
        if not latest_run:
            print("❌ No pipeline runs found")
            return False
        
        print(f"📋 Latest Run Status: {latest_run.status}")
        print(f"📊 Records: Extract={latest_run.records_extracted}, Transform={latest_run.records_transformed}, Load={latest_run.records_loaded}")
        
        # Get final counts
        final_counts = {
            'destination': db.query(Destination).count(),
            'hotel': db.query(Hotel).count(),
            'categories': db.query(ProductCategory).count(),
            'product': db.query(Product).count(),
            'junction_entries': db.query(ProductCategoryproduct).count(),
            'product_variant': db.query(ProductVariants).count()
        }
        
        print(f"\n📊 Final State:")
        for table, count in final_counts.items():
            print(f"   {table:20}: {count}")
        
        # Verify dependency management worked
        success_indicators = []
        
        if final_counts['destination'] > 0:
            success_indicators.append("✅ destination populated (Resort Pipeline executed)")
        else:
            success_indicators.append("❌ No destination found")
        
        if final_counts['hotel'] > 0 and final_counts['categories'] > 0:
            success_indicators.append("✅ hotel and categories populated (Hotel Pipeline executed)")
        else:
            success_indicators.append("❌ hotel or categories missing")
        
        if final_counts['product'] > 0:
            success_indicators.append("✅ product populated (Room Type Pipeline executed)")
        else:
            success_indicators.append("❌ No product found")
        
        if final_counts['junction_entries'] > 0:
            success_indicators.append("✅ Junction table populated")
        else:
            success_indicators.append("❌ Junction table empty")
        
        if final_counts['product_variant'] > 0:
            success_indicators.append("✅ Product variants populated (Room__c data)")
        else:
            success_indicators.append("❌ No product variants found")
        
        print(f"\n🎯 Success Indicators:")
        for indicator in success_indicators:
            print(f"   {indicator}")
        
        # Show sample data
        if final_counts['product_variant'] > 0:
            print(f"\n📋 Sample Product Variants:")
            sample_variants = db.query(ProductVariants).limit(5).all()
            for variant in sample_variants:
                product_name = "Unknown"
                if variant.product:
                    product_name = variant.product.title
                
                print(f"   - {variant.name} → Product: {product_name}")
                print(f"     ID: {variant.id}, Connected: {variant.is_connected}")
        
        # Calculate success rate
        success_count = sum(1 for indicator in success_indicators if indicator.startswith("✅"))
        total_count = len(success_indicators)
        success_rate = (success_count / total_count) * 100
        
        print(f"\n📈 Overall Success Rate: {success_rate:.1f}% ({success_count}/{total_count})")
        
        return success_rate >= 80  # 80% success rate threshold
        
    except Exception as e:
        print(f"❌ Error verifying results: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()


def main():
    """Main test function"""
    print("🧪 ENHANCED ROOM TYPE PIPELINE TEST SUITE")
    print("=" * 70)
    
    # Test 1: Setup and validation
    setup_success = test_enhanced_pipeline_system()
    
    if not setup_success:
        print("\n💥 Setup failed - aborting tests")
        return False
    
    # Test 2: Execute enhanced pipeline
    execution_success = execute_enhanced_pipeline()
    
    if not execution_success:
        print("\n💥 Pipeline execution failed - aborting tests")
        return False
    
    # Test 3: Verify results
    verification_success = verify_enhanced_results()
    
    print("\n" + "=" * 70)
    print("📊 ENHANCED PIPELINE TEST RESULTS:")
    print(f"   Setup: {'✅ PASS' if setup_success else '❌ FAIL'}")
    print(f"   Execution: {'✅ PASS' if execution_success else '❌ FAIL'}")
    print(f"   Verification: {'✅ PASS' if verification_success else '❌ FAIL'}")
    
    overall_success = all([setup_success, execution_success, verification_success])
    
    if overall_success:
        print("\n🎉 ALL ENHANCED PIPELINE TESTS PASSED!")
        print("\n✅ Features Verified:")
        print("   - Automatic dependency management (Resort → Hotel → Room Type)")
        print("   - Product variants extraction from Room__c")
        print("   - Junction table population")
        print("   - End-to-end pipeline execution")
        print("\n🚀 Enhanced Room Type Pipeline is production ready!")
    else:
        print("\n💥 SOME ENHANCED PIPELINE TESTS FAILED")
        print("   Please check the logs and fix issues before proceeding")
    
    return overall_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
