#!/usr/bin/env python3
"""
Create Hotel Pipeline configuration script.
This script creates the Hotel Pipeline configuration in the database with proper field mappings
and transformation rules for extracting from Salesforce Hotel__c and Resort__c objects.
"""
import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import Session<PERSON>ocal
from app.models.etl_pipeline import ETLPipeline
from sqlalchemy.exc import IntegrityError


def create_hotel_pipeline():
    """Create the Hotel Pipeline configuration"""
    print("🏨 Creating Hotel Pipeline Configuration")
    print("=" * 50)
    
    db = SessionLocal()
    
    try:
        # Check if Hotel Pipeline already exists
        existing_pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Hotel Pipeline").first()
        if existing_pipeline:
            print("⚠️  Hotel Pipeline already exists in database")
            print(f"   Pipeline ID: {existing_pipeline.id}")
            print("   Use update_hotel_pipeline.py to modify existing configuration")
            return False
        
        # Hotel Pipeline configuration
        # This pipeline extracts from both Resort__c and Hotel__c objects
        # and loads to destination, hotel, and product_category tables
        
        # Source fields from Hotel__c object
        hotel_source_fields = [
            "Id",           # Hotel ID for migrated_id
            "Name",         # Hotel name
            "Resort__c"     # Foreign key to Resort__c
        ]
        
        # Destination field mappings for hotel table
        hotel_destination_fields = {
            "migrated_id": "Id",
            "name": "Name",
            "resort_migrated_id": "Resort__c"  # Used for destination lookup
        }
        
        # Transformation configuration
        hotel_transformation_config = {
            "clean_nulls": False,
            "trim_whitespace": True,
            "field_transformations": {
                # No special transformations needed for hotel data
                # The foreign key lookup is handled in the loading logic
            }
        }
        
        # Create the Hotel Pipeline
        hotel_pipeline = ETLPipeline(
            name="Hotel Pipeline",
            description="Extract hotel data from Salesforce Hotel__c object and load to PostgreSQL hotel and product_category tables with destination relationships",
            source_type="salesforce",
            source_object="Hotel__c",
            source_fields=hotel_source_fields,
            destination_type="postgresql",
            destination_table="hotel",  # Primary destination table
            destination_fields=hotel_destination_fields,
            transformation_config=hotel_transformation_config,
            is_active=True
        )
        
        db.add(hotel_pipeline)
        db.commit()
        db.refresh(hotel_pipeline)
        
        print("✅ Hotel Pipeline created successfully!")
        print(f"   Pipeline ID: {hotel_pipeline.id}")
        print(f"   Name: {hotel_pipeline.name}")
        print(f"   Source Object: {hotel_pipeline.source_object}")
        print(f"   Destination Table: {hotel_pipeline.destination_table}")
        print(f"   Source Fields: {hotel_pipeline.source_fields}")
        
        print("\n📋 Field Mappings:")
        for dest_field, source_field in hotel_destination_fields.items():
            print(f"   {source_field} → {dest_field}")
        
        print("\n📝 Pipeline Details:")
        print("   • Extracts from Salesforce Hotel__c object")
        print("   • Includes Resort__c foreign key relationship")
        print("   • Loads to both hotel and product_category tables")
        print("   • Performs destination lookup via Resort__c.Id")
        print("   • Maintains referential integrity across all tables")
        
        return True
        
    except IntegrityError as e:
        db.rollback()
        print(f"❌ Database integrity error: {e}")
        print("   This usually means the pipeline name already exists")
        return False
    except Exception as e:
        db.rollback()
        print(f"❌ Error creating Hotel Pipeline: {e}")
        return False
    finally:
        db.close()


def verify_hotel_pipeline():
    """Verify the Hotel Pipeline configuration"""
    print("\n🔍 Verifying Hotel Pipeline Configuration")
    print("=" * 50)
    
    db = SessionLocal()
    
    try:
        # Find the Hotel Pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Hotel Pipeline").first()
        if not pipeline:
            print("❌ Hotel Pipeline not found in database")
            return False
        
        print("✅ Hotel Pipeline found and verified")
        print(f"   ID: {pipeline.id}")
        print(f"   Name: {pipeline.name}")
        print(f"   Source Type: {pipeline.source_type}")
        print(f"   Source Object: {pipeline.source_object}")
        print(f"   Destination Type: {pipeline.destination_type}")
        print(f"   Destination Table: {pipeline.destination_table}")
        print(f"   Is Active: {pipeline.is_active}")
        print(f"   Created: {pipeline.created_at}")
        
        # Verify source fields
        expected_fields = ["Id", "Name", "Resort__c"]
        actual_fields = pipeline.source_fields or []
        missing_fields = set(expected_fields) - set(actual_fields)
        
        if missing_fields:
            print(f"⚠️  Missing source fields: {missing_fields}")
        else:
            print("✅ All required source fields present")
        
        # Verify destination fields
        expected_dest_fields = ["migrated_id", "name", "resort_migrated_id"]
        actual_dest_fields = list((pipeline.destination_fields or {}).keys())
        missing_dest_fields = set(expected_dest_fields) - set(actual_dest_fields)
        
        if missing_dest_fields:
            print(f"⚠️  Missing destination fields: {missing_dest_fields}")
        else:
            print("✅ All required destination field mappings present")
        
        return True
        
    except Exception as e:
        print(f"❌ Error verifying Hotel Pipeline: {e}")
        return False
    finally:
        db.close()


if __name__ == "__main__":
    print("🏨 Hotel Pipeline Configuration Setup")
    print("=" * 60)
    
    # Create the pipeline
    success = create_hotel_pipeline()
    
    if success:
        # Verify the configuration
        verify_hotel_pipeline()
        
        print("\n🎉 Hotel Pipeline setup completed successfully!")
        print("=" * 60)
        print("✅ Pipeline configuration: Complete")
        print("✅ Field mappings: Complete")
        print("✅ Verification: Complete")
        
        print("\n📋 Next Steps:")
        print("1. Run database migration: alembic upgrade head")
        print("2. Test the pipeline through the web interface")
        print("3. Monitor pipeline execution logs")
        
        print("\n💡 Pipeline Features:")
        print("• Multi-object extraction (Hotel__c with Resort__c relationships)")
        print("• Multi-table loading (hotel + product_category)")
        print("• Foreign key resolution (destination lookup)")
        print("• Data consistency and referential integrity")
        
    else:
        print("\n❌ Hotel Pipeline setup failed!")
        print("Please check the error messages above and try again.")
        sys.exit(1)
