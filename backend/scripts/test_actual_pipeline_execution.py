#!/usr/bin/env python3
"""
Test the actual Resort Pipeline execution end-to-end.
"""
import os
import sys
import traceback
from datetime import datetime

def test_actual_pipeline_execution():
    """Test the actual pipeline execution."""
    print("🚀 Testing actual Resort Pipeline execution...")
    
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app.core.database import SessionLocal
        from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun, Destination
        from app.services.etl_pipeline_service import ETLPipelineService
        
        db = SessionLocal()
        try:
            # Get Resort Pipeline
            resort_pipeline = db.query(ETLPipeline).filter(
                ETLPipeline.name == "Resort Pipeline"
            ).first()
            
            if not resort_pipeline:
                print("   ❌ Resort Pipeline not found")
                return False
            
            print(f"   ✅ Resort Pipeline found (ID: {resort_pipeline.id})")
            
            # Check current destination count
            initial_count = db.query(Destination).count()
            print(f"   📊 Initial destination count: {initial_count}")
            
            # Create pipeline service
            pipeline_service = ETLPipelineService(db)
            
            # Execute pipeline with limited records for testing
            print("   🔄 Executing Resort Pipeline (limited to 5 records for testing)...")
            
            # Temporarily modify the pipeline to limit records for testing
            original_source_fields = resort_pipeline.source_fields.copy() if resort_pipeline.source_fields else []
            
            # Execute the pipeline
            run = pipeline_service.execute_pipeline(resort_pipeline.id)
            
            print(f"   ✅ Pipeline execution started (Run ID: {run.id})")
            
            # Wait for completion (check status periodically)
            import time
            max_wait_time = 120  # 2 minutes max
            wait_interval = 5    # Check every 5 seconds
            elapsed_time = 0
            
            while elapsed_time < max_wait_time:
                db.refresh(run)
                print(f"   ⏳ Status: {run.status} | Extracted: {run.records_extracted} | Transformed: {run.records_transformed} | Loaded: {run.records_loaded}")
                
                if run.status in ['completed', 'failed', 'cancelled']:
                    break
                
                time.sleep(wait_interval)
                elapsed_time += wait_interval
            
            # Final status check
            db.refresh(run)
            print(f"\n   📊 Final Pipeline Results:")
            print(f"      Status: {run.status}")
            print(f"      Records Extracted: {run.records_extracted}")
            print(f"      Records Transformed: {run.records_transformed}")
            print(f"      Records Loaded: {run.records_loaded}")
            print(f"      Duration: {elapsed_time} seconds")
            
            if run.error_message:
                print(f"      Error: {run.error_message}")
            
            # Check if any records were loaded
            final_count = db.query(Destination).count()
            new_records = final_count - initial_count
            print(f"      New destination records: {new_records}")
            
            # Show logs if available
            if run.logs:
                print(f"\n   📋 Pipeline Logs (last 5 entries):")
                for log_entry in run.logs[-5:]:
                    timestamp = log_entry.get('timestamp', 'N/A')
                    level = log_entry.get('level', 'INFO')
                    stage = log_entry.get('stage', 'unknown')
                    message = log_entry.get('message', '')
                    print(f"      [{timestamp}] {level} [{stage}] {message}")
            
            # Show sample new records if any were created
            if new_records > 0:
                print(f"\n   📋 Sample new destination records:")
                new_destinations = db.query(Destination).order_by(Destination.created_at.desc()).limit(3).all()
                for i, dest in enumerate(new_destinations, 1):
                    print(f"      Record {i}:")
                    print(f"        ID: {dest.id}")
                    print(f"        Name: {dest.name}")
                    print(f"        Country: {dest.country}")
                    print(f"        Currency Code: {dest.currency}")
                    if hasattr(dest, 'metadata_') and dest.metadata_:
                        print(f"        ETL Metadata: {dest.metadata_}")
            
            # Determine success
            if run.status == 'completed' and run.records_loaded and run.records_loaded > 0:
                print(f"\n   🎉 Pipeline execution successful!")
                return True
            elif run.status == 'completed' and run.records_loaded == 0:
                print(f"\n   ⚠️  Pipeline completed but no records were loaded")
                print(f"      This might indicate no new data or filtering issues")
                return False
            else:
                print(f"\n   ❌ Pipeline execution failed or incomplete")
                return False
            
        except Exception as execution_error:
            print(f"   ❌ Pipeline execution error: {execution_error}")
            traceback.print_exc()
            return False
        finally:
            db.close()
            
    except Exception as e:
        print(f"   ❌ Error testing pipeline execution: {e}")
        traceback.print_exc()
        return False

def test_pipeline_with_dry_run():
    """Test pipeline with a dry run approach."""
    print("\n🔍 Testing pipeline with dry run approach...")
    
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app.core.database import SessionLocal
        from app.models.etl_pipeline import ETLPipeline
        from app.etl.pipelines.pipeline_strategies.salesforce_to_postgresql_strategy import SalesforceToPostgreSQLStrategy
        from app.etl.extractors.salesforce_extractor import SalesforceExtractor
        
        db = SessionLocal()
        try:
            # Get Resort Pipeline
            resort_pipeline = db.query(ETLPipeline).filter(
                ETLPipeline.name == "Resort Pipeline"
            ).first()
            
            if not resort_pipeline:
                print("   ❌ Resort Pipeline not found")
                return False
            
            print("   ✅ Resort Pipeline found")
            
            # Create extractor and strategy
            extractor = SalesforceExtractor()
            strategy = SalesforceToPostgreSQLStrategy(db)
            
            # Test authentication
            if not extractor.authenticate():
                print("   ❌ Salesforce authentication failed")
                return False
            
            print("   ✅ Salesforce authentication successful")
            
            # Extract limited data
            print("   📥 Extracting data from Salesforce...")
            extracted_data = extractor.extract_records(
                object_name=resort_pipeline.source_object,
                fields=resort_pipeline.source_fields or [],
                limit=3  # Just 3 records for testing
            )
            
            print(f"   ✅ Extracted {len(extracted_data)} records")
            
            if extracted_data:
                print("   📋 Sample extracted data:")
                for i, record in enumerate(extracted_data[:2], 1):
                    print(f"      Record {i}: ID={record.get('Id')}, Name={record.get('Name')}, Country={record.get('Country__c')}")
            
            # Create a mock run for transformation testing
            from app.models.etl_pipeline import ETLPipelineRun
            mock_run = ETLPipelineRun(
                pipeline_id=resort_pipeline.id,
                status="testing",
                started_at=datetime.utcnow()
            )
            
            # Transform data
            print("   🔄 Transforming data...")
            transformed_data = strategy._transform_data(resort_pipeline, mock_run, extracted_data)
            
            print(f"   ✅ Transformed {len(transformed_data)} records")
            
            if transformed_data:
                print("   📋 Sample transformed data:")
                for i, record in enumerate(transformed_data[:2], 1):
                    print(f"      Record {i}:")
                    print(f"        Name: {record.get('name')}")
                    print(f"        Country: {record.get('country')}")
                    print(f"        Currency: {record.get('currency')}")
                    print(f"        Migrated ID: {record.get('migrated_id')}")
            
            print("   🎉 Dry run successful - pipeline should work!")
            return True
            
        except Exception as dry_run_error:
            print(f"   ❌ Dry run failed: {dry_run_error}")
            traceback.print_exc()
            return False
        finally:
            db.close()
            
    except Exception as e:
        print(f"   ❌ Error in dry run: {e}")
        traceback.print_exc()
        return False

def main():
    """Run pipeline execution tests."""
    print("🚀 Testing Resort Pipeline Execution")
    print("=" * 60)
    
    tests = [
        ("Dry Run Test", test_pipeline_with_dry_run),
        ("Actual Pipeline Execution", test_actual_pipeline_execution)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! Resort Pipeline is working correctly.")
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
    
    return all_passed

if __name__ == "__main__":
    main()
