#!/usr/bin/env python3
"""
Check the current schema of the destination table in powderbyrne database.
"""
import os
import sys

def check_destination_schema():
    """Check the current schema of the destination table."""
    print("🔍 Checking destination table schema in powderbyrne database...")

    try:
        # Add current directory to Python path
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

        from app.core.database import SessionLocal
        from sqlalchemy import text

        # Use SQLAlchemy connection which works
        db = SessionLocal()

        # Check if destination table exists
        result = db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'destination'
            );
        """))

        table_exists = result.scalar()

        if not table_exists:
            print("❌ destination table does not exist")
            return

        print("✅ destination table exists")

        # Get table schema
        result = db.execute(text("""
            SELECT
                column_name,
                data_type,
                is_nullable,
                column_default,
                character_maximum_length
            FROM information_schema.columns
            WHERE table_schema = 'public'
            AND table_name = 'destination'
            ORDER BY ordinal_position;
        """))

        columns = result.fetchall()
        
        print("\n📋 Current destination table schema:")
        print("   Column Name          | Data Type    | Nullable | Default | Max Length")
        print("   " + "-" * 70)
        
        for col in columns:
            col_name, data_type, nullable, default, max_len = col
            nullable_str = "YES" if nullable == "YES" else "NO"
            default_str = str(default) if default else "None"
            max_len_str = str(max_len) if max_len else "N/A"
            
            print(f"   {col_name:<20} | {data_type:<12} | {nullable_str:<8} | {default_str:<7} | {max_len_str}")
        
        # Get sample data
        result = db.execute(text("SELECT COUNT(*) FROM destination"))
        count = result.scalar()
        print(f"\n📊 Total records: {count}")

        if count > 0:
            result = db.execute(text("SELECT * FROM destination LIMIT 3"))
            sample_data = result.fetchall()

            # Get column names for display
            result = db.execute(text("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_schema = 'public'
                AND table_name = 'destination'
                ORDER BY ordinal_position;
            """))
            column_names = [row[0] for row in result.fetchall()]
            
            print("\n📋 Sample data:")
            for i, row in enumerate(sample_data, 1):
                print(f"   Record {i}:")
                for col_name, value in zip(column_names, row):
                    print(f"     {col_name}: {value}")
                print()
        
        # Check indexes
        result = db.execute(text("""
            SELECT
                indexname,
                indexdef
            FROM pg_indexes
            WHERE tablename = 'destination'
            AND schemaname = 'public';
        """))

        indexes = result.fetchall()

        if indexes:
            print("🔍 Indexes:")
            for idx_name, idx_def in indexes:
                print(f"   {idx_name}: {idx_def}")
        else:
            print("🔍 No indexes found")

        db.close()
        
        print("\n" + "=" * 70)
        print("✅ Schema check completed successfully!")
        
    except Exception as e:
        print(f"❌ Error checking schema: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_destination_schema()
