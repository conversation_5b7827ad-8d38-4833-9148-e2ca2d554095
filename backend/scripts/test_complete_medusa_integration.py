#!/usr/bin/env python3
"""
Complete test of Medusa integration - extraction, transformation, and loading
"""
import sys
import os
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun
from app.etl.extractors.salesforce_extractor import SalesforceExtractor
from app.etl.pipelines.pipeline_strategies.salesforce_to_postgresql_strategy import SalesforceToPostgreSQLStrategy
from sqlalchemy import text


def test_complete_room_type_pipeline():
    """Test complete Room Type Pipeline with Medusa integration"""
    print("🧪 TESTING COMPLETE ROOM TYPE PIPELINE (MEDUSA INTEGRATION)")
    print("=" * 70)
    
    db = SessionLocal()
    try:
        # Get Room Type Pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Room Type Pipeline").first()
        if not pipeline:
            print("❌ Room Type Pipeline not found")
            return False
        
        print(f"✅ Found pipeline: {pipeline.name}")
        print(f"   Destination table: {pipeline.destination_table}")
        
        # Create pipeline run
        run = ETLPipelineRun(
            pipeline_id=pipeline.id,
            status="running",
            logs=[]
        )
        db.add(run)
        db.commit()
        db.refresh(run)
        
        print(f"✅ Created pipeline run: {run.id}")
        
        # Initialize strategy
        strategy = SalesforceToPostgreSQLStrategy(db)
        
        # Step 1: Extract data
        print("\n🔄 Step 1: Extracting data from Salesforce...")
        try:
            data = strategy._extract_data(pipeline, run)
            print(f"✅ Extracted {len(data)} records")
            
            if len(data) == 0:
                print("⚠️ No data extracted - skipping remaining steps")
                return False
                
        except Exception as e:
            print(f"❌ Extraction failed: {e}")
            return False
        
        # Step 2: Transform data
        print("\n🔄 Step 2: Transforming data for Medusa...")
        try:
            transformed_data = strategy._transform_data(pipeline, run, data)
            print(f"✅ Transformed {len(transformed_data)} records")
            
            if transformed_data:
                sample = transformed_data[0]
                print("Sample transformed record:")
                for key, value in sample.items():
                    if key == "metadata" and isinstance(value, dict):
                        print(f"  {key}: (metadata with {len(value)} fields)")
                    else:
                        print(f"  {key}: {str(value)[:100]}...")
                        
        except Exception as e:
            print(f"❌ Transformation failed: {e}")
            return False
        
        # Step 3: Load data to Medusa
        print("\n🔄 Step 3: Loading data to Medusa product table...")
        try:
            loaded_count = strategy._load_to_medusa_product(run, transformed_data)
            print(f"✅ Loaded {loaded_count} records to Medusa product table")
            
            # Verify data in Medusa table
            result = db.execute(text("""
                SELECT COUNT(*) FROM product 
                WHERE external_id IS NOT NULL 
                AND metadata->>'source_system' = 'salesforce'
            """))
            sf_product_count = result.scalar()
            print(f"✅ Verified {sf_product_count} Salesforce products in Medusa table")
            
            # Update run status
            run.status = "completed"
            run.completed_at = datetime.utcnow()
            run.records_extracted = len(data)
            run.records_transformed = len(transformed_data)
            run.records_loaded = loaded_count
            db.commit()
            
            return loaded_count > 0
            
        except Exception as e:
            print(f"❌ Loading failed: {e}")
            import traceback
            traceback.print_exc()
            
            # Update run status
            run.status = "failed"
            run.completed_at = datetime.utcnow()
            run.error_message = str(e)
            db.commit()
            
            return False
            
    finally:
        db.close()


def test_complete_room_pipeline():
    """Test complete Room Pipeline with Medusa integration"""
    print("\n🧪 TESTING COMPLETE ROOM PIPELINE (MEDUSA INTEGRATION)")
    print("=" * 70)
    
    db = SessionLocal()
    try:
        # Get Room Pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Room Pipeline").first()
        if not pipeline:
            print("❌ Room Pipeline not found")
            return False
        
        print(f"✅ Found pipeline: {pipeline.name}")
        print(f"   Destination table: {pipeline.destination_table}")
        
        # Create pipeline run
        run = ETLPipelineRun(
            pipeline_id=pipeline.id,
            status="running",
            logs=[]
        )
        db.add(run)
        db.commit()
        db.refresh(run)
        
        print(f"✅ Created pipeline run: {run.id}")
        
        # Initialize strategy
        strategy = SalesforceToPostgreSQLStrategy(db)
        
        # Step 1: Extract data
        print("\n🔄 Step 1: Extracting data from Salesforce...")
        try:
            data = strategy._extract_data(pipeline, run)
            print(f"✅ Extracted {len(data)} records")
            
            if len(data) == 0:
                print("⚠️ No data extracted - skipping remaining steps")
                return False
                
        except Exception as e:
            print(f"❌ Extraction failed: {e}")
            return False
        
        # Step 2: Transform data
        print("\n🔄 Step 2: Transforming data for Medusa...")
        try:
            transformed_data = strategy._transform_data(pipeline, run, data)
            print(f"✅ Transformed {len(transformed_data)} records")
            
            if transformed_data:
                sample = transformed_data[0]
                print("Sample transformed record:")
                for key, value in sample.items():
                    if key == "metadata" and isinstance(value, dict):
                        print(f"  {key}: (metadata with {len(value)} fields)")
                    else:
                        print(f"  {key}: {str(value)[:100]}...")
                        
        except Exception as e:
            print(f"❌ Transformation failed: {e}")
            return False
        
        # Step 3: Load data to Medusa
        print("\n🔄 Step 3: Loading data to Medusa product_variant table...")
        try:
            loaded_count = strategy._load_to_medusa_product_variant(run, transformed_data)
            print(f"✅ Loaded {loaded_count} records to Medusa product_variant table")
            
            # Verify data in Medusa table
            result = db.execute(text("""
                SELECT COUNT(*) FROM product_variant 
                WHERE metadata IS NOT NULL 
                AND metadata->>'source_system' = 'salesforce'
            """))
            sf_variant_count = result.scalar()
            print(f"✅ Verified {sf_variant_count} Salesforce product variants in Medusa table")
            
            # Update run status
            run.status = "completed"
            run.completed_at = datetime.utcnow()
            run.records_extracted = len(data)
            run.records_transformed = len(transformed_data)
            run.records_loaded = loaded_count
            db.commit()
            
            return loaded_count > 0
            
        except Exception as e:
            print(f"❌ Loading failed: {e}")
            import traceback
            traceback.print_exc()
            
            # Update run status
            run.status = "failed"
            run.completed_at = datetime.utcnow()
            run.error_message = str(e)
            db.commit()
            
            return False
            
    finally:
        db.close()


def verify_final_medusa_integration():
    """Verify the final Medusa integration results"""
    print("\n🔍 VERIFYING FINAL MEDUSA INTEGRATION")
    print("=" * 70)
    
    db = SessionLocal()
    try:
        # Check Salesforce products in Medusa
        result = db.execute(text("""
            SELECT COUNT(*) FROM product 
            WHERE external_id IS NOT NULL 
            AND metadata->>'source_system' = 'salesforce'
        """))
        sf_products = result.scalar()
        
        # Check Salesforce product variants in Medusa
        result = db.execute(text("""
            SELECT COUNT(*) FROM product_variant 
            WHERE metadata IS NOT NULL 
            AND metadata->>'source_system' = 'salesforce'
        """))
        sf_variants = result.scalar()
        
        print(f"📊 FINAL RESULTS:")
        print(f"   Salesforce products in Medusa: {sf_products}")
        print(f"   Salesforce product variants in Medusa: {sf_variants}")
        
        if sf_products > 0:
            result = db.execute(text("""
                SELECT title, external_id, status, metadata->>'hotel_id' as hotel_id
                FROM product 
                WHERE metadata->>'source_system' = 'salesforce'
                ORDER BY created_at DESC 
                LIMIT 3
            """))
            products = result.fetchall()
            print(f"\n   Recent Salesforce products:")
            for product in products:
                print(f"     - {product[0]} (SF ID: {product[1]}, Hotel: {product[3]})")
        
        if sf_variants > 0:
            result = db.execute(text("""
                SELECT title, metadata->>'salesforce_id' as sf_id, product_id
                FROM product_variant 
                WHERE metadata->>'source_system' = 'salesforce'
                ORDER BY created_at DESC 
                LIMIT 3
            """))
            variants = result.fetchall()
            print(f"\n   Recent Salesforce product variants:")
            for variant in variants:
                print(f"     - {variant[0]} (SF ID: {variant[1]}, Product: {variant[2]})")
        
        return sf_products > 0 and sf_variants > 0
        
    except Exception as e:
        print(f"❌ Error verifying final integration: {e}")
        return False
    finally:
        db.close()


if __name__ == "__main__":
    print("🚀 COMPLETE MEDUSA INTEGRATION TEST")
    print("=" * 80)
    
    room_type_ok = test_complete_room_type_pipeline()
    room_ok = test_complete_room_pipeline()
    final_ok = verify_final_medusa_integration()
    
    print(f"\n🎯 COMPLETE INTEGRATION SUMMARY:")
    print(f"   Room Type Pipeline (Salesforce → Medusa product): {'✅ OK' if room_type_ok else '❌ FAILED'}")
    print(f"   Room Pipeline (Salesforce → Medusa product_variant): {'✅ OK' if room_ok else '❌ FAILED'}")
    print(f"   Final Medusa Integration: {'✅ OK' if final_ok else '❌ FAILED'}")
    
    if room_type_ok and room_ok and final_ok:
        print("\n🎉 MEDUSA INTEGRATION COMPLETE SUCCESS!")
        print("   ✅ Salesforce Room_Type__c data → Medusa product table")
        print("   ✅ Salesforce Room__c data → Medusa product_variant table")
        print("   ✅ Field mappings working correctly")
        print("   ✅ Metadata preservation working")
        print("   ✅ Data persistence verified")
    else:
        print("\n❌ MEDUSA INTEGRATION ISSUES DETECTED")
        print("   Check the individual test results above for details.")
