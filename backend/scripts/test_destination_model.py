#!/usr/bin/env python3
"""
Test the updated Destination model with powderbyrne database schema.
"""
import os
import sys

def test_destination_model():
    """Test the Destination model."""
    print("🔍 Testing updated Destination model...")
    
    try:
        # Add current directory to Python path
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app.core.database import SessionLocal
        from app.models.etl_pipeline import Destination
        
        print("✅ Successfully imported Destination model")
        
        # Test database connection and query
        db = SessionLocal()
        try:
            # Query existing destinations
            destinations = db.query(Destination).limit(3).all()
            print(f"✅ Successfully queried Destination model: {len(destinations)} records found")
            
            if destinations:
                print("📋 Sample Destination records:")
                for dest in destinations:
                    print(f"   - ID: {dest.id}")
                    print(f"     Name: {dest.name}")
                    print(f"     Country: {dest.country}")
                    print(f"     Handle: {dest.handle}")
                    print(f"     Currency Code: {dest.currency}")
                    print(f"     Is Active: {dest.is_active}")
                    if hasattr(dest, 'metadata_') and dest.metadata_:
                        print(f"     Metadata: {dest.metadata_}")
                    print()
            
            print("✅ Destination model is working correctly with powderbyrne database!")
            return True
            
        except Exception as e:
            print(f"❌ Error querying Destination model: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Error importing Destination model: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_etl_pipeline_model():
    """Test the ETL Pipeline model."""
    print("\n🔍 Testing ETL Pipeline model...")
    
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app.core.database import SessionLocal
        from app.models.etl_pipeline import ETLPipeline
        
        print("✅ Successfully imported ETLPipeline model")
        
        db = SessionLocal()
        try:
            # Query existing pipelines
            pipelines = db.query(ETLPipeline).filter(
                ETLPipeline.destination_table == "destination"
            ).all()
            print(f"✅ Found {len(pipelines)} ETL pipelines targeting destination table")
            
            for pipeline in pipelines:
                print(f"   - Pipeline: {pipeline.name}")
                print(f"     Source: {pipeline.source_object}")
                print(f"     Destination: {pipeline.destination_table}")
                print(f"     Active: {pipeline.is_active}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error querying ETL Pipeline model: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Error importing ETL Pipeline model: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 Testing Updated Models for powderbyrne Database")
    print("=" * 60)
    
    tests = [
        test_destination_model,
        test_etl_pipeline_model
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            import traceback
            traceback.print_exc()
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    
    test_names = [
        "Destination Model",
        "ETL Pipeline Model"
    ]
    
    all_passed = True
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {i+1}. {name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! Your models are correctly configured for the powderbyrne database.")
        print("\n📋 Next Steps:")
        print("   1. Test the Resort Pipeline execution")
        print("   2. Verify data loading works correctly")
        print("   3. Monitor pipeline execution through the web interface")
    else:
        print("\n⚠️  Some tests failed. Please review the errors above.")
    
    return all_passed

if __name__ == "__main__":
    main()
