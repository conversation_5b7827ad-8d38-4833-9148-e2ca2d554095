#!/usr/bin/env python3
"""
Test the Medusa transformation logic directly
"""
import sys
import os
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ET<PERSON>ipeline, ETLPipelineRun
from app.etl.extractors.salesforce_extractor import SalesforceExtractor
from app.etl.pipelines.pipeline_strategies.salesforce_to_postgresql_strategy import SalesforceToPostgreSQLStrategy


def test_room_type_transformation():
    """Test Room Type Pipeline transformation with Medusa mappings"""
    print("🧪 TESTING ROOM TYPE TRANSFORMATION (MEDUSA MAPPINGS)")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Get Room Type Pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Room Type Pipeline").first()
        if not pipeline:
            print("❌ Room Type Pipeline not found")
            return False
        
        print(f"✅ Found pipeline: {pipeline.name}")
        print(f"   Destination table: {pipeline.destination_table}")
        print(f"   Field mappings: {pipeline.destination_fields}")
        
        # Extract sample data
        extractor = SalesforceExtractor()
        if not extractor.authenticate():
            print("❌ Salesforce authentication failed")
            return False
        
        print("✅ Salesforce authenticated")
        
        data = extractor.extract_records(
            object_name=pipeline.source_object,
            fields=pipeline.source_fields or [],
            limit=2
        )
        
        print(f"✅ Extracted {len(data)} records for transformation test")
        
        if data:
            print("Sample extracted record:")
            sample = data[0]
            for key, value in sample.items():
                print(f"  {key}: {value}")
        
        # Create test run
        run = ETLPipelineRun(
            pipeline_id=pipeline.id,
            status="running",
            logs=[]
        )
        db.add(run)
        db.commit()
        db.refresh(run)
        
        print(f"✅ Created test run: {run.id}")
        
        # Test transformation
        strategy = SalesforceToPostgreSQLStrategy(db)
        
        print("\n🔄 Testing transformation...")
        try:
            transformed_data = strategy._transform_data(pipeline, run, data)
            print(f"✅ Transformed {len(transformed_data)} records")
            
            if transformed_data:
                print("\nSample transformed record:")
                sample = transformed_data[0]
                for key, value in sample.items():
                    if key == "metadata" and isinstance(value, dict):
                        print(f"  {key}:")
                        for meta_key, meta_value in value.items():
                            print(f"    {meta_key}: {meta_value}")
                    else:
                        print(f"  {key}: {value}")
                
                # Update run status
                run.status = "completed"
                run.completed_at = datetime.utcnow()
                db.commit()
                
                return True
            else:
                print("⚠️ No data transformed")
                return False
                
        except Exception as e:
            print(f"❌ Error during transformation: {e}")
            import traceback
            traceback.print_exc()
            
            # Update run status
            run.status = "failed"
            run.completed_at = datetime.utcnow()
            run.error_message = str(e)
            db.commit()
            
            return False
            
    finally:
        db.close()


def test_room_transformation():
    """Test Room Pipeline transformation with Medusa mappings"""
    print("\n🧪 TESTING ROOM TRANSFORMATION (MEDUSA MAPPINGS)")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Get Room Pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Room Pipeline").first()
        if not pipeline:
            print("❌ Room Pipeline not found")
            return False
        
        print(f"✅ Found pipeline: {pipeline.name}")
        print(f"   Destination table: {pipeline.destination_table}")
        print(f"   Field mappings: {pipeline.destination_fields}")
        
        # Extract sample data
        extractor = SalesforceExtractor()
        if not extractor.authenticate():
            print("❌ Salesforce authentication failed")
            return False
        
        print("✅ Salesforce authenticated")
        
        data = extractor.extract_records(
            object_name=pipeline.source_object,
            fields=pipeline.source_fields or [],
            limit=2
        )
        
        print(f"✅ Extracted {len(data)} records for transformation test")
        
        if data:
            print("Sample extracted record:")
            sample = data[0]
            for key, value in sample.items():
                print(f"  {key}: {value}")
        
        # Create test run
        run = ETLPipelineRun(
            pipeline_id=pipeline.id,
            status="running",
            logs=[]
        )
        db.add(run)
        db.commit()
        db.refresh(run)
        
        print(f"✅ Created test run: {run.id}")
        
        # Test transformation
        strategy = SalesforceToPostgreSQLStrategy(db)
        
        print("\n🔄 Testing transformation...")
        try:
            transformed_data = strategy._transform_data(pipeline, run, data)
            print(f"✅ Transformed {len(transformed_data)} records")
            
            if transformed_data:
                print("\nSample transformed record:")
                sample = transformed_data[0]
                for key, value in sample.items():
                    if key == "metadata" and isinstance(value, dict):
                        print(f"  {key}:")
                        for meta_key, meta_value in value.items():
                            print(f"    {meta_key}: {meta_value}")
                    else:
                        print(f"  {key}: {value}")
                
                # Update run status
                run.status = "completed"
                run.completed_at = datetime.utcnow()
                db.commit()
                
                return True
            else:
                print("⚠️ No data transformed")
                return False
                
        except Exception as e:
            print(f"❌ Error during transformation: {e}")
            import traceback
            traceback.print_exc()
            
            # Update run status
            run.status = "failed"
            run.completed_at = datetime.utcnow()
            run.error_message = str(e)
            db.commit()
            
            return False
            
    finally:
        db.close()


if __name__ == "__main__":
    print("🚀 TESTING MEDUSA TRANSFORMATION LOGIC")
    print("=" * 70)
    
    room_type_ok = test_room_type_transformation()
    room_ok = test_room_transformation()
    
    print(f"\n🎯 TRANSFORMATION SUMMARY:")
    print(f"   Room Type Pipeline transformation: {'✅ OK' if room_type_ok else '❌ FAILED'}")
    print(f"   Room Pipeline transformation: {'✅ OK' if room_ok else '❌ FAILED'}")
    
    if room_type_ok and room_ok:
        print("\n✅ MEDUSA TRANSFORMATION SUCCESS!")
        print("   Both pipelines can transform data for Medusa tables.")
    else:
        print("\n❌ MEDUSA TRANSFORMATION ISSUES")
        print("   Check the individual test results above for details.")
