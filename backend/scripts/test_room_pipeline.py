#!/usr/bin/env python3
"""
Test script for the dedicated Room Pipeline
"""
import sys
import os
import time
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import (
    ETLPipeline, ETLPipelineRun, Product, ProductVariants
)


def test_room_pipeline_setup():
    """Test Room Pipeline configuration and prerequisites"""
    print("🔍 TESTING ROOM PIPELINE SETUP")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Check Room Pipeline exists
        room_pipeline = db.query(ETLPipeline).filter(
            ETLPipeline.name == "Room Pipeline"
        ).first()
        
        if not room_pipeline:
            print("❌ Room Pipeline not found")
            return False, None
        
        print(f"✅ Room Pipeline found (ID: {room_pipeline.id})")
        
        # Check prerequisites - product table should have data
        product_count = db.query(Product).count()
        print(f"📊 product available: {product_count}")
        
        if product_count == 0:
            print("⚠️ No product found - Room Type Pipeline should run first")
            return False, room_pipeline.id
        
        # Check current product_variant count
        variant_count = db.query(ProductVariants).count()
        print(f"📊 Current product variants: {variant_count}")
        
        print(f"✅ Prerequisites met - ready to test Room Pipeline")
        return True, room_pipeline.id
        
    except Exception as e:
        print(f"❌ Error checking setup: {e}")
        return False, None
    finally:
        db.close()


def execute_room_pipeline(pipeline_id):
    """Execute the Room Pipeline via API"""
    print(f"\n🚀 EXECUTING ROOM PIPELINE (ID: {pipeline_id})")
    print("=" * 60)
    
    import requests
    
    try:
        # Clear existing product variants for clean test
        print("🧹 Clearing existing product variants...")
        db = SessionLocal()
        try:
            deleted_count = db.query(ProductVariants).count()
            db.query(ProductVariants).delete()
            db.commit()
            print(f"   Deleted {deleted_count} existing product variants")
        finally:
            db.close()
        
        # Execute Room Pipeline
        print(f"📡 Triggering Room Pipeline execution...")
        response = requests.post(f"http://localhost:8000/api/v1/pipelines/{pipeline_id}/execute")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Pipeline execution started: {result}")
            return result.get('run_id')
        else:
            print(f"❌ Failed to start pipeline: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error executing pipeline: {e}")
        return None


def monitor_pipeline_execution(pipeline_id, run_id):
    """Monitor Room Pipeline execution progress"""
    print(f"\n⏳ MONITORING PIPELINE EXECUTION")
    print("=" * 60)
    
    import requests
    
    max_wait_time = 120  # 2 minutes
    check_interval = 10  # 10 seconds
    elapsed_time = 0
    
    while elapsed_time < max_wait_time:
        try:
            # Get pipeline run status
            response = requests.get(f"http://localhost:8000/api/v1/pipelines/{pipeline_id}/runs")
            
            if response.status_code == 200:
                runs = response.json()
                if runs:
                    latest_run = runs[0]  # Most recent run
                    status = latest_run.get('status')
                    extracted = latest_run.get('records_extracted', 0)
                    transformed = latest_run.get('records_transformed', 0)
                    loaded = latest_run.get('records_loaded', 0)
                    
                    print(f"📊 Status: {status} | Extract: {extracted} | Transform: {transformed} | Load: {loaded}")
                    
                    if status in ['completed', 'failed']:
                        return latest_run
                    
            time.sleep(check_interval)
            elapsed_time += check_interval
            
        except Exception as e:
            print(f"⚠️ Error monitoring pipeline: {e}")
            time.sleep(check_interval)
            elapsed_time += check_interval
    
    print(f"⏰ Timeout reached - pipeline may still be running")
    return None


def verify_room_pipeline_results():
    """Verify Room Pipeline execution results"""
    print(f"\n🔍 VERIFYING ROOM PIPELINE RESULTS")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Get final counts
        product_count = db.query(Product).count()
        variant_count = db.query(ProductVariants).count()
        
        print(f"📊 Final State:")
        print(f"   product: {product_count}")
        print(f"   Product Variants: {variant_count}")
        
        # Check if variants were created
        if variant_count == 0:
            print(f"❌ No product variants created")
            return False
        
        # Sample some variants
        print(f"\n📋 Sample Product Variants:")
        sample_variants = db.query(ProductVariants).limit(5).all()
        
        for variant in sample_variants:
            product_name = "Unknown"
            if variant.product:
                product_name = variant.product.title
            
            print(f"   - {variant.name}")
            print(f"     ID: {variant.id}")
            print(f"     Product: {product_name}")
            print(f"     Connected: {variant.is_connected}")
            print(f"     Room Config: {variant.room_config_name}")
        
        # Check foreign key relationships
        variants_with_product = db.query(ProductVariants).filter(
            ProductVariants.product_id.isnot(None)
        ).count()
        
        variants_without_product = variant_count - variants_with_product
        
        print(f"\n🔗 Relationship Analysis:")
        print(f"   Variants with Product links: {variants_with_product}")
        print(f"   Variants without Product links: {variants_without_product}")
        
        if variants_with_product > 0:
            link_percentage = (variants_with_product / variant_count) * 100
            print(f"   Link success rate: {link_percentage:.1f}%")
        
        # Success criteria
        success_criteria = [
            ("Variants Created", variant_count > 0),
            ("Product Links", variants_with_product > 0),
            ("Data Integrity", variant_count > 0 and all(v.migrated_id for v in sample_variants))
        ]
        
        print(f"\n📋 Success Criteria:")
        all_passed = True
        for criterion, passed in success_criteria:
            status = "✅" if passed else "❌"
            print(f"   {status} {criterion}")
            if not passed:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error verifying results: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()


def main():
    """Main test function"""
    print("🧪 ROOM PIPELINE TEST SUITE")
    print("=" * 60)
    
    # Test 1: Setup and prerequisites
    setup_success, pipeline_id = test_room_pipeline_setup()
    
    if not setup_success or not pipeline_id:
        print("\n💥 Setup failed - aborting tests")
        return False
    
    # Test 2: Execute Room Pipeline
    run_id = execute_room_pipeline(pipeline_id)
    
    if not run_id:
        print("\n💥 Pipeline execution failed - aborting tests")
        return False
    
    # Test 3: Monitor execution
    final_run = monitor_pipeline_execution(pipeline_id, run_id)
    
    # Test 4: Verify results
    verification_success = verify_room_pipeline_results()
    
    print("\n" + "=" * 60)
    print("📊 ROOM PIPELINE TEST RESULTS:")
    print(f"   Setup: {'✅ PASS' if setup_success else '❌ FAIL'}")
    print(f"   Execution: {'✅ PASS' if run_id else '❌ FAIL'}")
    print(f"   Monitoring: {'✅ PASS' if final_run else '⏰ TIMEOUT'}")
    print(f"   Verification: {'✅ PASS' if verification_success else '❌ FAIL'}")
    
    overall_success = all([setup_success, run_id, verification_success])
    
    if overall_success:
        print("\n🎉 ROOM PIPELINE TESTS PASSED!")
        print("\n✅ Features Verified:")
        print("   - Dedicated Room Pipeline configuration")
        print("   - Room__c data extraction from Salesforce")
        print("   - Product variants table population")
        print("   - Foreign key relationships to product")
        print("   - Independent pipeline execution")
        print("\n🚀 Room Pipeline is production ready!")
    else:
        print("\n💥 SOME ROOM PIPELINE TESTS FAILED")
        print("   Please check the logs and fix issues before proceeding")
    
    return overall_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
