#!/usr/bin/env python3
"""
Test script to verify Salesforce data integration with real field mappings
"""
import sys
import os
import json
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun
from app.etl.extractors.salesforce_extractor import SalesforceExtractor
from app.etl.pipelines.pipeline_strategies.salesforce_to_postgresql_strategy import SalesforceToPostgreSQLStrategy


def test_salesforce_connection():
    """Test Salesforce connection and authentication"""
    print("🔌 Testing Salesforce Connection...")
    
    try:
        extractor = SalesforceExtractor()
        if extractor.authenticate():
            print("✅ Salesforce authentication successful")
            
            # Test basic object access
            try:
                resort_info = extractor.get_object_info("Resort__c")
                print(f"✅ Resort__c object accessible - {resort_info['field_count']} fields")
                
                hotel_info = extractor.get_object_info("Hotel__c")
                print(f"✅ Hotel__c object accessible - {hotel_info['field_count']} fields")
                
                return True
            except Exception as e:
                print(f"❌ Error accessing Salesforce objects: {e}")
                return False
        else:
            print("❌ Salesforce authentication failed")
            return False
    except Exception as e:
        print(f"❌ Error connecting to Salesforce: {e}")
        return False


def test_resort_field_mappings():
    """Test Resort__c field mappings and data extraction"""
    print("\n🏖️ Testing Resort Pipeline Field Mappings...")
    
    db = SessionLocal()
    try:
        # Get the resort pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Resort Pipeline").first()
        if not pipeline:
            print("❌ Resort Pipeline not found")
            return False
        
        print(f"✅ Found Resort Pipeline")
        print(f"   Source Object: {pipeline.source_object}")
        print(f"   Source Fields: {pipeline.source_fields}")
        print(f"   Destination Fields: {pipeline.destination_fields}")
        
        # Test field extraction with real Salesforce data
        try:
            extractor = SalesforceExtractor()
            if not extractor.authenticate():
                print("❌ Salesforce authentication failed")
                return False
            
            # Extract a small sample of Resort__c records
            print("🔄 Extracting sample Resort__c records...")
            sample_records = extractor.extract_records(
                object_name="Resort__c",
                fields=pipeline.source_fields,
                limit=3  # Just get 3 records for testing
            )
            
            print(f"✅ Extracted {len(sample_records)} Resort__c records")
            
            if sample_records:
                print("📋 Sample record structure:")
                sample_record = sample_records[0]
                for field, value in sample_record.items():
                    value_str = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                    print(f"   {field}: {value_str}")
                
                # Test transformation logic
                print("\n🔄 Testing transformation logic...")
                strategy = SalesforceToPostgreSQLStrategy(db)
                
                # Create a test pipeline run
                run = ETLPipelineRun(
                    pipeline_id=pipeline.id,
                    status="running",
                    logs=[]
                )
                db.add(run)
                db.commit()
                db.refresh(run)
                
                # Test transformation
                transformed_data = strategy._transform_data(pipeline, run, sample_records)
                print(f"✅ Transformed {len(transformed_data)} records")
                
                if transformed_data:
                    print("📋 Sample transformed record:")
                    sample_transformed = transformed_data[0]
                    for field, value in sample_transformed.items():
                        value_str = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                        print(f"   {field}: {value_str}")
                
                # Clean up test run
                run.status = "completed"
                run.completed_at = datetime.utcnow()
                db.commit()
                
                return True
            else:
                print("⚠️ No Resort__c records found in Salesforce")
                return False
                
        except Exception as e:
            print(f"❌ Error testing Resort field mappings: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    finally:
        db.close()


def test_hotel_field_mappings():
    """Test Hotel__c field mappings and data extraction"""
    print("\n🏨 Testing Hotel Pipeline Field Mappings...")
    
    db = SessionLocal()
    try:
        # Get the hotel pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Hotel Pipeline").first()
        if not pipeline:
            print("❌ Hotel Pipeline not found")
            return False
        
        print(f"✅ Found Hotel Pipeline")
        print(f"   Source Object: {pipeline.source_object}")
        print(f"   Source Fields: {pipeline.source_fields}")
        print(f"   Destination Fields: {pipeline.destination_fields}")
        
        # Test field extraction with real Salesforce data
        try:
            extractor = SalesforceExtractor()
            if not extractor.authenticate():
                print("❌ Salesforce authentication failed")
                return False
            
            # Extract a small sample of Hotel__c records
            print("🔄 Extracting sample Hotel__c records...")
            sample_records = extractor.extract_records(
                object_name="Hotel__c",
                fields=pipeline.source_fields,
                limit=3  # Just get 3 records for testing
            )
            
            print(f"✅ Extracted {len(sample_records)} Hotel__c records")
            
            if sample_records:
                print("📋 Sample record structure:")
                sample_record = sample_records[0]
                for field, value in sample_record.items():
                    value_str = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                    print(f"   {field}: {value_str}")
                
                # Test transformation logic
                print("\n🔄 Testing transformation logic...")
                strategy = SalesforceToPostgreSQLStrategy(db)
                
                # Create a test pipeline run
                run = ETLPipelineRun(
                    pipeline_id=pipeline.id,
                    status="running",
                    logs=[]
                )
                db.add(run)
                db.commit()
                db.refresh(run)
                
                # Test transformation
                transformed_data = strategy._transform_data(pipeline, run, sample_records)
                print(f"✅ Transformed {len(transformed_data)} records")
                
                if transformed_data:
                    print("📋 Sample transformed record:")
                    sample_transformed = transformed_data[0]
                    for field, value in sample_transformed.items():
                        value_str = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                        print(f"   {field}: {value_str}")
                
                # Clean up test run
                run.status = "completed"
                run.completed_at = datetime.utcnow()
                db.commit()
                
                return True
            else:
                print("⚠️ No Hotel__c records found in Salesforce")
                return False
                
        except Exception as e:
            print(f"❌ Error testing Hotel field mappings: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    finally:
        db.close()


def main():
    """Main test function"""
    print("🚀 Testing Salesforce Data Integration")
    print("=" * 60)
    
    # Test Salesforce connection
    connection_success = test_salesforce_connection()
    
    if not connection_success:
        print("\n💥 Salesforce connection failed - cannot proceed with field mapping tests")
        return False
    
    # Test resort field mappings
    resort_success = test_resort_field_mappings()
    
    # Test hotel field mappings
    hotel_success = test_hotel_field_mappings()
    
    print("\n" + "=" * 60)
    print("📊 Salesforce Integration Test Results:")
    print(f"   Connection: {'✅ PASS' if connection_success else '❌ FAIL'}")
    print(f"   Resort Field Mappings: {'✅ PASS' if resort_success else '❌ FAIL'}")
    print(f"   Hotel Field Mappings: {'✅ PASS' if hotel_success else '❌ FAIL'}")
    
    if connection_success and resort_success and hotel_success:
        print("\n🎉 All Salesforce integration tests passed!")
        return True
    else:
        print("\n💥 Some Salesforce integration tests failed.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
