#!/usr/bin/env python3
"""
Utility script to clean up stale/stuck pipeline runs
"""
import sys
import os
from datetime import datetime, timedelta

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipelineRun


def cleanup_stale_pipelines(max_runtime_hours=6):
    """
    Clean up pipeline runs that have been stuck in 'running' status for too long
    
    Args:
        max_runtime_hours: Maximum hours a pipeline should run before being considered stale
    """
    db = SessionLocal()
    try:
        # Calculate cutoff time
        cutoff_time = datetime.utcnow() - timedelta(hours=max_runtime_hours)
        
        # Find stuck pipeline runs
        stuck_runs = db.query(ETLPipelineRun).filter(
            ETLPipelineRun.status == 'running',
            ETLPipelineRun.started_at < cutoff_time
        ).all()
        
        if not stuck_runs:
            print("✅ No stale pipeline runs found")
            return 0
        
        print(f"🔄 Found {len(stuck_runs)} stale pipeline runs to clean up:")
        
        for run in stuck_runs:
            runtime = datetime.utcnow() - run.started_at
            print(f"  - Run ID {run.id} (Pipeline {run.pipeline_id}): running for {runtime}")
            
            # Mark as failed with appropriate error message
            run.status = 'failed'
            run.error_message = f'Pipeline run terminated due to exceeding maximum runtime of {max_runtime_hours} hours'
            run.completed_at = datetime.utcnow()
        
        # Commit changes
        db.commit()
        print(f"✅ Successfully cleaned up {len(stuck_runs)} stale pipeline runs")
        return len(stuck_runs)
        
    except Exception as e:
        print(f"❌ Error cleaning up stale pipelines: {e}")
        db.rollback()
        return 0
    finally:
        db.close()


def get_pipeline_status_summary():
    """Get a summary of all pipeline run statuses"""
    db = SessionLocal()
    try:
        from sqlalchemy import text
        
        # Get status counts
        result = db.execute(text("""
            SELECT status, COUNT(*) as count 
            FROM etl_pipeline_runs 
            GROUP BY status 
            ORDER BY count DESC
        """)).fetchall()
        
        print("📊 Pipeline Run Status Summary:")
        for status, count in result:
            print(f"  {status}: {count} runs")
        
        # Get recent runs
        recent_runs = db.query(ETLPipelineRun).order_by(
            ETLPipelineRun.started_at.desc()
        ).limit(5).all()
        
        print("\n📋 Recent Pipeline Runs:")
        for run in recent_runs:
            status_icon = "✅" if run.status == "completed" else "❌" if run.status == "failed" else "🔄"
            print(f"  {status_icon} Run {run.id}: {run.status} (Started: {run.started_at})")
            
    except Exception as e:
        print(f"❌ Error getting pipeline status: {e}")
    finally:
        db.close()


def main():
    """Main function"""
    print("🧹 Pipeline Cleanup Utility")
    print("=" * 50)
    
    # Show current status
    get_pipeline_status_summary()
    
    print("\n" + "=" * 50)
    
    # Clean up stale pipelines
    cleaned_count = cleanup_stale_pipelines()
    
    if cleaned_count > 0:
        print("\n" + "=" * 50)
        print("📊 Updated Status After Cleanup:")
        get_pipeline_status_summary()
    
    print("\n🎉 Pipeline cleanup completed!")


if __name__ == "__main__":
    main()
