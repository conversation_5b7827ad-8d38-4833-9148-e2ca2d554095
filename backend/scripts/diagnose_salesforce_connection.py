#!/usr/bin/env python3
"""
Comprehensive diagnostic script for Salesforce connection and Resort Pipeline data extraction.
"""
import os
import sys
import traceback
from typing import Dict, Any, List

def test_environment_variables():
    """Test Salesforce environment variables."""
    print("🔍 Testing Salesforce environment variables...")
    
    required_vars = [
        'SALESFORCE_CLIENT_ID',
        'SALESFORCE_CLIENT_SECRET', 
        'SALESFORCE_USERNAME',
        'SALESFORCE_PASSWORD'
    ]
    
    optional_vars = ['SALESFORCE_SECURITY_TOKEN']
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if not value:
            missing_vars.append(var)
        else:
            # Show partial value for security
            if 'PASSWORD' in var:
                print(f"   ✅ {var}: {'*' * len(value)} (length: {len(value)})")
            elif 'SECRET' in var or 'TOKEN' in var:
                print(f"   ✅ {var}: {value[:10]}... (length: {len(value)})")
            else:
                print(f"   ✅ {var}: {value}")
    
    for var in optional_vars:
        value = os.getenv(var)
        if value and value != "your_security_token_here":
            print(f"   ✅ {var}: {value[:10]}... (length: {len(value)})")
        else:
            print(f"   ⚠️  {var}: Not set or placeholder value")
    
    if missing_vars:
        print(f"   ❌ Missing required variables: {missing_vars}")
        return False
    else:
        print("   ✅ All required Salesforce environment variables are set")
        return True

def test_salesforce_authentication():
    """Test Salesforce authentication."""
    print("\n🔍 Testing Salesforce authentication...")
    
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app.etl.extractors.salesforce_extractor import SalesforceExtractor
        from app.config.settings import settings
        
        print(f"   Username: {settings.salesforce_username}")
        print(f"   Client ID: {settings.salesforce_client_id}")
        print(f"   Domain: powderbyrne.my")
        
        # Create extractor and test authentication
        extractor = SalesforceExtractor()
        
        print("   Attempting authentication...")
        auth_result = extractor.authenticate()
        
        if auth_result:
            print("   ✅ Salesforce authentication successful!")
            
            # Test basic API call
            try:
                user_info = extractor.sf.query("SELECT Id, Name, Username FROM User LIMIT 1")
                print(f"   ✅ API test successful - found {user_info['totalSize']} user(s)")
                if user_info['records']:
                    user = user_info['records'][0]
                    print(f"      User: {user.get('Name')} ({user.get('Username')})")
                
                return True
                
            except Exception as api_error:
                print(f"   ❌ API test failed: {api_error}")
                return False
                
        else:
            print("   ❌ Salesforce authentication failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing authentication: {e}")
        traceback.print_exc()
        return False

def test_resort_object_access():
    """Test access to Resort__c object."""
    print("\n🔍 Testing Resort__c object access...")
    
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app.etl.extractors.salesforce_extractor import SalesforceExtractor
        
        extractor = SalesforceExtractor()
        
        if not extractor.authenticate():
            print("   ❌ Cannot test object access - authentication failed")
            return False
        
        # Test object describe
        try:
            describe_result = extractor.sf.Resort__c.describe()
            print(f"   ✅ Resort__c object accessible")
            print(f"      Label: {describe_result['label']}")
            print(f"      Fields: {len(describe_result['fields'])} available")
            
            # Show some key fields
            key_fields = ['Id', 'Name', 'Country__c', 'CurrencyIsoCode', 'Description__c']
            available_fields = [f['name'] for f in describe_result['fields']]
            
            print("   📋 Key fields availability:")
            for field in key_fields:
                if field in available_fields:
                    print(f"      ✅ {field}")
                else:
                    print(f"      ❌ {field} - NOT FOUND")
            
        except Exception as describe_error:
            print(f"   ❌ Cannot describe Resort__c object: {describe_error}")
            return False
        
        # Test record count
        try:
            count_query = "SELECT COUNT() FROM Resort__c"
            count_result = extractor.sf.query(count_query)
            record_count = count_result['totalSize']
            print(f"   ✅ Resort__c record count: {record_count}")
            
            if record_count == 0:
                print("   ⚠️  No Resort__c records found in Salesforce")
                return False
                
        except Exception as count_error:
            print(f"   ❌ Cannot count Resort__c records: {count_error}")
            return False
        
        # Test sample data extraction
        try:
            sample_query = "SELECT Id, Name, Country__c, CurrencyIsoCode, Description__c FROM Resort__c LIMIT 3"
            sample_result = extractor.sf.query(sample_query)
            
            print(f"   ✅ Sample data extraction successful - {sample_result['totalSize']} records")
            
            if sample_result['records']:
                print("   📋 Sample Resort__c records:")
                for i, record in enumerate(sample_result['records'], 1):
                    print(f"      Record {i}:")
                    print(f"        ID: {record.get('Id')}")
                    print(f"        Name: {record.get('Name')}")
                    print(f"        Country: {record.get('Country__c')}")
                    print(f"        Currency: {record.get('CurrencyIsoCode')}")
                    print(f"        Description: {record.get('Description__c', 'N/A')}")
            
            return True
            
        except Exception as sample_error:
            print(f"   ❌ Cannot extract sample data: {sample_error}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing Resort__c access: {e}")
        traceback.print_exc()
        return False

def test_resort_pipeline_configuration():
    """Test Resort Pipeline configuration."""
    print("\n🔍 Testing Resort Pipeline configuration...")
    
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app.core.database import SessionLocal
        from app.models.etl_pipeline import ETLPipeline
        
        db = SessionLocal()
        try:
            # Find Resort Pipeline
            resort_pipeline = db.query(ETLPipeline).filter(
                ETLPipeline.name == "Resort Pipeline"
            ).first()
            
            if not resort_pipeline:
                print("   ❌ Resort Pipeline not found in database")
                return False
            
            print("   ✅ Resort Pipeline found")
            print(f"      ID: {resort_pipeline.id}")
            print(f"      Name: {resort_pipeline.name}")
            print(f"      Source Type: {resort_pipeline.source_type}")
            print(f"      Source Object: {resort_pipeline.source_object}")
            print(f"      Destination Table: {resort_pipeline.destination_table}")
            print(f"      Is Active: {resort_pipeline.is_active}")
            
            # Check source fields
            if resort_pipeline.source_fields:
                print(f"   📋 Source fields ({len(resort_pipeline.source_fields)}):")
                for field in resort_pipeline.source_fields:
                    print(f"      - {field}")
            else:
                print("   ⚠️  No source fields configured")
            
            # Check destination fields mapping
            if resort_pipeline.destination_fields:
                print(f"   📋 Destination field mappings ({len(resort_pipeline.destination_fields)}):")
                for dest_field, source_field in resort_pipeline.destination_fields.items():
                    print(f"      {source_field} → {dest_field}")
            else:
                print("   ⚠️  No destination field mappings configured")
            
            return True
            
        except Exception as db_error:
            print(f"   ❌ Database error: {db_error}")
            return False
        finally:
            db.close()
            
    except Exception as e:
        print(f"   ❌ Error testing pipeline configuration: {e}")
        traceback.print_exc()
        return False

def test_end_to_end_extraction():
    """Test end-to-end data extraction using the pipeline extractor."""
    print("\n🔍 Testing end-to-end data extraction...")
    
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app.core.database import SessionLocal
        from app.models.etl_pipeline import ETLPipeline
        from app.etl.extractors.salesforce_extractor import SalesforceExtractor
        
        db = SessionLocal()
        try:
            # Get Resort Pipeline configuration
            resort_pipeline = db.query(ETLPipeline).filter(
                ETLPipeline.name == "Resort Pipeline"
            ).first()
            
            if not resort_pipeline:
                print("   ❌ Resort Pipeline not found")
                return False
            
            # Create extractor and authenticate
            extractor = SalesforceExtractor()
            if not extractor.authenticate():
                print("   ❌ Authentication failed")
                return False
            
            print("   ✅ Authentication successful")
            
            # Extract data using pipeline configuration
            print(f"   Extracting from object: {resort_pipeline.source_object}")
            print(f"   Using fields: {resort_pipeline.source_fields}")
            
            data = extractor.extract_records(
                object_name=resort_pipeline.source_object,
                fields=resort_pipeline.source_fields or [],
                limit=5  # Limit for testing
            )
            
            print(f"   ✅ Extraction successful - {len(data)} records extracted")
            
            if data:
                print("   📋 Sample extracted data:")
                for i, record in enumerate(data[:2], 1):
                    print(f"      Record {i}: {record}")
            
            return True
            
        except Exception as extraction_error:
            print(f"   ❌ Extraction failed: {extraction_error}")
            traceback.print_exc()
            return False
        finally:
            db.close()
            
    except Exception as e:
        print(f"   ❌ Error in end-to-end test: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all diagnostic tests."""
    print("🚀 Salesforce Connection and Resort Pipeline Diagnostics")
    print("=" * 70)
    
    tests = [
        ("Environment Variables", test_environment_variables),
        ("Salesforce Authentication", test_salesforce_authentication),
        ("Resort__c Object Access", test_resort_object_access),
        ("Resort Pipeline Configuration", test_resort_pipeline_configuration),
        ("End-to-End Extraction", test_end_to_end_extraction)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 70)
    print("📊 Diagnostic Results Summary:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All diagnostics passed! Resort Pipeline should work correctly.")
    else:
        print("\n⚠️  Some diagnostics failed. Please fix the issues above before running the pipeline.")
    
    return all_passed

if __name__ == "__main__":
    main()
