#!/usr/bin/env python3
"""
Test script to isolate Room Type Pipeline transformation issues
"""
import sys
import os
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun
from app.etl.extractors.salesforce_extractor import SalesforceExtractor
from app.etl.pipelines.pipeline_strategies.salesforce_to_postgresql_strategy import SalesforceToPostgreSQLStrategy


def test_room_type_pipeline_transformation():
    """Test Room Type Pipeline transformation step by step"""
    print("🧪 TESTING ROOM TYPE PIPELINE TRANSFORMATION")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        # Get Room Type Pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Room Type Pipeline").first()
        if not pipeline:
            print("❌ Room Type Pipeline not found")
            return False
        
        print(f"✅ Found pipeline: {pipeline.name}")
        
        # Extract sample data
        extractor = SalesforceExtractor()
        extractor.authenticate()
        
        data = extractor.extract_records(
            object_name=pipeline.source_object,
            fields=pipeline.source_fields or [],
            limit=3
        )
        
        print(f"✅ Extracted {len(data)} records for transformation test")
        
        # Create test run
        run = ETLPipelineRun(
            pipeline_id=pipeline.id,
            status="running",
            logs=[]
        )
        db.add(run)
        db.commit()
        db.refresh(run)
        
        print(f"✅ Created test run: {run.id}")
        
        # Test transformation
        strategy = SalesforceToPostgreSQLStrategy(db)
        
        print("\n🔄 Testing transformation...")
        try:
            transformed_data = strategy._transform_data(pipeline, run, data)
            print(f"✅ Transformed {len(transformed_data)} records")
            
            if transformed_data:
                print("\n📋 Sample transformed record:")
                sample = transformed_data[0]
                for key, value in sample.items():
                    value_str = str(value)[:100] + "..." if len(str(value)) > 100 else str(value)
                    print(f"   {key}: {value_str}")
                
                # Update run status
                run.status = "completed"
                run.completed_at = datetime.utcnow()
                db.commit()
                
                return True
            else:
                print("⚠️ No data transformed")
                return False
                
        except Exception as e:
            print(f"❌ Error during transformation: {e}")
            import traceback
            traceback.print_exc()
            
            # Update run status
            run.status = "failed"
            run.completed_at = datetime.utcnow()
            run.error_message = str(e)
            db.commit()
            
            return False
            
    finally:
        db.close()


def test_room_pipeline_transformation():
    """Test Room Pipeline transformation step by step"""
    print("\n🧪 TESTING ROOM PIPELINE TRANSFORMATION")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        # Get Room Pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Room Pipeline").first()
        if not pipeline:
            print("❌ Room Pipeline not found")
            return False
        
        print(f"✅ Found pipeline: {pipeline.name}")
        
        # Extract sample data
        extractor = SalesforceExtractor()
        extractor.authenticate()
        
        data = extractor.extract_records(
            object_name=pipeline.source_object,
            fields=pipeline.source_fields or [],
            limit=3
        )
        
        print(f"✅ Extracted {len(data)} records for transformation test")
        
        # Create test run
        run = ETLPipelineRun(
            pipeline_id=pipeline.id,
            status="running",
            logs=[]
        )
        db.add(run)
        db.commit()
        db.refresh(run)
        
        print(f"✅ Created test run: {run.id}")
        
        # Test transformation
        strategy = SalesforceToPostgreSQLStrategy(db)
        
        print("\n🔄 Testing transformation...")
        try:
            transformed_data = strategy._transform_data(pipeline, run, data)
            print(f"✅ Transformed {len(transformed_data)} records")
            
            if transformed_data:
                print("\n📋 Sample transformed record:")
                sample = transformed_data[0]
                for key, value in sample.items():
                    value_str = str(value)[:100] + "..." if len(str(value)) > 100 else str(value)
                    print(f"   {key}: {value_str}")
                
                # Update run status
                run.status = "completed"
                run.completed_at = datetime.utcnow()
                db.commit()
                
                return True
            else:
                print("⚠️ No data transformed")
                return False
                
        except Exception as e:
            print(f"❌ Error during transformation: {e}")
            import traceback
            traceback.print_exc()
            
            # Update run status
            run.status = "failed"
            run.completed_at = datetime.utcnow()
            run.error_message = str(e)
            db.commit()
            
            return False
            
    finally:
        db.close()


if __name__ == "__main__":
    room_type_ok = test_room_type_pipeline_transformation()
    room_ok = test_room_pipeline_transformation()
    
    print(f"\n🎯 SUMMARY:")
    print(f"   Room Type Pipeline transformation: {'✅ OK' if room_type_ok else '❌ FAILED'}")
    print(f"   Room Pipeline transformation: {'✅ OK' if room_ok else '❌ FAILED'}")
    
    if room_type_ok and room_ok:
        print("\n✅ Both pipelines can transform data successfully!")
        print("   The issue is likely in the loading phase or pipeline execution flow.")
    else:
        print("\n❌ Data transformation issues detected.")
        print("   Need to fix transformation before proceeding.")
