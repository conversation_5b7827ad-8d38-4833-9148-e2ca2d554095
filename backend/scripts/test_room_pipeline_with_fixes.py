#!/usr/bin/env python3
"""
Test Room Pipeline with implemented fixes for Salesforce extraction timeout
"""
import sys
import os
import time
import requests
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipelineRun, ProductVariants, Product


def cancel_stuck_room_pipelines():
    """Cancel any stuck Room Pipeline runs"""
    print("🛑 CANCELLING STUCK ROOM PIPELINES")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        stuck_runs = db.query(ETLPipelineRun).filter(
            ETLPipelineRun.pipeline_id == 4,
            ETLPipelineRun.status == 'running'
        ).all()
        
        if stuck_runs:
            for run in stuck_runs:
                run.status = 'cancelled'
                run.completed_at = datetime.utcnow()
                run.error_message = "Cancelled before testing fixes"
            db.commit()
            print(f"✅ Cancelled {len(stuck_runs)} stuck run(s)")
        else:
            print("✅ No stuck runs found")
        
        return True
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        db.close()


def verify_prerequisites():
    """Verify Room Pipeline prerequisites"""
    print("\n📋 VERIFYING PREREQUISITES")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        # Check product table
        product_count = db.query(Product).count()
        print(f"📊 product available: {product_count}")
        
        if product_count == 0:
            print("❌ No product found - need to run Room Type Pipeline first")
            return False
        
        # Check product_variant table
        variant_count = db.query(ProductVariants).count()
        print(f"📊 Current product variants: {variant_count}")
        
        # Show sample product for reference
        sample_product = db.query(Product).limit(3).all()
        print(f"\n📋 Sample product (for Room__c mapping):")
        for product in sample_product:
            print(f"   - {product.title} (migrated_id: {product.migrated_id})")
        
        print("✅ Prerequisites verified")
        return True
        
    except Exception as e:
        print(f"❌ Error verifying prerequisites: {e}")
        return False
    finally:
        db.close()


def test_room_pipeline_with_timeout_protection():
    """Test Room Pipeline with enhanced timeout protection"""
    print("\n🚀 TESTING ROOM PIPELINE WITH FIXES")
    print("=" * 50)
    
    try:
        # Get initial state
        db = SessionLocal()
        try:
            initial_variants = db.query(ProductVariants).count()
            print(f"📊 Initial product variants: {initial_variants}")
        finally:
            db.close()
        
        # Execute Room Pipeline
        print("📡 Starting Room Pipeline with timeout protection...")
        start_time = time.time()
        
        response = requests.post("http://localhost:8000/api/v1/pipelines/4/execute", timeout=15)
        
        if response.status_code != 200:
            print(f"❌ Failed to start: {response.status_code}")
            print(f"Response: {response.text}")
            return False
        
        result = response.json()
        run_id = result.get('run_id')
        print(f"✅ Started: Run ID {run_id}")
        
        # Enhanced monitoring with early timeout detection
        print("⏳ Enhanced monitoring (2 minute timeout, early detection)...")
        
        max_wait = 120  # 2 minutes
        check_interval = 5  # 5 seconds
        last_status = None
        stuck_count = 0
        extraction_timeout_count = 0
        
        while time.time() - start_time < max_wait:
            try:
                elapsed = time.time() - start_time
                
                # Get pipeline status
                status_response = requests.get("http://localhost:8000/api/v1/pipelines/4/runs", timeout=5)
                
                if status_response.status_code == 200:
                    runs = status_response.json()
                    if runs:
                        latest_run = runs[0]
                        status = latest_run.get('status')
                        extracted = latest_run.get('records_extracted', 0)
                        transformed = latest_run.get('records_transformed', 0)
                        loaded = latest_run.get('records_loaded', 0)
                        error_msg = latest_run.get('error_message', '')
                        
                        # Progress tracking
                        current_status = f"{status}-{extracted}-{transformed}-{loaded}"
                        if current_status == last_status:
                            stuck_count += 1
                        else:
                            stuck_count = 0
                        last_status = current_status
                        
                        # Early timeout detection for extraction phase
                        if status == 'running' and extracted is None and elapsed > 30:
                            extraction_timeout_count += 1
                        else:
                            extraction_timeout_count = 0
                        
                        print(f"   [{elapsed:3.0f}s] {status} | E:{extracted} T:{transformed} L:{loaded} | Stuck:{stuck_count}")
                        
                        if error_msg:
                            print(f"   [{elapsed:3.0f}s] Error: {error_msg}")
                        
                        # Check database progress
                        db = SessionLocal()
                        try:
                            current_variants = db.query(ProductVariants).count()
                            if current_variants != initial_variants:
                                print(f"   [{elapsed:3.0f}s] 🎉 Database: {current_variants} variants (+{current_variants - initial_variants})")
                        finally:
                            db.close()
                        
                        # Success conditions
                        if status == 'completed':
                            print(f"✅ COMPLETED in {elapsed:.1f} seconds!")
                            print(f"📊 Final: Extract:{extracted} Transform:{transformed} Load:{loaded}")
                            return True
                        
                        elif status == 'failed':
                            print(f"❌ FAILED after {elapsed:.1f} seconds")
                            print(f"Error: {error_msg}")
                            return False
                        
                        # Early timeout detection
                        if extraction_timeout_count >= 6:  # 30 seconds stuck in extraction
                            print(f"🚨 EARLY TIMEOUT DETECTION: Extraction stuck for 30+ seconds")
                            print("   Likely Salesforce API timeout - cancelling run")
                            return False
                        
                        # General stuck detection
                        if stuck_count >= 12:  # 60 seconds no progress
                            print(f"🚨 STUCK DETECTION: No progress for 60+ seconds")
                            return False
                
                time.sleep(check_interval)
                
            except Exception as e:
                print(f"⚠️ Monitoring error: {e}")
                time.sleep(check_interval)
        
        print(f"⏰ TIMEOUT after {max_wait} seconds")
        return False
        
    except Exception as e:
        print(f"❌ Execution error: {e}")
        return False


def verify_room_pipeline_results():
    """Verify Room Pipeline results after execution"""
    print("\n🔍 VERIFYING ROOM PIPELINE RESULTS")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        # Check final counts
        total_variants = db.query(ProductVariants).count()
        total_product = db.query(Product).count()
        
        print(f"📊 Final Results:")
        print(f"   product: {total_product}")
        print(f"   Product Variants: {total_variants}")
        
        if total_variants > 0:
            print("✅ DATA PERSISTENCE SUCCESS!")
            
            # Analyze relationships
            variants_with_product = db.query(ProductVariants).filter(
                ProductVariants.product_id.isnot(None)
            ).count()
            
            variants_without_product = total_variants - variants_with_product
            
            print(f"\n🔗 Relationship Analysis:")
            print(f"   Variants with product links: {variants_with_product}")
            print(f"   Variants without product links: {variants_without_product}")
            
            if total_variants > 0:
                link_rate = (variants_with_product / total_variants) * 100
                print(f"   Link success rate: {link_rate:.1f}%")
            
            # Show sample data
            print(f"\n📋 Sample Product Variants:")
            sample_variants = db.query(ProductVariants).limit(5).all()
            
            for variant in sample_variants:
                product_name = "No Product"
                if variant.product:
                    product_name = variant.product.title
                
                print(f"   - {variant.name}")
                print(f"     Product: {product_name}")
                print(f"     Connected: {variant.is_connected}")
                print(f"     Created: {variant.created_at}")
            
            return True
        else:
            print("❌ NO PRODUCT VARIANTS FOUND")
            print("   Data persistence failed - pipeline did not complete loading phase")
            return False
        
    except Exception as e:
        print(f"❌ Error verifying results: {e}")
        return False
    finally:
        db.close()


def main():
    """Main test function with fixes"""
    print("🧪 ROOM PIPELINE TEST WITH SALESFORCE FIXES")
    print("=" * 60)
    print(f"Timestamp: {datetime.utcnow()}")
    print()
    
    # Step 1: Clean up
    cleanup_success = cancel_stuck_room_pipelines()
    
    # Step 2: Verify prerequisites
    prereq_success = verify_prerequisites()
    
    if not prereq_success:
        print("\n💥 Prerequisites not met - aborting test")
        return False
    
    # Step 3: Test with fixes
    execution_success = test_room_pipeline_with_timeout_protection()
    
    # Step 4: Verify results
    verification_success = verify_room_pipeline_results()
    
    print("\n" + "=" * 60)
    print("📊 ROOM PIPELINE FIX TEST RESULTS")
    print("=" * 60)
    
    results = [
        ("Cleanup", cleanup_success),
        ("Prerequisites", prereq_success),
        ("Execution", execution_success),
        ("Verification", verification_success)
    ]
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅" if success else "❌"
        print(f"{status} {test_name}")
    
    success_rate = (passed / total) * 100
    print(f"\nOverall Success Rate: {passed}/{total} ({success_rate:.1f}%)")
    
    if execution_success and verification_success:
        print("\n🎉 ROOM PIPELINE FIXES SUCCESSFUL!")
        print("\n✅ Issues Resolved:")
        print("   - Salesforce extraction timeout fixed")
        print("   - Data persistence working correctly")
        print("   - Performance optimized with limits")
        print("   - Early timeout detection implemented")
        print("\n🚀 Room Pipeline is now fully operational!")
        
    elif execution_success and not verification_success:
        print("\n⚠️ PARTIAL SUCCESS")
        print("   - Pipeline executes without timeout")
        print("   - But data persistence still has issues")
        
    else:
        print("\n💥 FIXES INCOMPLETE")
        print("   - Salesforce extraction issues persist")
        print("   - Additional investigation required")
    
    return execution_success and verification_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
