#!/usr/bin/env python3
"""
Update the Resort Pipeline configuration with new field mappings.
This script adds the additional Salesforce fields to the existing Resort Pipeline.
"""
import sys
import os
import json

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipeline


def update_resort_pipeline():
    """Update the Resort Pipeline configuration with new field mappings"""
    db: Session = SessionLocal()
    
    try:
        # Find the Resort Pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Resort Pipeline").first()
        if not pipeline:
            print("❌ Resort Pipeline not found in database")
            print("💡 You may need to create the pipeline first")
            return False
        
        print(f"🔍 Found Resort Pipeline with ID: {pipeline.id}")
        print(f"   Current source fields: {pipeline.source_fields}")
        print(f"   Current destination fields: {pipeline.destination_fields}")
        
        # Updated field mappings based on your requirements
        new_source_fields = [
            "IsDeleted",
            "Name", 
            "CurrencyIsoCode",
            "CreatedDate",
            "LastModifiedDate",
            "Country__c",
            "Description__c"
        ]
        
        new_destination_fields = {
            "is_active": "IsDeleted",  # Note: This will need transformation (IsDeleted -> !is_active)
            "name": "Name",
            "currency": "CurrencyIsoCode", 
            "created_at": "CreatedDate",
            "updated_at": "LastModifiedDate",
            "country": "Country__c",
            "description": "Description__c"
        }
        
        # Updated transformation config to handle the IsDeleted -> is_active conversion
        new_transformation_config = {
            "clean_nulls": False,
            "trim_whitespace": True,
            "field_transformations": {
                "is_active": {
                    "type": "invert_boolean",
                    "source_field": "IsDeleted",
                    "description": "Convert IsDeleted to is_active by inverting the boolean value"
                }
            }
        }
        
        # Update the pipeline
        pipeline.source_fields = new_source_fields
        pipeline.destination_fields = new_destination_fields
        pipeline.transformation_config = new_transformation_config
        
        # Update description to reflect new fields
        pipeline.description = (
            "Extract resort data from Salesforce Resort__c object with extended fields "
            "(IsDeleted, Name, CurrencyIsoCode, CreatedDate, LastModifiedDate, Country__c, Description__c) "
            "and load into PostgreSQL destination table"
        )
        
        db.commit()
        db.refresh(pipeline)
        
        print("✅ Resort Pipeline updated successfully!")
        print(f"   New source fields ({len(new_source_fields)}): {new_source_fields}")
        print(f"   New destination fields ({len(new_destination_fields)}): {list(new_destination_fields.keys())}")
        print("\n📋 Field Mappings:")
        for dest_field, source_field in new_destination_fields.items():
            transformation = ""
            if dest_field == "is_active":
                transformation = " (inverted from IsDeleted)"
            print(f"   {source_field} → {dest_field}{transformation}")
        
        print(f"\n⚙️ Transformation Config:")
        print(f"   Clean nulls: {new_transformation_config['clean_nulls']}")
        print(f"   Trim whitespace: {new_transformation_config['trim_whitespace']}")
        print(f"   Special transformations: {len(new_transformation_config.get('field_transformations', {}))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error updating Resort Pipeline: {e}")
        db.rollback()
        return False
    finally:
        db.close()


def verify_update():
    """Verify the pipeline update was successful"""
    db: Session = SessionLocal()
    
    try:
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Resort Pipeline").first()
        if not pipeline:
            print("❌ Pipeline verification failed - pipeline not found")
            return False
        
        print(f"\n🔍 Verification Results:")
        print(f"   Pipeline ID: {pipeline.id}")
        print(f"   Name: {pipeline.name}")
        print(f"   Source fields count: {len(pipeline.source_fields or [])}")
        print(f"   Destination fields count: {len(pipeline.destination_fields or {})}")
        print(f"   Is active: {pipeline.is_active}")
        
        # Check if all expected fields are present
        expected_source_fields = {"IsDeleted", "Name", "CurrencyIsoCode", "CreatedDate", "LastModifiedDate", "Country__c", "Description__c"}
        actual_source_fields = set(pipeline.source_fields or [])
        
        expected_dest_fields = {"is_active", "name", "currency", "created_at", "updated_at", "country", "description"}
        actual_dest_fields = set(pipeline.destination_fields.keys() if pipeline.destination_fields else [])
        
        missing_source = expected_source_fields - actual_source_fields
        missing_dest = expected_dest_fields - actual_dest_fields
        
        if missing_source:
            print(f"   ⚠️ Missing source fields: {missing_source}")
        else:
            print(f"   ✅ All expected source fields present")
            
        if missing_dest:
            print(f"   ⚠️ Missing destination fields: {missing_dest}")
        else:
            print(f"   ✅ All expected destination fields present")
        
        return len(missing_source) == 0 and len(missing_dest) == 0
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        return False
    finally:
        db.close()


if __name__ == "__main__":
    print("🚀 Updating Resort Pipeline Field Mappings...")
    print("=" * 60)
    
    # Update the pipeline
    success = update_resort_pipeline()
    
    if success:
        # Verify the update
        verification_success = verify_update()
        
        if verification_success:
            print(f"\n🎉 Resort Pipeline update completed successfully!")
            print("✅ All field mappings are now configured")
            print("✅ Pipeline is ready for execution with extended fields")
        else:
            print(f"\n⚠️ Update completed but verification found issues")
    else:
        print(f"\n💥 Resort Pipeline update failed!")
        sys.exit(1)
    
    print(f"\n💡 Next steps:")
    print("1. Run database migration: alembic upgrade head")
    print("2. Test the pipeline with the new field mappings")
    print("3. Verify data is correctly transformed and loaded")
