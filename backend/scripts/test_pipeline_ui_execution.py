#!/usr/bin/env python3
"""
Test script to verify pipeline execution through the API (simulating UI)
"""
import sys
import os
import requests
import time
import json

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun


def test_api_pipeline_execution():
    """Test pipeline execution through the API"""
    print("🌐 Testing Pipeline Execution via API...")
    
    # API base URL (assuming the backend is running on localhost:8000)
    base_url = "http://localhost:8000/api/v1"
    
    db = SessionLocal()
    try:
        # Get pipeline IDs
        resort_pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Resort Pipeline").first()
        hotel_pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Hotel Pipeline").first()
        
        if not resort_pipeline or not hotel_pipeline:
            print("❌ Pipelines not found in database")
            return False
        
        print(f"✅ Found pipelines - Resort: {resort_pipeline.id}, Hotel: {hotel_pipeline.id}")
        
        # Test 1: Execute Resort Pipeline
        print("\n🏖️ Testing Resort Pipeline execution...")
        try:
            response = requests.post(f"{base_url}/etl-pipelines/{resort_pipeline.id}/execute")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Resort Pipeline execution started - Run ID: {result.get('run_id')}")
                
                # Wait a moment and check status
                time.sleep(2)
                
                # Get pipeline status
                status_response = requests.get(f"{base_url}/etl-pipelines/{resort_pipeline.id}")
                if status_response.status_code == 200:
                    pipeline_info = status_response.json()
                    print(f"✅ Resort Pipeline status retrieved")
                else:
                    print(f"⚠️ Could not get Resort Pipeline status: {status_response.status_code}")
                
            elif response.status_code == 400:
                error_detail = response.json().get('detail', 'Unknown error')
                if "already running" in error_detail.lower():
                    print(f"⚠️ Resort Pipeline is already running: {error_detail}")
                else:
                    print(f"❌ Resort Pipeline execution failed: {error_detail}")
                    return False
            else:
                print(f"❌ Resort Pipeline execution failed with status {response.status_code}: {response.text}")
                return False
                
        except requests.exceptions.ConnectionError:
            print("❌ Could not connect to API - is the backend server running?")
            print("   Please start the backend server with: uvicorn app.main:app --reload")
            return False
        except Exception as e:
            print(f"❌ Error testing Resort Pipeline execution: {e}")
            return False
        
        # Test 2: Try to execute the same pipeline again (should fail with "already running")
        print("\n🔄 Testing duplicate execution prevention...")
        try:
            response = requests.post(f"{base_url}/etl-pipelines/{resort_pipeline.id}/execute")
            
            if response.status_code == 400:
                error_detail = response.json().get('detail', 'Unknown error')
                if "already running" in error_detail.lower():
                    print("✅ Duplicate execution properly prevented")
                else:
                    print(f"⚠️ Unexpected error: {error_detail}")
            elif response.status_code == 200:
                print("⚠️ Duplicate execution was allowed (this might be OK if the first run completed quickly)")
            else:
                print(f"❌ Unexpected response: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error testing duplicate execution: {e}")
            return False
        
        # Test 3: Wait for pipeline to complete and then try again
        print("\n⏳ Waiting for pipeline to complete...")
        max_wait = 60  # Wait up to 60 seconds
        wait_time = 0
        
        while wait_time < max_wait:
            try:
                # Check if any pipelines are still running
                running_runs = db.query(ETLPipelineRun).filter(
                    ETLPipelineRun.pipeline_id == resort_pipeline.id,
                    ETLPipelineRun.status == 'running'
                ).count()
                
                if running_runs == 0:
                    print("✅ Pipeline completed")
                    break
                
                print(f"   Still running... ({wait_time}s)")
                time.sleep(5)
                wait_time += 5
                
            except Exception as e:
                print(f"❌ Error checking pipeline status: {e}")
                break
        
        if wait_time >= max_wait:
            print("⚠️ Pipeline did not complete within 60 seconds")
        
        # Test 4: Execute Hotel Pipeline
        print("\n🏨 Testing Hotel Pipeline execution...")
        try:
            response = requests.post(f"{base_url}/etl-pipelines/{hotel_pipeline.id}/execute")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Hotel Pipeline execution started - Run ID: {result.get('run_id')}")
            elif response.status_code == 400:
                error_detail = response.json().get('detail', 'Unknown error')
                print(f"⚠️ Hotel Pipeline execution issue: {error_detail}")
            else:
                print(f"❌ Hotel Pipeline execution failed with status {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error testing Hotel Pipeline execution: {e}")
            return False
        
        return True
        
    finally:
        db.close()


def test_pipeline_status_api():
    """Test pipeline status retrieval through API"""
    print("\n📊 Testing Pipeline Status API...")
    
    base_url = "http://localhost:8000/api/v1"
    
    try:
        # Get all pipelines
        response = requests.get(f"{base_url}/etl-pipelines/")
        
        if response.status_code == 200:
            pipelines = response.json()
            print(f"✅ Retrieved {len(pipelines)} pipelines")
            
            for pipeline in pipelines:
                print(f"   - {pipeline['name']} (ID: {pipeline['id']}) - Active: {pipeline['is_active']}")
            
            return True
        else:
            print(f"❌ Failed to get pipelines: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to API - is the backend server running?")
        return False
    except Exception as e:
        print(f"❌ Error testing pipeline status API: {e}")
        return False


def main():
    """Main test function"""
    print("🚀 Testing Pipeline Execution UI/API")
    print("=" * 60)
    
    # Test pipeline status API
    status_success = test_pipeline_status_api()
    
    if not status_success:
        print("\n💥 Pipeline status API test failed - cannot proceed")
        return False
    
    # Test pipeline execution
    execution_success = test_api_pipeline_execution()
    
    print("\n" + "=" * 60)
    print("📊 Pipeline UI/API Test Results:")
    print(f"   Status API: {'✅ PASS' if status_success else '❌ FAIL'}")
    print(f"   Execution API: {'✅ PASS' if execution_success else '❌ FAIL'}")
    
    if status_success and execution_success:
        print("\n🎉 All pipeline UI/API tests passed!")
        print("\n💡 The web interface should now work correctly:")
        print("   - Pipeline status is properly tracked")
        print("   - Duplicate executions are prevented")
        print("   - Stale pipeline runs are automatically cleaned up")
        return True
    else:
        print("\n💥 Some pipeline UI/API tests failed.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
