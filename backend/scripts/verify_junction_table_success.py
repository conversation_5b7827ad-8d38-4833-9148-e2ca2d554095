#!/usr/bin/env python3
"""
Final verification script for product_category_product junction table functionality
"""
import sys
import os
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import Product, ProductCategory, ProductCategoryProduct, ETLPipelineRun, Hotel


def main():
    """Main verification function"""
    print("🔍 FINAL JUNCTION TABLE VERIFICATION")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Check latest Room Type Pipeline run
        latest_run = db.query(ETLPipelineRun).filter(
            ETLPipelineRun.pipeline_id == 3
        ).order_by(ETLPipelineRun.started_at.desc()).first()
        
        print("=== LATEST ROOM TYPE PIPELINE RUN ===")
        if latest_run:
            duration = 'N/A'
            if latest_run.completed_at and latest_run.started_at:
                duration = f'{(latest_run.completed_at - latest_run.started_at).total_seconds():.1f}s'
            
            print(f'Run {latest_run.id}: {latest_run.status} | Duration: {duration}')
            print(f'Records: Extract={latest_run.records_extracted}, Transform={latest_run.records_transformed}, Load={latest_run.records_loaded}')
            
            if latest_run.error_message:
                print(f'ERROR: {latest_run.error_message}')
        
        # Check current counts
        product_count = db.query(Product).count()
        product_with_categories = db.query(Product).filter(Product.category_id.isnot(None)).count()
        junction_count = db.query(ProductCategoryproduct).count()
        hotel_count = db.query(Hotel).count()
        category_count = db.query(ProductCategory).count()
        
        print(f'\\n=== DATABASE SUMMARY ===')
        print(f'hotel: {hotel_count}')
        print(f'Product Categories: {category_count}')
        print(f'product: {product_count} total, {product_with_categories} with categories')
        print(f'Junction Table Entries: {junction_count}')
        
        # Calculate success metrics
        if product_with_categories > 0:
            junction_coverage = (junction_count / product_with_categories) * 100
            print(f'Junction Coverage: {junction_coverage:.1f}% of product with categories')
        
        if junction_count > 0:
            print(f'\\n🎉 SUCCESS! Junction table populated!')
            
            # Show sample junction entries
            print(f'\\n=== SAMPLE JUNCTION TABLE ENTRIES ===')
            sample_entries = db.query(ProductCategoryproduct).limit(10).all()
            for i, entry in enumerate(sample_entries, 1):
                product = db.query(Product).filter(Product.id == entry.product_id).first()
                category = db.query(ProductCategory).filter(ProductCategory.id == entry.product_category_id).first()
                
                product_name = product.title if product else 'Unknown'
                category_name = category.name if category else 'Unknown'
                
                print(f'  {i:2d}. Product: {product_name[:40]:<40} → Category: {category_name}')
            
            # Verify data integrity
            print(f'\\n=== DATA INTEGRITY CHECKS ===')
            
            # Check for orphaned junction entries
            orphaned_product = db.query(ProductCategoryProduct).filter(
                ~ProductCategoryProduct.product_id.in_(
                    db.query(Product.id)
                )
            ).count()

            orphaned_categories = db.query(ProductCategoryProduct).filter(
                ~ProductCategoryProduct.product_category_id.in_(
                    db.query(ProductCategory.id)
                )
            ).count()
            
            print(f'Orphaned product references: {orphaned_product}')
            print(f'Orphaned category references: {orphaned_categories}')
            
            # Check for duplicate entries
            duplicate_entries = db.execute("""
                SELECT product_category_id, product_id, COUNT(*) as count
                FROM product_category_product 
                GROUP BY product_category_id, product_id 
                HAVING COUNT(*) > 1
            """).fetchall()
            
            print(f'Duplicate junction entries: {len(duplicate_entries)}')
            
            # Check if entries are from the latest run
            if latest_run and latest_run.status == 'completed':
                latest_run_product = db.query(Product).filter(Product.pipeline_run_id == latest_run.id).all()
                latest_run_product_ids = [p.id for p in latest_run_product]
                
                junction_from_latest = db.query(ProductCategoryproduct).filter(
                    ProductCategoryproduct.product_id.in_(latest_run_product_ids)
                ).count()
                
                print(f'\\n📊 Junction entries from latest run: {junction_from_latest}')
                print(f'product from latest run with categories: {len([p for p in latest_run_product if p.category_id])}')
                
                if junction_from_latest > 0:
                    print(f'\\n✅ JUNCTION TABLE AUTOMATION WORKING!')
                    print(f'   - Junction entries are being created automatically')
                    print(f'   - Data integrity is maintained')
                    print(f'   - Foreign key relationships are correct')
                else:
                    print(f'\\n⚠️ Junction entries not created for latest run')
            
            # Show category distribution
            print(f'\\n=== CATEGORY DISTRIBUTION ===')
            category_stats = db.execute("""
                SELECT pc.name, COUNT(pcp.product_id) as product_count
                FROM product_category pc
                LEFT JOIN product_category_product pcp ON pc.id = pcp.product_category_id
                GROUP BY pc.id, pc.name
                ORDER BY product_count DESC
                LIMIT 10
            """).fetchall()
            
            for category_name, product_count in category_stats:
                print(f'  {category_name[:40]:<40}: {product_count:3d} product')
            
            return True
            
        else:
            print(f'\\n❌ JUNCTION TABLE NOT POPULATED')
            
            if latest_run and latest_run.status == 'completed':
                print(f'\\nDiagnostic Information:')
                run_product = db.query(Product).filter(Product.pipeline_run_id == latest_run.id).count()
                run_product_with_categories = db.query(Product).filter(
                    Product.pipeline_run_id == latest_run.id,
                    Product.category_id.isnot(None)
                ).count()
                print(f'  - product from latest run: {run_product}')
                print(f'  - product with categories: {run_product_with_categories}')
                print(f'  - Expected junction entries: {run_product_with_categories}')
                print(f'  - Actual junction entries: {junction_count}')
            
            return False
        
    except Exception as e:
        print(f'❌ Error during verification: {e}')
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()


if __name__ == "__main__":
    success = main()
    print(f'\\n{"="*60}')
    if success:
        print("🎉 JUNCTION TABLE IMPLEMENTATION: SUCCESS!")
        print("✅ Many-to-many relationship established")
        print("✅ Automatic population working")
        print("✅ Data integrity maintained")
    else:
        print("💥 JUNCTION TABLE IMPLEMENTATION: NEEDS ATTENTION")
    
    sys.exit(0 if success else 1)
