#!/usr/bin/env python3
"""
Simple test script to isolate Room Type Pipeline extraction issues
"""
import sys
import os
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun
from app.etl.extractors.salesforce_extractor import SalesforceExtractor


def test_room_type_pipeline_extraction():
    """Test Room Type Pipeline data extraction step by step"""
    print("🧪 TESTING ROOM TYPE PIPELINE EXTRACTION")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        # Get Room Type Pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Room Type Pipeline").first()
        if not pipeline:
            print("❌ Room Type Pipeline not found")
            return False
        
        print(f"✅ Found pipeline: {pipeline.name}")
        print(f"   Source object: {pipeline.source_object}")
        print(f"   Source fields: {pipeline.source_fields}")
        print(f"   Destination table: {pipeline.destination_table}")
        
        # Test Salesforce extraction directly
        print("\n📊 Testing Salesforce extraction...")
        extractor = SalesforceExtractor()
        
        if not extractor.authenticate():
            print("❌ Salesforce authentication failed")
            return False
        
        print("✅ Salesforce authentication successful")
        
        # Extract data using pipeline configuration
        try:
            data = extractor.extract_records(
                object_name=pipeline.source_object,
                fields=pipeline.source_fields or [],
                limit=5  # Small limit for testing
            )
            
            print(f"✅ Extracted {len(data)} records from {pipeline.source_object}")
            
            if data:
                print("\n📋 Sample extracted record:")
                sample = data[0]
                for key, value in sample.items():
                    value_str = str(value)[:100] + "..." if len(str(value)) > 100 else str(value)
                    print(f"   {key}: {value_str}")
                
                return True
            else:
                print("⚠️ No data extracted")
                return False
                
        except Exception as e:
            print(f"❌ Error during extraction: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    finally:
        db.close()


def test_room_pipeline_extraction():
    """Test Room Pipeline data extraction step by step"""
    print("\n🧪 TESTING ROOM PIPELINE EXTRACTION")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        # Get Room Pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Room Pipeline").first()
        if not pipeline:
            print("❌ Room Pipeline not found")
            return False
        
        print(f"✅ Found pipeline: {pipeline.name}")
        print(f"   Source object: {pipeline.source_object}")
        print(f"   Source fields: {pipeline.source_fields}")
        print(f"   Destination table: {pipeline.destination_table}")
        
        # Test Salesforce extraction directly
        print("\n📊 Testing Salesforce extraction...")
        extractor = SalesforceExtractor()
        
        if not extractor.authenticate():
            print("❌ Salesforce authentication failed")
            return False
        
        print("✅ Salesforce authentication successful")
        
        # Extract data using pipeline configuration
        try:
            data = extractor.extract_records(
                object_name=pipeline.source_object,
                fields=pipeline.source_fields or [],
                limit=5  # Small limit for testing
            )
            
            print(f"✅ Extracted {len(data)} records from {pipeline.source_object}")
            
            if data:
                print("\n📋 Sample extracted record:")
                sample = data[0]
                for key, value in sample.items():
                    value_str = str(value)[:100] + "..." if len(str(value)) > 100 else str(value)
                    print(f"   {key}: {value_str}")
                
                return True
            else:
                print("⚠️ No data extracted")
                return False
                
        except Exception as e:
            print(f"❌ Error during extraction: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    finally:
        db.close()


if __name__ == "__main__":
    room_type_ok = test_room_type_pipeline_extraction()
    room_ok = test_room_pipeline_extraction()
    
    print(f"\n🎯 SUMMARY:")
    print(f"   Room Type Pipeline extraction: {'✅ OK' if room_type_ok else '❌ FAILED'}")
    print(f"   Room Pipeline extraction: {'✅ OK' if room_ok else '❌ FAILED'}")
    
    if room_type_ok and room_ok:
        print("\n✅ Both pipelines can extract data successfully!")
        print("   The issue is likely in the transformation or loading phase.")
    else:
        print("\n❌ Data extraction issues detected.")
        print("   Need to fix extraction before proceeding.")
