#!/usr/bin/env python3
"""
Test script to verify Room Type Pipeline with real Salesforce Room_Type__c data
"""
import sys
import os
import json
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun, Product, Hotel, ProductCategory
from app.etl.extractors.salesforce_extractor import SalesforceExtractor
from app.etl.pipelines.pipeline_strategies.salesforce_to_postgresql_strategy import SalesforceToPostgreSQLStrategy


def test_salesforce_room_type_access():
    """Test access to Salesforce Room_Type__c object"""
    print("🔌 Testing Salesforce Room_Type__c Access...")
    
    try:
        extractor = SalesforceExtractor()
        if not extractor.authenticate():
            print("❌ Salesforce authentication failed")
            return False
        
        # Test Room_Type__c object access
        try:
            room_type_info = extractor.get_object_info("Room_Type__c")
            print(f"✅ Room_Type__c object accessible - {room_type_info['field_count']} fields")
            
            # Show available fields
            print("📋 Available Room_Type__c fields:")
            for field in room_type_info['fields'][:10]:  # Show first 10 fields
                print(f"   - {field['name']} ({field['type']})")
            if len(room_type_info['fields']) > 10:
                print(f"   ... and {len(room_type_info['fields']) - 10} more fields")
            
            return True
        except Exception as e:
            print(f"❌ Error accessing Room_Type__c object: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Error connecting to Salesforce: {e}")
        return False


def test_room_type_pipeline_configuration():
    """Test Room Type Pipeline configuration"""
    print("\n🏗️ Testing Room Type Pipeline Configuration...")
    
    db = SessionLocal()
    try:
        # Get the Room Type Pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Room Type Pipeline").first()
        if not pipeline:
            print("❌ Room Type Pipeline not found")
            return False
        
        print(f"✅ Found Room Type Pipeline (ID: {pipeline.id})")
        print(f"   Source Object: {pipeline.source_object}")
        print(f"   Destination Table: {pipeline.destination_table}")
        print(f"   Source Fields: {pipeline.source_fields}")
        print(f"   Destination Fields: {pipeline.destination_fields}")
        print(f"   Active: {pipeline.is_active}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing pipeline configuration: {e}")
        return False
    finally:
        db.close()


def test_room_type_data_extraction():
    """Test Room_Type__c data extraction and transformation"""
    print("\n🏨 Testing Room Type Data Extraction...")
    
    db = SessionLocal()
    try:
        # Get the Room Type Pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Room Type Pipeline").first()
        if not pipeline:
            print("❌ Room Type Pipeline not found")
            return False
        
        # Test Salesforce extraction
        extractor = SalesforceExtractor()
        if not extractor.authenticate():
            print("❌ Salesforce authentication failed")
            return False
        
        # Extract sample Room_Type__c records
        print("🔄 Extracting sample Room_Type__c records...")
        sample_records = extractor.extract_records(
            object_name="Room_Type__c",
            fields=pipeline.source_fields,
            limit=3  # Just get 3 records for testing
        )
        
        print(f"✅ Extracted {len(sample_records)} Room_Type__c records")
        
        if sample_records:
            print("📋 Sample record structure:")
            sample_record = sample_records[0]
            for field, value in sample_record.items():
                value_str = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                print(f"   {field}: {value_str}")
            
            # Test transformation logic
            print("\n🔄 Testing transformation logic...")
            strategy = SalesforceToPostgreSQLStrategy(db)
            
            # Create a test pipeline run
            run = ETLPipelineRun(
                pipeline_id=pipeline.id,
                status="running",
                logs=[]
            )
            db.add(run)
            db.commit()
            db.refresh(run)
            
            # Test transformation
            transformed_data = strategy._transform_data(pipeline, run, sample_records)
            print(f"✅ Transformed {len(transformed_data)} records")
            
            if transformed_data:
                print("📋 Sample transformed record:")
                sample_transformed = transformed_data[0]
                for field, value in sample_transformed.items():
                    value_str = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                    print(f"   {field}: {value_str}")
            
            # Clean up test run
            run.status = "completed"
            run.completed_at = datetime.utcnow()
            db.commit()
            
            return True
        else:
            print("⚠️ No Room_Type__c records found in Salesforce")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Room Type data extraction: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()


def test_hotel_relationship_resolution():
    """Test hotel relationship resolution"""
    print("\n🔗 Testing Hotel Relationship Resolution...")
    
    db = SessionLocal()
    try:
        # Check if we have hotel in the database
        hotel_count = db.query(Hotel).count()
        print(f"📊 Found {hotel_count} hotel in database")
        
        if hotel_count == 0:
            print("⚠️ No hotel found - run Hotel Pipeline first to populate hotel")
            return False
        
        # Show sample hotel
        sample_hotel = db.query(Hotel).limit(3).all()
        print("📋 Sample hotel for relationship testing:")
        for hotel in sample_hotel:
            print(f"   - {hotel.name} (ID: {hotel.id}, Category: {hotel.category_id})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing hotel relationships: {e}")
        return False
    finally:
        db.close()


def test_room_type_pipeline_execution():
    """Test full Room Type Pipeline execution"""
    print("\n🚀 Testing Room Type Pipeline Execution...")
    
    db = SessionLocal()
    try:
        # Get the Room Type Pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Room Type Pipeline").first()
        if not pipeline:
            print("❌ Room Type Pipeline not found")
            return False
        
        # Check initial product count
        initial_count = db.query(Product).count()
        print(f"📊 Initial product count: {initial_count}")
        
        # Execute pipeline via API
        import requests
        try:
            response = requests.post(f"http://localhost:8000/api/v1/pipelines/{pipeline.id}/execute")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Room Type Pipeline execution started - Run ID: {result.get('run_id')}")
                
                # Wait a moment for execution
                import time
                time.sleep(5)
                
                # Check final product count
                final_count = db.query(Product).count()
                print(f"📊 Final product count: {final_count}")
                
                if final_count > initial_count:
                    print(f"✅ Successfully loaded {final_count - initial_count} new product")
                    
                    # Show sample product
                    sample_product = db.query(Product).order_by(Product.created_at.desc()).limit(3).all()
                    print("📋 Sample product loaded:")
                    for product in sample_product:
                        print(f"   - {product.title} (ID: {product.id})")
                        print(f"     Hotel: {product.hotel_name} (ID: {product.hotel_id})")
                        print(f"     Category: {product.category_id}")
                        print(f"     Active: {product.is_active}")
                        print()
                    
                    return True
                else:
                    print("⚠️ No new product were loaded")
                    return False
                
            elif response.status_code == 400:
                error_detail = response.json().get('detail', 'Unknown error')
                print(f"⚠️ Pipeline execution issue: {error_detail}")
                return False
            else:
                print(f"❌ Pipeline execution failed with status {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            print("❌ Could not connect to API - is the backend server running?")
            return False
            
    except Exception as e:
        print(f"❌ Error testing pipeline execution: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()


def main():
    """Main test function"""
    print("🚀 Testing Room Type Pipeline")
    print("=" * 60)
    
    # Test Salesforce access
    salesforce_success = test_salesforce_room_type_access()
    
    # Test pipeline configuration
    config_success = test_room_type_pipeline_configuration()
    
    # Test data extraction
    extraction_success = test_room_type_data_extraction() if salesforce_success else False
    
    # Test hotel relationships
    relationship_success = test_hotel_relationship_resolution()
    
    # Test full pipeline execution
    execution_success = test_room_type_pipeline_execution() if all([
        salesforce_success, config_success, relationship_success
    ]) else False
    
    print("\n" + "=" * 60)
    print("📊 Room Type Pipeline Test Results:")
    print(f"   Salesforce Access: {'✅ PASS' if salesforce_success else '❌ FAIL'}")
    print(f"   Pipeline Configuration: {'✅ PASS' if config_success else '❌ FAIL'}")
    print(f"   Data Extraction: {'✅ PASS' if extraction_success else '❌ FAIL'}")
    print(f"   Hotel Relationships: {'✅ PASS' if relationship_success else '❌ FAIL'}")
    print(f"   Pipeline Execution: {'✅ PASS' if execution_success else '❌ FAIL'}")
    
    if all([salesforce_success, config_success, extraction_success, relationship_success, execution_success]):
        print("\n🎉 All Room Type Pipeline tests passed!")
        print("\n💡 The Room Type Pipeline is ready for production use!")
        return True
    else:
        print("\n💥 Some Room Type Pipeline tests failed.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
