#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create the Room Type Pipeline configuration in the database
"""
import sys
import os
import json
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import Session<PERSON><PERSON>al
from app.models.etl_pipeline import ETLPipeline


def create_room_type_pipeline():
    """Create the Room Type Pipeline configuration"""
    print("🏗️ Creating Room Type Pipeline configuration...")
    
    db = SessionLocal()
    try:
        # Check if pipeline already exists
        existing_pipeline = db.query(ETLPipeline).filter(
            ETLPipeline.name == "Room Type Pipeline"
        ).first()
        
        if existing_pipeline:
            print("⚠️ Room Type Pipeline already exists. Updating configuration...")
            pipeline = existing_pipeline
        else:
            print("✅ Creating new Room Type Pipeline...")
            pipeline = ETLPipeline()
        
        # Configure pipeline
        pipeline.name = "Room Type Pipeline"
        pipeline.description = (
            "Extract room type data from Salesforce Room_Type__c object with hotel relationship "
            "resolution and load into PostgreSQL product table. Includes comprehensive field "
            "mapping, hotel name resolution, and category inheritance from related hotel."
        )
        
        # Source configuration
        pipeline.source_type = "salesforce"
        pipeline.source_object = "Room_Type__c"
        pipeline.source_fields = [
            "Id",
            "Name", 
            "CurrencyIsoCode",
            "CreatedDate",
            "LastModifiedDate",
            "IsDeleted",
            "Hotel__c",  # Reference to Hotel__c object
            "Description__c"  # Description field if available
        ]
        
        # Destination configuration
        pipeline.destination_type = "postgresql"
        pipeline.destination_table = "product"
        pipeline.destination_fields = {
            "title": "Name",
            "currency": "CurrencyIsoCode",
            "source_created_at": "CreatedDate",
            "source_updated_at": "LastModifiedDate",
            "is_active": "IsDeleted",
            "hotel_id": "Hotel__c",  # Will be resolved to hotel.id
            "description": "Description__c",
            "migrated_id": "Id",
            "external_id": "Id",
            "source_system": "salesforce"
        }
        
        # Transformation configuration
        pipeline.transformation_config = {
            "clean_nulls": False,
            "trim_whitespace": True,
            "field_transformations": {
                "is_active": {
                    "type": "invert_boolean",
                    "description": "Convert IsDeleted to is_active by inverting the boolean value",
                    "source_field": "IsDeleted"
                },
                "title": {
                    "type": "clean_text",
                    "description": "Clean and normalize room type title",
                    "source_field": "Name"
                },
                "description": {
                    "type": "clean_text",
                    "description": "Clean and normalize room type description",
                    "source_field": "Description__c"
                }
            },
            "hotel_relationship_resolution": {
                "enabled": True,
                "description": "Resolve hotel relationships and inherit category from hotel",
                "lookup_field": "Hotel__c",
                "target_fields": {
                    "hotel_name": "Name",
                    "hotel_id": "id",
                    "category_id": "category_id"
                },
                "lookup_object": "Hotel__c"
            }
        }
        
        # Schedule configuration (disabled by default)
        pipeline.schedule_config = {
            "cron": "0 3 * * *",  # Run at 3 AM daily
            "enabled": False,
            "timezone": "UTC"
        }
        
        # Pipeline is active
        pipeline.is_active = True
        
        # Set timestamps
        if not existing_pipeline:
            pipeline.created_at = datetime.utcnow()
        pipeline.updated_at = datetime.utcnow()
        
        # Save to database
        if not existing_pipeline:
            db.add(pipeline)
        
        db.commit()
        db.refresh(pipeline)
        
        print(f"✅ Room Type Pipeline created successfully!")
        print(f"   Pipeline ID: {pipeline.id}")
        print(f"   Source Object: {pipeline.source_object}")
        print(f"   Destination Table: {pipeline.destination_table}")
        print(f"   Source Fields: {len(pipeline.source_fields)} fields")
        print(f"   Destination Fields: {len(pipeline.destination_fields)} mappings")
        print(f"   Active: {pipeline.is_active}")
        
        return pipeline
        
    except Exception as e:
        print(f"❌ Error creating Room Type Pipeline: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def verify_pipeline_dependencies():
    """Verify that required dependencies exist"""
    print("\n🔍 Verifying pipeline dependencies...")
    
    db = SessionLocal()
    try:
        # Check if Hotel Pipeline exists
        hotel_pipeline = db.query(ETLPipeline).filter(
            ETLPipeline.name == "Hotel Pipeline"
        ).first()
        
        if not hotel_pipeline:
            print("❌ Hotel Pipeline not found - Room Type Pipeline depends on hotel existing")
            return False
        
        print("✅ Hotel Pipeline found")
        
        # Check if Resort Pipeline exists
        resort_pipeline = db.query(ETLPipeline).filter(
            ETLPipeline.name == "Resort Pipeline"
        ).first()
        
        if not resort_pipeline:
            print("❌ Resort Pipeline not found - hotel depend on destination existing")
            return False
        
        print("✅ Resort Pipeline found")
        print("✅ All pipeline dependencies satisfied")
        
        return True
        
    except Exception as e:
        print(f"❌ Error verifying dependencies: {e}")
        return False
    finally:
        db.close()


def main():
    """Main function"""
    print("🚀 Room Type Pipeline Setup")
    print("=" * 50)
    
    # Verify dependencies
    if not verify_pipeline_dependencies():
        print("\n💥 Pipeline dependencies not satisfied. Please ensure Resort and Hotel pipelines exist.")
        return False
    
    # Create pipeline
    try:
        pipeline = create_room_type_pipeline()
        
        print("\n" + "=" * 50)
        print("🎉 Room Type Pipeline setup completed successfully!")
        print("\n📋 Next Steps:")
        print("1. Test the pipeline with real Salesforce Room_Type__c data")
        print("2. Verify hotel relationship resolution works correctly")
        print("3. Check that product are loaded with proper foreign keys")
        print("4. Enable scheduling if needed")
        
        return True
        
    except Exception as e:
        print(f"\n💥 Pipeline setup failed: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
