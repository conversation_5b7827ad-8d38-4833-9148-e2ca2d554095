#!/usr/bin/env python3
"""
Identify the exact 6 missing hotel records by analyzing pipeline logs and database state
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.config.settings import settings
import json
import re

def extract_hotel_names_from_logs():
    """Extract hotel names that the pipeline attempted to process from the latest run logs"""
    print("🔍 Extracting hotel names from pipeline logs...")
    
    engine = create_engine(settings.database_url)
    
    with engine.connect() as conn:
        try:
            # Get the most recent completed run with detailed logs
            latest_run = conn.execute(text("""
                SELECT pr.id, pr.logs, pr.records_loaded, pr.started_at
                FROM etl_pipeline_runs pr
                JOIN etl_pipelines p ON pr.pipeline_id = p.id
                WHERE p.name = 'Hotel Pipeline' 
                  AND pr.status = 'completed'
                  AND pr.records_loaded = 241
                ORDER BY pr.started_at DESC
                LIMIT 1
            """)).fetchone()
            
            if not latest_run:
                print("❌ No recent completed runs found")
                return []
            
            print(f"📋 Analyzing run {latest_run[0]} from {latest_run[3]}")
            
            # The logs are stored as a list, not JSON string
            logs = latest_run[1]
            if not logs:
                print("❌ No logs available")
                return []
            
            # Extract hotel names from log messages
            hotel_names = []
            
            # Look for patterns in log messages that contain hotel names
            for log_entry in logs:
                if isinstance(log_entry, dict):
                    message = log_entry.get('message', '')
                    
                    # Look for patterns like "Updated existing hotel: Hotel Name"
                    if 'Updated existing hotel:' in message:
                        # Extract hotel name after "Updated existing hotel: "
                        match = re.search(r'Updated existing hotel: (.+?) \(ID:', message)
                        if match:
                            hotel_name = match.group(1).strip()
                            hotel_names.append(hotel_name)
                    
                    # Look for patterns like "Created new hotel: Hotel Name"
                    elif 'Created new hotel:' in message:
                        match = re.search(r'Created new hotel: (.+?) \(ID:', message)
                        if match:
                            hotel_name = match.group(1).strip()
                            hotel_names.append(hotel_name)
                    
                    # Look for patterns like "Processing hotel record X/241: Hotel Name"
                    elif 'Processing hotel record' in message:
                        match = re.search(r'Processing hotel record \d+/\d+: (.+)', message)
                        if match:
                            hotel_name = match.group(1).strip().strip("'\"")
                            if hotel_name and hotel_name not in hotel_names:
                                hotel_names.append(hotel_name)
            
            print(f"✅ Extracted {len(hotel_names)} hotel names from logs")
            return hotel_names
            
        except Exception as e:
            print(f"❌ Error extracting from logs: {e}")
            return []

def get_database_hotel_names():
    """Get all hotel names currently in the database"""
    print("🔍 Getting hotel names from database...")
    
    engine = create_engine(settings.database_url)
    
    with engine.connect() as conn:
        try:
            result = conn.execute(text("""
                SELECT name FROM hotel ORDER BY name
            """)).fetchall()
            
            hotel_names = [row[0] for row in result]
            print(f"✅ Found {len(hotel_names)} hotels in database")
            return hotel_names
            
        except Exception as e:
            print(f"❌ Error querying database: {e}")
            return []

def analyze_sql_logs_for_hotel_names():
    """Extract hotel names from SQL UPDATE statements in the terminal output"""
    print("🔍 Analyzing SQL logs for hotel names...")
    
    # Since we can see the SQL logs in the terminal output, let's extract hotel names from UPDATE statements
    # This is a manual approach based on the logs we observed
    
    # From the logs, we can see UPDATE statements like:
    # UPDATE hotel SET name = %(name)s, ... WHERE id = %(hotel_id)s
    # with parameters like {'name': 'Hotel Les Aiglons', ...}
    
    # Let's create a more direct approach by running a test pipeline and capturing the hotel names
    print("📋 Running a quick test to extract hotel names from pipeline processing...")
    
    try:
        # Import the pipeline strategy to extract data directly
        from app.etl.pipelines.pipeline_strategies.salesforce_to_postgresql_strategy import SalesforceToPostgreSQLStrategy
        from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun
        from sqlalchemy.orm import sessionmaker
        
        engine = create_engine(settings.database_url)
        Session = sessionmaker(bind=engine)
        session = Session()
        
        try:
            # Get the Hotel Pipeline configuration
            pipeline = session.query(ETLPipeline).filter(ETLPipeline.name == "Hotel Pipeline").first()
            
            if not pipeline:
                print("❌ Hotel Pipeline not found")
                return []
            
            # Create strategy instance and extract data
            strategy = SalesforceToPostgreSQLStrategy(session)
            
            # Create a temporary run for extraction
            temp_run = ETLPipelineRun(pipeline_id=pipeline.id, status="running")
            
            # Extract the data using the same method as the pipeline
            salesforce_data = strategy._extract_data(pipeline, temp_run)
            
            # Transform the data to get hotel names
            transformed_data = strategy._transform_data(pipeline, temp_run, salesforce_data)
            
            hotel_names = [record.get('name', '').strip() for record in transformed_data if record.get('name')]
            hotel_names = [name for name in hotel_names if name]  # Remove empty names
            
            print(f"✅ Extracted {len(hotel_names)} hotel names from Salesforce")
            return hotel_names
            
        finally:
            session.close()
            
    except Exception as e:
        print(f"❌ Error extracting from pipeline: {e}")
        return []

def compare_and_identify_missing(salesforce_names, database_names):
    """Compare Salesforce and database hotel names to find missing ones"""
    print("\n🔍 Comparing Salesforce and Database hotel names...")
    
    # Convert to sets for comparison (case-insensitive)
    sf_names_lower = {name.lower().strip() for name in salesforce_names}
    db_names_lower = {name.lower().strip() for name in database_names}
    
    # Find missing hotels
    missing_in_db = sf_names_lower - db_names_lower
    extra_in_db = db_names_lower - sf_names_lower
    
    print(f"\n📊 Comparison Results:")
    print(f"   Salesforce hotels: {len(salesforce_names)}")
    print(f"   Database hotels: {len(database_names)}")
    print(f"   Missing in database: {len(missing_in_db)}")
    print(f"   Extra in database: {len(extra_in_db)}")
    
    if missing_in_db:
        print(f"\n❌ Hotels missing from database ({len(missing_in_db)}):")
        for i, name_lower in enumerate(sorted(missing_in_db), 1):
            # Find the original case version
            original_name = next((name for name in salesforce_names if name.lower().strip() == name_lower), name_lower)
            print(f"   {i}. {original_name}")
    
    if extra_in_db:
        print(f"\n➕ Extra hotels in database ({len(extra_in_db)}):")
        for i, name_lower in enumerate(sorted(extra_in_db), 1):
            # Find the original case version
            original_name = next((name for name in database_names if name.lower().strip() == name_lower), name_lower)
            print(f"   {i}. {original_name}")
    
    return missing_in_db, extra_in_db

def main():
    """Main analysis function"""
    print("🔍 Identifying Missing Hotel Records")
    print("=" * 60)
    
    # Method 1: Extract from pipeline logs
    log_hotel_names = extract_hotel_names_from_logs()
    
    # Method 2: Get current database state
    database_hotel_names = get_database_hotel_names()
    
    # Method 3: Extract directly from Salesforce via pipeline
    salesforce_hotel_names = analyze_sql_logs_for_hotel_names()
    
    if salesforce_hotel_names and database_hotel_names:
        missing_in_db, extra_in_db = compare_and_identify_missing(salesforce_hotel_names, database_hotel_names)
        
        print(f"\n🎯 Summary:")
        print(f"   Expected: {len(salesforce_hotel_names)} hotel records")
        print(f"   Actual: {len(database_hotel_names)} hotel records")
        print(f"   Missing: {len(missing_in_db)} records")
        
        if len(missing_in_db) == 6:
            print(f"\n✅ Found the exact 6 missing records!")
        else:
            print(f"\n⚠️  Expected 6 missing records, found {len(missing_in_db)}")
    
    else:
        print("❌ Failed to extract hotel names from one or both sources")

if __name__ == "__main__":
    main()
