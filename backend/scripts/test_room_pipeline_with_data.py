#!/usr/bin/env python3
"""
Test Room Pipeline with sample data setup
"""
import sys
import os
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import (
    ETLPipeline, ETLPipelineRun, Product, ProductVariant,
    generate_product_id, generate_product_variant_id
)


def setup_test_data():
    """Create sample product for testing Room Pipeline"""
    print("🔧 Setting up test data...")
    
    db = SessionLocal()
    try:
        # Clear existing data
        db.query(ProductVariants).delete()
        db.query(Product).delete()
        db.commit()

        # Create a dummy pipeline run for foreign key constraint
        dummy_run = ETLPipelineRun(
            pipeline_id=1,  # Assuming pipeline 1 exists
            status="completed",
            logs=[]
        )
        db.add(dummy_run)
        db.commit()
        db.refresh(dummy_run)

        # Create sample product
        sample_product = [
            {
                "migrated_id": "a0ba000000BoDqSAAV",
                "title": "Standard Room",
                "hotel_name": "Test Hotel 1"
            },
            {
                "migrated_id": "a0ba000000BoDqcAAF", 
                "title": "Deluxe Room",
                "hotel_name": "Test Hotel 2"
            },
            {
                "migrated_id": "a0ba000000BoDqhAAF",
                "title": "Suite",
                "hotel_name": "Test Hotel 3"
            }
        ]
        
        for product_data in sample_product:
            product = Product(
                id=generate_product_id(),
                migrated_id=product_data["migrated_id"],
                title=product_data["title"],
                hotel_name=product_data["hotel_name"],
                pipeline_run_id=dummy_run.id,
                extracted_at=datetime.utcnow(),
                source_system='salesforce',
                external_id=product_data["migrated_id"],
                is_active=True
            )
            db.add(product)
        
        db.commit()
        
        product_count = db.query(Product).count()
        print(f"✅ Created {product_count} test product")
        return True
        
    except Exception as e:
        print(f"❌ Error setting up test data: {e}")
        db.rollback()
        return False
    finally:
        db.close()


def test_room_pipeline_logic():
    """Test Room Pipeline logic directly"""
    print("\n🧪 Testing Room Pipeline Logic")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        # Get Room Pipeline
        room_pipeline = db.query(ETLPipeline).filter(
            ETLPipeline.name == "Room Pipeline"
        ).first()
        
        if not room_pipeline:
            print("❌ Room Pipeline not found")
            return False
        
        print(f"✅ Room Pipeline found (ID: {room_pipeline.id})")
        
        # Create a test pipeline run
        test_run = ETLPipelineRun(
            pipeline_id=room_pipeline.id,
            status="running",
            logs=[]
        )
        db.add(test_run)
        db.commit()
        db.refresh(test_run)
        
        # Test with sample Room__c data
        sample_room_data = [
            {
                "Id": "a0c000000001AAA",
                "Name": "Room 101",
                "Room_Type__c": "a0ba000000BoDqSAAV",  # Links to Standard Room
                "Room_Type__r": {"Name": "Standard Room"},
                "Is_Connected__c": True,
                "CreatedDate": "2023-01-01T10:00:00.000+0000",
                "LastModifiedDate": "2023-01-01T10:00:00.000+0000"
            },
            {
                "Id": "a0c000000001BBB", 
                "Name": "Room 201",
                "Room_Type__c": "a0ba000000BoDqcAAF",  # Links to Deluxe Room
                "Room_Type__r": {"Name": "Deluxe Room"},
                "Is_Connected__c": False,
                "CreatedDate": "2023-01-01T10:00:00.000+0000",
                "LastModifiedDate": "2023-01-01T10:00:00.000+0000"
            },
            {
                "Id": "a0c000000001CCC",
                "Name": "Room 301", 
                "Room_Type__c": "a0ba000000BoDqhAAF",  # Links to Suite
                "Room_Type__r": {"Name": "Suite"},
                "Is_Connected__c": True,
                "CreatedDate": "2023-01-01T10:00:00.000+0000",
                "LastModifiedDate": "2023-01-01T10:00:00.000+0000"
            }
        ]
        
        # Import the strategy to test the loading logic
        from app.etl.pipelines.pipeline_strategies.salesforce_to_postgresql_strategy import SalesforceToPostgreSQLStrategy
        
        strategy = SalesforceToPostgreSQLStrategy(db)
        loaded_count = strategy._load_to_product_variant(test_run, sample_room_data)
        
        print(f"✅ Loaded {loaded_count} product variants")
        
        # Verify results
        variant_count = db.query(ProductVariant).count()
        print(f"📊 Total product variants in database: {variant_count}")
        
        if variant_count > 0:
            print(f"\n📋 Created Product Variants:")
            variants = db.query(ProductVariants).all()
            for variant in variants:
                product_name = "Unknown"
                if variant.product:
                    product_name = variant.product.title
                
                print(f"   - {variant.name} (ID: {variant.id})")
                print(f"     Product: {product_name}")
                print(f"     Connected: {variant.is_connected}")
                print(f"     Room Config: {variant.room_config_name}")
        
        # Check relationships
        variants_with_product = db.query(ProductVariants).filter(
            ProductVariants.product_id.isnot(None)
        ).count()
        
        print(f"\n🔗 Relationship Analysis:")
        print(f"   Variants with Product links: {variants_with_product}/{variant_count}")
        
        success = variant_count == len(sample_room_data) and variants_with_product > 0
        
        if success:
            print(f"✅ Room Pipeline logic test PASSED")
        else:
            print(f"❌ Room Pipeline logic test FAILED")
        
        return success
        
    except Exception as e:
        print(f"❌ Error testing Room Pipeline logic: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()


def test_room_pipeline_api():
    """Test Room Pipeline via API"""
    print(f"\n🌐 Testing Room Pipeline API")
    print("=" * 50)
    
    try:
        import requests
        
        # Clear existing variants for clean test
        db = SessionLocal()
        try:
            db.query(ProductVariants).delete()
            db.commit()
            print("🧹 Cleared existing product variants")
        finally:
            db.close()
        
        # Execute Room Pipeline via API
        response = requests.post("http://localhost:8000/api/v1/pipelines/4/execute")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API call successful: {result}")
            return True
        else:
            print(f"❌ API call failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing API: {e}")
        return False


def main():
    """Main test function"""
    print("🧪 ROOM PIPELINE COMPREHENSIVE TEST")
    print("=" * 50)
    
    # Step 1: Setup test data
    setup_success = setup_test_data()
    if not setup_success:
        print("💥 Test data setup failed")
        return False
    
    # Step 2: Test Room Pipeline logic directly
    logic_success = test_room_pipeline_logic()
    
    # Step 3: Test Room Pipeline via API
    api_success = test_room_pipeline_api()
    
    print("\n" + "=" * 50)
    print("📊 ROOM PIPELINE TEST RESULTS:")
    print(f"   Test Data Setup: {'✅ PASS' if setup_success else '❌ FAIL'}")
    print(f"   Pipeline Logic: {'✅ PASS' if logic_success else '❌ FAIL'}")
    print(f"   API Integration: {'✅ PASS' if api_success else '❌ FAIL'}")
    
    overall_success = all([setup_success, logic_success, api_success])
    
    if overall_success:
        print("\n🎉 ALL ROOM PIPELINE TESTS PASSED!")
        print("\n✅ Verified Features:")
        print("   - Room Pipeline configuration")
        print("   - Product variants table population")
        print("   - Foreign key relationships")
        print("   - API integration")
        print("\n🚀 Room Pipeline is ready for production!")
    else:
        print("\n💥 SOME TESTS FAILED")
        if logic_success:
            print("✅ Core logic works - API issues may be temporary")
        else:
            print("❌ Core logic issues need to be addressed")
    
    return overall_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
