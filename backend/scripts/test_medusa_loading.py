#!/usr/bin/env python3
"""
Test the Medusa loading logic directly
"""
import sys
import os
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun
from app.etl.pipelines.pipeline_strategies.salesforce_to_postgresql_strategy import SalesforceToPostgreSQLStrategy
from sqlalchemy import text


def test_medusa_product_loading():
    """Test loading data to Medusa product table"""
    print("🧪 TESTING MEDUSA PRODUCT LOADING")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Create test run
        run = ETLPipelineRun(
            pipeline_id=3,  # Room Type Pipeline
            status="running",
            logs=[]
        )
        db.add(run)
        db.commit()
        db.refresh(run)
        
        print(f"✅ Created test run: {run.id}")
        
        # Create sample transformed data for Medusa product table
        sample_data = [
            {
                'title': 'Test Room Type 1',
                'description': 'A test room type for Medusa integration',
                'external_id': 'test_room_type_001',
                'status': 'published',
                'handle': 'test-room-type-1',
                'is_giftcard': False,
                'discountable': True,
                'metadata': {
                    'salesforce_id': 'test_room_type_001',
                    'currency_code': 'GBP',
                    'hotel_id': 'test_hotel_001',
                    'source_system': 'salesforce',
                    'is_active': True
                }
            },
            {
                'title': 'Test Room Type 2',
                'description': 'Another test room type for Medusa integration',
                'external_id': 'test_room_type_002',
                'status': 'published',
                'handle': 'test-room-type-2',
                'is_giftcard': False,
                'discountable': True,
                'metadata': {
                    'salesforce_id': 'test_room_type_002',
                    'currency_code': 'EUR',
                    'hotel_id': 'test_hotel_002',
                    'source_system': 'salesforce',
                    'is_active': True
                }
            }
        ]
        
        print(f"📦 Prepared {len(sample_data)} test records")
        
        # Test loading
        strategy = SalesforceToPostgreSQLStrategy(db)
        
        print("\n🔄 Testing Medusa product loading...")
        try:
            loaded_count = strategy._load_to_medusa_product(run, sample_data)
            print(f"✅ Loaded {loaded_count} records to Medusa product table")
            
            # Verify the data was loaded
            result = db.execute(text("""
                SELECT id, title, external_id, status, metadata->>'salesforce_id' as sf_id
                FROM product 
                WHERE external_id LIKE 'test_room_type_%'
                ORDER BY created_at DESC
            """))
            loaded_products = result.fetchall()
            
            print(f"✅ Verified {len(loaded_products)} products in Medusa table:")
            for product in loaded_products:
                print(f"  - {product[1]} (ID: {product[0]}, SF: {product[4]})")
            
            # Clean up test data
            db.execute(text("DELETE FROM product WHERE external_id LIKE 'test_room_type_%'"))
            db.commit()
            print("🧹 Cleaned up test data")
            
            return loaded_count > 0
            
        except Exception as e:
            print(f"❌ Error during Medusa product loading: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    finally:
        db.close()


def test_medusa_product_variant_loading():
    """Test loading data to Medusa product_variant table"""
    print("\n🧪 TESTING MEDUSA PRODUCT_VARIANT LOADING")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # First, create a test product to reference
        test_product_id = "prod_test_room_type_001"
        db.execute(text("""
            INSERT INTO product (id, title, handle, status, is_giftcard, discountable, created_at, updated_at)
            VALUES (:id, :title, :handle, :status, :is_giftcard, :discountable, :created_at, :updated_at)
            ON CONFLICT (id) DO NOTHING
        """), {
            'id': test_product_id,
            'title': 'Test Product for Variants',
            'handle': 'test-product-for-variants',
            'status': 'published',
            'is_giftcard': False,
            'discountable': True,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        })
        db.commit()
        print(f"✅ Created test product: {test_product_id}")
        
        # Create test run
        run = ETLPipelineRun(
            pipeline_id=4,  # Room Pipeline
            status="running",
            logs=[]
        )
        db.add(run)
        db.commit()
        db.refresh(run)
        
        print(f"✅ Created test run: {run.id}")
        
        # Create sample transformed data for Medusa product_variant table
        sample_data = [
            {
                'title': 'Test Room 1',
                'product_id': test_product_id,
                'allow_backorder': False,
                'manage_inventory': False,
                'metadata': {
                    'salesforce_id': 'test_room_001',
                    'room_type_id': 'test_room_type_001',
                    'is_connected': True,
                    'source_system': 'salesforce',
                    'is_active': True
                }
            },
            {
                'title': 'Test Room 2',
                'product_id': test_product_id,
                'allow_backorder': False,
                'manage_inventory': False,
                'metadata': {
                    'salesforce_id': 'test_room_002',
                    'room_type_id': 'test_room_type_001',
                    'is_connected': False,
                    'source_system': 'salesforce',
                    'is_active': True
                }
            }
        ]
        
        print(f"📦 Prepared {len(sample_data)} test records")
        
        # Test loading
        strategy = SalesforceToPostgreSQLStrategy(db)
        
        print("\n🔄 Testing Medusa product_variant loading...")
        try:
            loaded_count = strategy._load_to_medusa_product_variant(run, sample_data)
            print(f"✅ Loaded {loaded_count} records to Medusa product_variant table")
            
            # Verify the data was loaded
            result = db.execute(text("""
                SELECT id, title, product_id, metadata->>'salesforce_id' as sf_id
                FROM product_variant 
                WHERE metadata->>'salesforce_id' LIKE 'test_room_%'
                ORDER BY created_at DESC
            """))
            loaded_variants = result.fetchall()
            
            print(f"✅ Verified {len(loaded_variants)} product variants in Medusa table:")
            for variant in loaded_variants:
                print(f"  - {variant[1]} (ID: {variant[0]}, Product: {variant[2]}, SF: {variant[3]})")
            
            # Clean up test data
            db.execute(text("DELETE FROM product_variant WHERE metadata->>'salesforce_id' LIKE 'test_room_%'"))
            db.execute(text("DELETE FROM product WHERE id = :id"), {'id': test_product_id})
            db.commit()
            print("🧹 Cleaned up test data")
            
            return loaded_count > 0
            
        except Exception as e:
            print(f"❌ Error during Medusa product_variant loading: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    finally:
        db.close()


if __name__ == "__main__":
    print("🚀 TESTING MEDUSA LOADING LOGIC")
    print("=" * 70)
    
    product_ok = test_medusa_product_loading()
    variant_ok = test_medusa_product_variant_loading()
    
    print(f"\n🎯 LOADING SUMMARY:")
    print(f"   Medusa product loading: {'✅ OK' if product_ok else '❌ FAILED'}")
    print(f"   Medusa product_variant loading: {'✅ OK' if variant_ok else '❌ FAILED'}")
    
    if product_ok and variant_ok:
        print("\n✅ MEDUSA LOADING SUCCESS!")
        print("   Both loading methods work correctly with Medusa tables.")
    else:
        print("\n❌ MEDUSA LOADING ISSUES")
        print("   Check the individual test results above for details.")
