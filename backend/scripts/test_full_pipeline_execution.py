#!/usr/bin/env python3
"""
Test full pipeline execution for Room Type and Room pipelines
"""
import sys
import os
import time
import requests
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun, Product, ProductVariant


def test_room_type_pipeline_execution():
    """Test Room Type Pipeline full execution via API"""
    print("🧪 TESTING ROOM TYPE PIPELINE FULL EXECUTION")
    print("=" * 50)
    
    try:
        # Start pipeline execution
        print("🚀 Starting Room Type Pipeline...")
        response = requests.post("http://localhost:8000/api/v1/pipelines/3/execute", timeout=10)
        
        if response.status_code != 200:
            print(f"❌ Failed to start pipeline: {response.status_code}")
            print(f"Response: {response.text}")
            return False
        
        result = response.json()
        run_id = result.get('run_id')
        print(f"✅ Pipeline started: Run ID {run_id}")
        
        # Monitor execution
        print("⏳ Monitoring execution...")
        max_wait = 60  # 1 minute timeout
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            try:
                status_response = requests.get("http://localhost:8000/api/v1/pipelines/3/runs", timeout=5)
                if status_response.status_code == 200:
                    runs = status_response.json()
                    if runs:
                        latest_run = runs[0]
                        status = latest_run.get('status')
                        extracted = latest_run.get('records_extracted', 0)
                        transformed = latest_run.get('records_transformed', 0)
                        loaded = latest_run.get('records_loaded', 0)
                        
                        print(f"   Status: {status} | E:{extracted} T:{transformed} L:{loaded}")
                        
                        if status == 'completed':
                            print(f"✅ Pipeline completed successfully!")
                            print(f"   Records: {extracted} extracted → {transformed} transformed → {loaded} loaded")
                            return True
                        elif status == 'failed':
                            error = latest_run.get('error_message', 'Unknown error')
                            print(f"❌ Pipeline failed: {error}")
                            return False
                
                time.sleep(3)
                
            except Exception as e:
                print(f"⚠️ Monitoring error: {e}")
                time.sleep(3)
        
        print(f"⏰ Pipeline execution timeout after {max_wait} seconds")
        return False
        
    except Exception as e:
        print(f"❌ Error testing Room Type Pipeline: {e}")
        return False


def test_room_pipeline_execution():
    """Test Room Pipeline full execution via API"""
    print("\n🧪 TESTING ROOM PIPELINE FULL EXECUTION")
    print("=" * 50)
    
    try:
        # Start pipeline execution
        print("🚀 Starting Room Pipeline...")
        response = requests.post("http://localhost:8000/api/v1/pipelines/4/execute", timeout=10)
        
        if response.status_code != 200:
            print(f"❌ Failed to start pipeline: {response.status_code}")
            print(f"Response: {response.text}")
            return False
        
        result = response.json()
        run_id = result.get('run_id')
        print(f"✅ Pipeline started: Run ID {run_id}")
        
        # Monitor execution
        print("⏳ Monitoring execution...")
        max_wait = 60  # 1 minute timeout
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            try:
                status_response = requests.get("http://localhost:8000/api/v1/pipelines/4/runs", timeout=5)
                if status_response.status_code == 200:
                    runs = status_response.json()
                    if runs:
                        latest_run = runs[0]
                        status = latest_run.get('status')
                        extracted = latest_run.get('records_extracted', 0)
                        transformed = latest_run.get('records_transformed', 0)
                        loaded = latest_run.get('records_loaded', 0)
                        
                        print(f"   Status: {status} | E:{extracted} T:{transformed} L:{loaded}")
                        
                        if status == 'completed':
                            print(f"✅ Pipeline completed successfully!")
                            print(f"   Records: {extracted} extracted → {transformed} transformed → {loaded} loaded")
                            return True
                        elif status == 'failed':
                            error = latest_run.get('error_message', 'Unknown error')
                            print(f"❌ Pipeline failed: {error}")
                            return False
                
                time.sleep(3)
                
            except Exception as e:
                print(f"⚠️ Monitoring error: {e}")
                time.sleep(3)
        
        print(f"⏰ Pipeline execution timeout after {max_wait} seconds")
        return False
        
    except Exception as e:
        print(f"❌ Error testing Room Pipeline: {e}")
        return False


def verify_data_persistence():
    """Verify that data was actually loaded into the database"""
    print("\n🔍 VERIFYING DATA PERSISTENCE")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        # Check product (from Room Type Pipeline)
        product_count = db.query(Product).count()
        print(f"product in database: {product_count}")
        
        if product_count > 0:
            recent_product = db.query(Product).order_by(Product.created_at.desc()).limit(3).all()
            print("Recent product:")
            for product in recent_product:
                print(f"  - {product.title} (ID: {product.id})")
        
        # Check product variants (from Room Pipeline)
        variant_count = db.query(ProductVariant).count()
        print(f"Product variants in database: {variant_count}")
        
        if variant_count > 0:
            recent_variants = db.query(ProductVariant).order_by(ProductVariant.created_at.desc()).limit(3).all()
            print("Recent product variants:")
            for variant in recent_variants:
                print(f"  - {variant.name} (ID: {variant.id})")
        
        return product_count > 0 or variant_count > 0
        
    except Exception as e:
        print(f"❌ Error verifying data persistence: {e}")
        return False
    finally:
        db.close()


def check_server_status():
    """Check if the server is running"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        return response.status_code == 200
    except:
        return False


if __name__ == "__main__":
    print("🚀 TESTING FULL PIPELINE EXECUTION")
    print("=" * 60)
    
    # Check server status
    if not check_server_status():
        print("❌ Server is not running. Please start the server first.")
        print("   Run: uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
        sys.exit(1)
    
    print("✅ Server is running")
    
    # Test Room Type Pipeline
    room_type_ok = test_room_type_pipeline_execution()
    
    # Test Room Pipeline
    room_ok = test_room_pipeline_execution()
    
    # Verify data persistence
    data_ok = verify_data_persistence()
    
    print(f"\n🎯 EXECUTION SUMMARY:")
    print(f"   Room Type Pipeline: {'✅ OK' if room_type_ok else '❌ FAILED'}")
    print(f"   Room Pipeline: {'✅ OK' if room_ok else '❌ FAILED'}")
    print(f"   Data Persistence: {'✅ OK' if data_ok else '❌ FAILED'}")
    
    if room_type_ok and room_ok and data_ok:
        print("\n🎉 ALL TESTS PASSED!")
        print("   Both pipelines are working correctly with proper data loading.")
    else:
        print("\n❌ SOME TESTS FAILED")
        print("   Check the individual test results above for details.")
