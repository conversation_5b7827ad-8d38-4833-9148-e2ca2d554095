#!/usr/bin/env python3
"""
Test script to verify pipeline fixes
"""
import sys
import os
import json
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun, Destination, Hotel, ProductCategory
from app.services.etl_pipeline_service import ETLPipelineService


def test_resort_pipeline():
    """Test the resort pipeline with mock data"""
    print("🧪 Testing Resort Pipeline...")
    
    db = SessionLocal()
    try:
        # Get the resort pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Resort Pipeline").first()
        if not pipeline:
            print("❌ Resort Pipeline not found")
            return False
        
        print(f"✅ Found Resort Pipeline (ID: {pipeline.id})")
        
        # Create a test pipeline run
        run = ETLPipelineRun(
            pipeline_id=pipeline.id,
            status="running",
            logs=[]
        )
        db.add(run)
        db.commit()
        db.refresh(run)
        
        print(f"✅ Created test pipeline run (ID: {run.id})")
        
        # Create mock transformed data
        mock_data = [
            {
                "migrated_id": "test_resort_001",
                "name": "Test Resort 1",
                "country": "Test Country",
                "is_active": True,
                "currency": "USD",
                "description": "Test resort description",
                "source_created_at": datetime.utcnow(),
                "source_updated_at": datetime.utcnow(),
                "extracted_at": datetime.utcnow(),
                "source_system": "salesforce",
                "external_id": "test_resort_001"
            },
            {
                "migrated_id": "test_resort_002", 
                "name": "Test Resort 2",
                "country": "Test Country 2",
                "is_active": False,
                "currency": "EUR",
                "description": "Another test resort",
                "source_created_at": datetime.utcnow(),
                "source_updated_at": datetime.utcnow(),
                "extracted_at": datetime.utcnow(),
                "source_system": "salesforce",
                "external_id": "test_resort_002"
            }
        ]
        
        # Test the data loading logic directly
        from app.etl.pipelines.pipeline_strategies.salesforce_to_postgresql_strategy import SalesforceToPostgreSQLStrategy
        strategy = SalesforceToPostgreSQLStrategy(db)
        
        print("🔄 Testing data loading...")
        loaded_count = strategy._load_to_destination(run, mock_data)
        
        print(f"✅ Loaded {loaded_count} destination records")
        
        # Verify the data was actually saved
        destination = db.query(Destination).filter(Destination.pipeline_run_id == run.id).all()
        print(f"✅ Verified {len(destination)} destination in database")
        
        for dest in destination:
            print(f"   - {dest.name} ({dest.migrated_id}) - Active: {dest.is_active}")
        
        # Update run status
        run.status = "completed"
        run.completed_at = datetime.utcnow()
        run.records_loaded = loaded_count
        db.commit()
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing resort pipeline: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()


def test_hotel_pipeline():
    """Test the hotel pipeline with mock data"""
    print("\n🧪 Testing Hotel Pipeline...")
    
    db = SessionLocal()
    try:
        # Get the hotel pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Hotel Pipeline").first()
        if not pipeline:
            print("❌ Hotel Pipeline not found")
            return False
        
        print(f"✅ Found Hotel Pipeline (ID: {pipeline.id})")
        
        # Create a test pipeline run
        run = ETLPipelineRun(
            pipeline_id=pipeline.id,
            status="running",
            logs=[]
        )
        db.add(run)
        db.commit()
        db.refresh(run)
        
        print(f"✅ Created test pipeline run (ID: {run.id})")
        
        # Create mock transformed data
        mock_data = [
            {
                "migrated_id": "test_hotel_001",
                "name": "Test Hotel 1",
                "resort_id": "test_resort_001",  # Should link to resort created above
                "source_created_at": datetime.utcnow(),
                "source_updated_at": datetime.utcnow(),
                "extracted_at": datetime.utcnow(),
                "source_system": "salesforce",
                "external_id": "test_hotel_001",
                "is_active": True,
                "currency": "USD",
                "description": "Test hotel description",
                "hotel_name": "Test Hotel 1",
                "email": "<EMAIL>",
                "website": "https://hotel1.com"
            }
        ]
        
        # Test the data loading logic directly
        from app.etl.pipelines.pipeline_strategies.salesforce_to_postgresql_strategy import SalesforceToPostgreSQLStrategy
        strategy = SalesforceToPostgreSQLStrategy(db)
        
        print("🔄 Testing hotel data loading...")
        loaded_count = strategy._load_to_hotel(run, mock_data)
        
        print(f"✅ Loaded {loaded_count} hotel records")
        
        # Verify the data was actually saved
        hotel = db.query(Hotel).filter(Hotel.pipeline_run_id == run.id).all()
        categories = db.query(ProductCategory).filter(ProductCategory.pipeline_run_id == run.id).all()
        
        print(f"✅ Verified {len(hotel)} hotel and {len(categories)} categories in database")
        
        for hotel in hotel:
            print(f"   - Hotel: {hotel.name} (ID: {hotel.id}) - Destination: {hotel.destination_id}")
        
        for cat in categories:
            print(f"   - Category: {cat.name} (ID: {cat.id})")
        
        # Update run status
        run.status = "completed"
        run.completed_at = datetime.utcnow()
        run.records_loaded = loaded_count
        db.commit()
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing hotel pipeline: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()


def main():
    """Main test function"""
    print("🚀 Testing ETL Pipeline Fixes")
    print("=" * 50)
    
    # Test resort pipeline first
    resort_success = test_resort_pipeline()
    
    # Test hotel pipeline
    hotel_success = test_hotel_pipeline()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   Resort Pipeline: {'✅ PASS' if resort_success else '❌ FAIL'}")
    print(f"   Hotel Pipeline: {'✅ PASS' if hotel_success else '❌ FAIL'}")
    
    if resort_success and hotel_success:
        print("\n🎉 All pipeline fixes are working correctly!")
        return True
    else:
        print("\n💥 Some pipeline fixes need more work.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
