#!/usr/bin/env python3
"""
Update Room Type and Room Pipeline configurations to fix field mappings
"""
import sys
import os
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipeline


def update_room_type_pipeline():
    """Update Room Type Pipeline configuration"""
    print("🔧 UPDATING ROOM TYPE PIPELINE CONFIGURATION")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        # Get Room Type Pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Room Type Pipeline").first()
        if not pipeline:
            print("❌ Room Type Pipeline not found")
            return False
        
        print(f"✅ Found pipeline: {pipeline.name}")
        
        # Update destination fields (remove problematic _extracted_at mapping)
        pipeline.destination_fields = {
            "title": "Name",
            "currency_code": "CurrencyIsoCode",
            "source_created_at": "CreatedDate",
            "source_updated_at": "LastModifiedDate",
            "is_active": "IsDeleted",
            "hotel_id": "Hotel__c",  # Will be resolved to hotel.id
            "description": "Description__c",
            "migrated_id": "Id",
            "external_id": "Id",
            "source_system": "salesforce"
        }
        
        # Ensure table name is correct
        pipeline.destination_table = "product"
        
        db.commit()
        print("✅ Room Type Pipeline configuration updated")
        return True
        
    except Exception as e:
        print(f"❌ Error updating Room Type Pipeline: {e}")
        db.rollback()
        return False
    finally:
        db.close()


def update_room_pipeline():
    """Update Room Pipeline configuration"""
    print("\n🔧 UPDATING ROOM PIPELINE CONFIGURATION")
    print("=" * 50)

    db = SessionLocal()
    try:
        # Get Room Pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Room Pipeline").first()
        if not pipeline:
            print("❌ Room Pipeline not found")
            return False

        print(f"✅ Found pipeline: {pipeline.name}")

        # Update source fields to include IsDeleted and ensure Room_Type__r.Name is accessible
        pipeline.source_fields = [
            "Id",
            "Name",
            "Room_Type__c",
            "Room_Type__r.Name",
            "Is_Connected__c",
            "CreatedDate",
            "LastModifiedDate",
            "IsDeleted"
        ]

        # Update destination fields (remove auto_generated and true mappings)
        pipeline.destination_fields = {
            "migrated_id": "Id",
            "name": "Name",
            "product_id": "Room_Type__c",  # Will be resolved to product.id
            "room_config_name": "Room_Type__r.Name",
            "is_connected": "Is_Connected__c",
            "source_created_at": "CreatedDate",
            "source_updated_at": "LastModifiedDate",
            "source_system": "salesforce",
            "external_id": "Id",
            "is_active": "IsDeleted"  # Use actual Salesforce field, will be inverted
        }

        # Ensure table name is correct
        pipeline.destination_table = "product_variant"

        db.commit()
        print("✅ Room Pipeline configuration updated")
        return True

    except Exception as e:
        print(f"❌ Error updating Room Pipeline: {e}")
        db.rollback()
        return False
    finally:
        db.close()


def verify_configurations():
    """Verify the updated configurations"""
    print("\n🔍 VERIFYING UPDATED CONFIGURATIONS")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        # Check Room Type Pipeline
        room_type_pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Room Type Pipeline").first()
        if room_type_pipeline:
            print(f"Room Type Pipeline:")
            print(f"  Destination table: {room_type_pipeline.destination_table}")
            print(f"  Field mappings: {len(room_type_pipeline.destination_fields)} fields")
            for field, source in room_type_pipeline.destination_fields.items():
                print(f"    {field} ← {source}")
        
        # Check Room Pipeline
        room_pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Room Pipeline").first()
        if room_pipeline:
            print(f"\nRoom Pipeline:")
            print(f"  Destination table: {room_pipeline.destination_table}")
            print(f"  Field mappings: {len(room_pipeline.destination_fields)} fields")
            for field, source in room_pipeline.destination_fields.items():
                print(f"    {field} ← {source}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error verifying configurations: {e}")
        return False
    finally:
        db.close()


if __name__ == "__main__":
    print("🚀 UPDATING PIPELINE CONFIGURATIONS")
    print("=" * 60)
    
    room_type_ok = update_room_type_pipeline()
    room_ok = update_room_pipeline()
    
    if room_type_ok and room_ok:
        verify_configurations()
        print(f"\n✅ CONFIGURATION UPDATE COMPLETE")
        print("   Both pipelines have been updated with correct field mappings")
        print("   Table names verified: product, product_variant")
    else:
        print(f"\n❌ CONFIGURATION UPDATE FAILED")
        print("   Some pipelines could not be updated")
