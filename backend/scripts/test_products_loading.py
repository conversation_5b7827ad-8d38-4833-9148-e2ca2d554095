#!/usr/bin/env python3
"""
Test script to verify product loading logic directly
"""
import sys
import os
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun, Product, Hotel
from app.etl.pipelines.pipeline_strategies.salesforce_to_postgresql_strategy import SalesforceToPostgreSQLStrategy


def test_product_loading_directly():
    """Test product loading logic directly with mock data"""
    print("🧪 Testing product Loading Logic Directly...")
    
    db = SessionLocal()
    try:
        # Get the Room Type Pipeline
        pipeline = db.query(ETLPipeline).filter(ETLPipeline.name == "Room Type Pipeline").first()
        if not pipeline:
            print("❌ Room Type Pipeline not found")
            return False
        
        # Get a sample hotel for testing
        sample_hotel = db.query(Hotel).first()
        if not sample_hotel:
            print("❌ No hotel found for testing")
            return False
        
        print(f"✅ Using sample hotel: {sample_hotel.name} (ID: {sample_hotel.id})")

        # Create mock transformed data that would come from Room_Type__c
        mock_data = [
            {
                "migrated_id": "test_room_type_001",
                "title": "Test Suite",
                "description": "A test room type for verification",
                "hotel_id": sample_hotel.id,  # Use hotel's id for lookup
                "currency": "USD",
                "source_created_at": datetime.utcnow(),
                "source_updated_at": datetime.utcnow(),
                "extracted_at": datetime.utcnow(),
                "source_system": "salesforce",
                "external_id": "test_room_type_001",
                "is_active": True
            },
            {
                "migrated_id": "test_room_type_002",
                "title": "Test Standard Room",
                "description": "Another test room type",
                "hotel_id": sample_hotel.id,
                "currency": "EUR",
                "source_created_at": datetime.utcnow(),
                "source_updated_at": datetime.utcnow(),
                "extracted_at": datetime.utcnow(),
                "source_system": "salesforce",
                "external_id": "test_room_type_002",
                "is_active": True
            }
        ]
        
        # Create a test pipeline run
        run = ETLPipelineRun(
            pipeline_id=pipeline.id,
            status="running",
            logs=[]
        )
        db.add(run)
        db.commit()
        db.refresh(run)
        
        print(f"✅ Created test pipeline run (ID: {run.id})")
        
        # Test the product loading logic directly
        strategy = SalesforceToPostgreSQLStrategy(db)
        
        print("🔄 Testing _load_to_product method...")
        loaded_count = strategy._load_to_product(run, mock_data)
        
        print(f"✅ Loaded {loaded_count} product records")
        
        # Verify the product were actually saved
        product = db.query(Product).filter(Product.pipeline_run_id == run.id).all()
        print(f"✅ Verified {len(product)} product in database")
        
        for product in product:
            print(f"   - {product.title} (ID: {product.id})")
            print(f"     Hotel: {product.hotel_name} (ID: {product.hotel_id})")
            print(f"     Category: {product.category_id}")
            print(f"     Active: {product.is_active}")
            print()
        
        # Update run status
        run.status = "completed"
        run.completed_at = datetime.utcnow()
        run.records_loaded = loaded_count
        db.commit()
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing product loading: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()


def test_hotel_relationship_resolution():
    """Test hotel relationship resolution with real data"""
    print("\n🔗 Testing Hotel Relationship Resolution with Real Data...")
    
    db = SessionLocal()
    try:
        # Get Room_Type__c records with Hotel__c relationships
        from app.etl.extractors.salesforce_extractor import SalesforceExtractor
        
        extractor = SalesforceExtractor()
        if not extractor.authenticate():
            print("❌ Salesforce authentication failed")
            return False
        
        # Get a few Room_Type__c records with Hotel__c relationships
        records = extractor.extract_records(
            object_name='Room_Type__c',
            fields=['Id', 'Name', 'Hotel__c', 'CurrencyIsoCode', 'CreatedDate', 'LastModifiedDate', 'IsDeleted'],
            where_clause='Hotel__c != null',
            limit=3
        )
        
        if not records:
            print("❌ No Room_Type__c records with Hotel__c relationships found")
            return False
        
        print(f"✅ Found {len(records)} Room_Type__c records with hotel relationships")
        
        # Test hotel resolution for each record
        for i, record in enumerate(records):
            hotel_migrated_id = record.get('Hotel__c')
            hotel = db.query(Hotel).filter(Hotel.external_id == hotel_migrated_id).first()
            
            print(f"Record {i+1}: {record['Name']}")
            print(f"  Hotel__c: {hotel_migrated_id}")
            
            if hotel:
                print(f"  ✅ Resolved to: {hotel.name} (ID: {hotel.id}, Category: {hotel.category_id})")
            else:
                print(f"  ❌ No hotel found in database")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing hotel relationship resolution: {e}")
        return False
    finally:
        db.close()


def main():
    """Main test function"""
    print("🚀 Testing product Loading Logic")
    print("=" * 50)
    
    # Test product loading directly
    loading_success = test_product_loading_directly()
    
    # Test hotel relationship resolution
    relationship_success = test_hotel_relationship_resolution()
    
    print("\n" + "=" * 50)
    print("📊 product Loading Test Results:")
    print(f"   Direct Loading: {'✅ PASS' if loading_success else '❌ FAIL'}")
    print(f"   Hotel Relationships: {'✅ PASS' if relationship_success else '❌ FAIL'}")
    
    if loading_success and relationship_success:
        print("\n🎉 product loading logic is working correctly!")
        print("\n💡 To complete the Room Type Pipeline setup:")
        print("1. Restart the backend server to load the new _load_to_product method")
        print("2. Run the Room Type Pipeline through the web interface")
        print("3. Verify product are loaded with proper hotel relationships")
        return True
    else:
        print("\n💥 Some product loading tests failed.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
