#!/usr/bin/env python3
"""
Comprehensive fix for Room Pipeline issues:
1. Remove extraction limit to get all ~1000 Room__c records
2. Fix UI status display issues
3. Ensure complete data persistence verification
"""
import sys
import os
import time
import requests
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipelineRun, ProductVariants, Product


def fix_extraction_limit_in_strategy():
    """Remove the limit=20 parameter from Room Pipeline extraction"""
    print("🔧 FIXING EXTRACTION LIMIT IN PIPELINE STRATEGY")
    print("=" * 60)
    
    try:
        strategy_file = "app/services/pipeline_strategies/salesforce_to_postgresql_strategy.py"
        
        # Read current content
        with open(strategy_file, 'r') as f:
            content = f.read()
        
        # Find and fix the extraction limit
        old_extraction = 'room_data = self.salesforce_extractor.extract_records(\'Room__c\', room_fields, limit=500)'
        new_extraction = 'room_data = self.salesforce_extractor.extract_records(\'Room__c\', room_fields)'
        
        if old_extraction in content:
            content = content.replace(old_extraction, new_extraction)
            print("✅ Found and removed limit=500 parameter")
        else:
            # Check for other limit variations
            import re
            pattern = r'extract_records\(\'Room__c\', room_fields, limit=\d+\)'
            match = re.search(pattern, content)
            if match:
                old_line = match.group(0)
                new_line = "extract_records('Room__c', room_fields)"
                content = content.replace(old_line, new_line)
                print(f"✅ Found and removed limit parameter: {old_line}")
            else:
                print("⚠️ No extraction limit found - may already be unlimited")
        
        # Also check the direct method we've been using
        old_direct = 'room_data = extractor.extract_records(\'Room__c\', room_fields, limit=20)'
        new_direct = 'room_data = extractor.extract_records(\'Room__c\', room_fields)'
        
        if old_direct in content:
            content = content.replace(old_direct, new_direct)
            print("✅ Removed limit=20 from direct extraction")
        
        # Write back the updated content
        with open(strategy_file, 'w') as f:
            f.write(content)
        
        print("✅ Pipeline strategy updated to extract ALL Room__c records")
        return True
        
    except Exception as e:
        print(f"❌ Error fixing extraction limit: {e}")
        return False


def clear_stuck_ui_status():
    """Clear any stuck pipeline runs that are causing UI status issues"""
    print("\n🛑 CLEARING STUCK UI STATUS")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Find ALL running Room Pipeline runs
        stuck_runs = db.query(ETLPipelineRun).filter(
            ETLPipelineRun.pipeline_id == 4,
            ETLPipelineRun.status == 'running'
        ).all()
        
        if stuck_runs:
            print(f"Found {len(stuck_runs)} stuck Room Pipeline run(s)")
            
            for run in stuck_runs:
                runtime = datetime.utcnow() - run.started_at if run.started_at else None
                print(f"   Cancelling Run {run.id}: {runtime} runtime")
                
                run.status = 'cancelled'
                run.completed_at = datetime.utcnow()
                run.error_message = "Cancelled - clearing UI status issues"
            
            db.commit()
            print(f"✅ Cancelled {len(stuck_runs)} stuck run(s)")
        else:
            print("✅ No stuck runs found")
        
        # Also check for any runs with NULL status that might confuse the UI
        null_status_runs = db.query(ETLPipelineRun).filter(
            ETLPipelineRun.pipeline_id == 4,
            ETLPipelineRun.status.is_(None)
        ).all()
        
        if null_status_runs:
            for run in null_status_runs:
                run.status = 'failed'
                run.error_message = "Fixed NULL status"
            db.commit()
            print(f"✅ Fixed {len(null_status_runs)} runs with NULL status")
        
        return True
        
    except Exception as e:
        print(f"❌ Error clearing UI status: {e}")
        db.rollback()
        return False
    finally:
        db.close()


def get_total_room_records_available():
    """Check how many Room__c records are actually available in Salesforce"""
    print("\n📊 CHECKING TOTAL ROOM__C RECORDS AVAILABLE")
    print("=" * 60)
    
    try:
        from app.etl.extractors.salesforce_extractor import SalesforceExtractor
        
        extractor = SalesforceExtractor()
        extractor.authenticate()
        
        # Get count of total Room__c records
        print("📡 Querying Salesforce for total Room__c count...")
        
        # Extract just IDs to get total count
        room_ids = extractor.extract_records('Room__c', ['Id'])
        total_count = len(room_ids)
        
        print(f"✅ Total Room__c records available: {total_count}")
        
        if total_count < 100:
            print("⚠️ Lower than expected - may need to check Salesforce permissions")
        elif total_count > 500:
            print("✅ Large dataset confirmed - full extraction needed")
        
        return total_count
        
    except Exception as e:
        print(f"❌ Error checking Salesforce records: {e}")
        return 0


def execute_complete_room_pipeline():
    """Execute Room Pipeline to extract ALL Room__c records"""
    print("\n🚀 EXECUTING COMPLETE ROOM PIPELINE")
    print("=" * 60)
    
    try:
        # Get initial state
        db = SessionLocal()
        try:
            initial_variants = db.query(ProductVariants).count()
            total_product = db.query(Product).count()
            print(f"📊 Initial state:")
            print(f"   Product variants: {initial_variants}")
            print(f"   product available: {total_product}")
        finally:
            db.close()
        
        # Execute Room Pipeline
        print("📡 Starting complete Room Pipeline execution...")
        start_time = time.time()
        
        response = requests.post("http://localhost:8000/api/v1/pipelines/4/execute", timeout=15)
        
        if response.status_code != 200:
            print(f"❌ Failed to start: {response.status_code}")
            print(f"Response: {response.text}")
            return False
        
        result = response.json()
        run_id = result.get('run_id')
        print(f"✅ Started: Run ID {run_id}")
        
        # Extended monitoring for large dataset
        print("⏳ Extended monitoring (5 minute timeout for large dataset)...")
        
        max_wait = 300  # 5 minutes for ~1000 records
        check_interval = 10  # 10 seconds
        last_extracted = None
        last_transformed = None
        last_loaded = None
        
        while time.time() - start_time < max_wait:
            try:
                elapsed = time.time() - start_time
                
                status_response = requests.get("http://localhost:8000/api/v1/pipelines/4/runs", timeout=10)
                
                if status_response.status_code == 200:
                    runs = status_response.json()
                    if runs:
                        latest_run = runs[0]
                        status = latest_run.get('status')
                        extracted = latest_run.get('records_extracted')
                        transformed = latest_run.get('records_transformed')
                        loaded = latest_run.get('records_loaded')
                        error_msg = latest_run.get('error_message', '')
                        
                        # Show progress
                        print(f"   [{elapsed:3.0f}s] {status} | E:{extracted} T:{transformed} L:{loaded}")
                        
                        # Show progress changes
                        if extracted != last_extracted or transformed != last_transformed or loaded != last_loaded:
                            if extracted and extracted > 0:
                                print(f"   [{elapsed:3.0f}s] 📊 Extraction progress: {extracted} records")
                            if transformed and transformed > 0:
                                print(f"   [{elapsed:3.0f}s] 🔄 Transformation progress: {transformed} records")
                            if loaded and loaded > 0:
                                print(f"   [{elapsed:3.0f}s] 💾 Loading progress: {loaded} records")
                        
                        last_extracted = extracted
                        last_transformed = transformed
                        last_loaded = loaded
                        
                        if error_msg:
                            print(f"   [{elapsed:3.0f}s] ⚠️ Error: {error_msg}")
                        
                        # Check database changes
                        db = SessionLocal()
                        try:
                            current_variants = db.query(ProductVariants).count()
                            if current_variants != initial_variants:
                                print(f"   [{elapsed:3.0f}s] 🎉 Database: {current_variants} variants (+{current_variants - initial_variants})")
                        finally:
                            db.close()
                        
                        if status == 'completed':
                            print(f"✅ COMPLETED in {elapsed:.1f} seconds!")
                            print(f"📊 Final results: Extract:{extracted} Transform:{transformed} Load:{loaded}")
                            return True
                        elif status == 'failed':
                            print(f"❌ FAILED: {error_msg}")
                            return False
                
                time.sleep(check_interval)
                
            except Exception as e:
                print(f"⚠️ Monitoring error: {e}")
                time.sleep(check_interval)
        
        print(f"⏰ TIMEOUT after {max_wait} seconds")
        return False
        
    except Exception as e:
        print(f"❌ Error executing pipeline: {e}")
        return False


def verify_complete_data_persistence():
    """Verify that all Room__c records were properly persisted"""
    print("\n🔍 VERIFYING COMPLETE DATA PERSISTENCE")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Get final counts
        total_variants = db.query(ProductVariants).count()
        total_product = db.query(Product).count()
        
        print(f"📊 Final Database State:")
        print(f"   Total product variants: {total_variants}")
        print(f"   Total product: {total_product}")
        
        if total_variants == 0:
            print("❌ NO DATA PERSISTED")
            return False
        
        # Analyze relationships
        variants_with_product = db.query(ProductVariants).filter(
            ProductVariants.product_id.isnot(None)
        ).count()
        
        variants_without_product = total_variants - variants_with_product
        
        print(f"\n🔗 Relationship Analysis:")
        print(f"   Variants with product links: {variants_with_product}")
        print(f"   Variants without product links: {variants_without_product}")
        
        if total_variants > 0:
            link_rate = (variants_with_product / total_variants) * 100
            print(f"   Link success rate: {link_rate:.1f}%")
        
        # Check data distribution by product type
        from sqlalchemy import func
        product_distribution = db.query(
            Product.title,
            func.count(ProductVariants.id).label('variant_count')
        ).join(ProductVariants, Product.id == ProductVariants.product_id).group_by(
            Product.title
        ).order_by(func.count(ProductVariants.id).desc()).all()
        
        print(f"\n📊 Product Type Distribution:")
        for product_title, count in product_distribution[:10]:  # Top 10
            print(f"   {product_title}: {count} variants")
        
        # Check recent pipeline runs
        recent_runs = db.query(ETLPipelineRun).filter(
            ETLPipelineRun.pipeline_id == 4
        ).order_by(ETLPipelineRun.started_at.desc()).limit(3).all()
        
        print(f"\n📋 Recent Pipeline Runs:")
        for run in recent_runs:
            duration = "Unknown"
            if run.started_at and run.completed_at:
                duration = str(run.completed_at - run.started_at)
            
            print(f"   Run {run.id}: {run.status} | Duration: {duration}")
            print(f"      Records: E:{run.records_extracted} T:{run.records_transformed} L:{run.records_loaded}")
        
        # Success criteria
        success = total_variants >= 500  # Expecting ~1000, but at least 500
        
        if success:
            print(f"\n✅ DATA PERSISTENCE SUCCESS!")
            print(f"   {total_variants} Room__c records successfully persisted")
            print(f"   {link_rate:.1f}% successfully linked to product")
        else:
            print(f"\n⚠️ PARTIAL SUCCESS:")
            print(f"   {total_variants} records persisted (expected ~1000)")
        
        return success
        
    except Exception as e:
        print(f"❌ Error verifying data persistence: {e}")
        return False
    finally:
        db.close()


def main():
    """Main function to fix all Room Pipeline issues"""
    print("🎯 COMPREHENSIVE ROOM PIPELINE FIX")
    print("=" * 70)
    print(f"Timestamp: {datetime.utcnow()}")
    print()
    
    # Step 1: Fix extraction limit
    extraction_fix = fix_extraction_limit_in_strategy()
    
    # Step 2: Clear UI status issues
    ui_fix = clear_stuck_ui_status()
    
    # Step 3: Check available records
    total_available = get_total_room_records_available()
    
    # Step 4: Execute complete pipeline
    execution_success = execute_complete_room_pipeline()
    
    # Step 5: Verify complete data persistence
    verification_success = verify_complete_data_persistence()
    
    print("\n" + "=" * 70)
    print("📊 COMPREHENSIVE FIX RESULTS")
    print("=" * 70)
    
    results = [
        ("Extraction Limit Fix", extraction_fix),
        ("UI Status Fix", ui_fix),
        ("Pipeline Execution", execution_success),
        ("Data Persistence Verification", verification_success)
    ]
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for result_name, success in results:
        status = "✅" if success else "❌"
        print(f"{status} {result_name}")
    
    success_rate = (passed / total) * 100
    print(f"\nOverall Success Rate: {passed}/{total} ({success_rate:.1f}%)")
    print(f"Total Room__c records available: {total_available}")
    
    if execution_success and verification_success:
        print("\n🎉 ALL ROOM PIPELINE ISSUES RESOLVED!")
        print("\n✅ ACHIEVEMENTS:")
        print("   - Extraction limit removed - processing ALL Room__c records")
        print("   - UI status issues cleared - accurate pipeline state display")
        print("   - Complete data persistence verified")
        print("   - Foreign key relationships established")
        print("   - Production-ready Room Pipeline operational")
        
    elif passed >= 3:
        print("\n⚠️ MOSTLY SUCCESSFUL:")
        print("   - Major issues resolved")
        print("   - Minor issues may remain")
        
    else:
        print("\n💥 ISSUES REMAIN:")
        print("   - Manual intervention may be required")
        print("   - Check server logs for detailed errors")
    
    return execution_success and verification_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
