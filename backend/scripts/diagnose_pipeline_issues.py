#!/usr/bin/env python3
"""
Comprehensive diagnostic script for ETL pipeline execution issues
"""
import sys
import os
import psutil
import time
from datetime import datetime, timedelta

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipeline, ETLPipelineRun, Product, ProductVariants, Hotel, Destination, ProductCategory


def check_pipeline_status():
    """Check current status of all pipelines"""
    print("🔍 PIPELINE STATUS INVESTIGATION")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Get all pipelines
        pipelines = db.query(ETLPipeline).all()
        
        print(f"📊 Found {len(pipelines)} pipelines:")
        for pipeline in pipelines:
            print(f"   {pipeline.id}: {pipeline.name} ({'Active' if pipeline.is_active else 'Inactive'})")
        
        print(f"\n📋 Recent Pipeline Runs (Last 24 hours):")
        
        # Get recent runs
        cutoff_time = datetime.utcnow() - timedelta(hours=24)
        recent_runs = db.query(ETLPipelineRun).filter(
            ETLPipelineRun.started_at >= cutoff_time
        ).order_by(ETLPipelineRun.started_at.desc()).limit(20).all()
        
        if not recent_runs:
            print("   No recent pipeline runs found")
            return []
        
        running_pipelines = []
        
        for run in recent_runs:
            pipeline = db.query(ETLPipeline).filter(ETLPipeline.id == run.pipeline_id).first()
            pipeline_name = pipeline.name if pipeline else f"Pipeline {run.pipeline_id}"
            
            duration = "Unknown"
            if run.started_at:
                if run.completed_at:
                    duration = str(run.completed_at - run.started_at)
                else:
                    duration = f"{datetime.utcnow() - run.started_at} (RUNNING)"
            
            status_icon = {
                'running': '🔄',
                'completed': '✅',
                'failed': '❌',
                'cancelled': '⏹️'
            }.get(run.status, '❓')
            
            print(f"   {status_icon} Run {run.id}: {pipeline_name}")
            print(f"      Status: {run.status}")
            print(f"      Started: {run.started_at}")
            print(f"      Duration: {duration}")
            print(f"      Records: Extract={run.records_extracted}, Transform={run.records_transformed}, Load={run.records_loaded}")
            
            if run.error_message:
                print(f"      Error: {run.error_message}")
            
            if run.status == 'running':
                running_pipelines.append((run, pipeline_name))
            
            print()
        
        return running_pipelines
        
    except Exception as e:
        print(f"❌ Error checking pipeline status: {e}")
        import traceback
        traceback.print_exc()
        return []
    finally:
        db.close()


def check_database_state():
    """Check current database table states"""
    print("🗄️ DATABASE STATE ANALYSIS")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        tables = [
            ("destination", Destination),
            ("hotel", Hotel),
            ("product_category", ProductCategory),
            ("product", Product),
            ("product_variant", ProductVariants)
        ]
        
        print("📊 Table Record Counts:")
        for table_name, model_class in tables:
            try:
                count = db.query(model_class).count()
                print(f"   {table_name:20}: {count:>8} records")
            except Exception as e:
                print(f"   {table_name:20}: ERROR - {e}")
        
        # Check for recent data
        print(f"\n📅 Recent Data Activity:")
        for table_name, model_class in tables:
            try:
                if hasattr(model_class, 'created_at'):
                    recent_count = db.query(model_class).filter(
                        model_class.created_at >= datetime.utcnow() - timedelta(hours=1)
                    ).count()
                    print(f"   {table_name:20}: {recent_count:>8} records in last hour")
            except Exception as e:
                print(f"   {table_name:20}: ERROR checking recent data - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking database state: {e}")
        return False
    finally:
        db.close()


def check_system_resources():
    """Check system resource usage"""
    print("💻 SYSTEM RESOURCE ANALYSIS")
    print("=" * 60)
    
    try:
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"🖥️ CPU Usage: {cpu_percent:.1f}%")
        
        # Memory usage
        memory = psutil.virtual_memory()
        print(f"🧠 Memory Usage: {memory.percent:.1f}% ({memory.used // (1024**3):.1f}GB / {memory.total // (1024**3):.1f}GB)")
        
        # Disk usage
        disk = psutil.disk_usage('/')
        print(f"💾 Disk Usage: {disk.percent:.1f}% ({disk.used // (1024**3):.1f}GB / {disk.total // (1024**3):.1f}GB)")
        
        # Process information
        print(f"\n🔍 Python Processes:")
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
            try:
                if 'python' in proc.info['name'].lower():
                    print(f"   PID {proc.info['pid']:>6}: {proc.info['name']:15} CPU={proc.info['cpu_percent']:>5.1f}% MEM={proc.info['memory_percent']:>5.1f}%")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking system resources: {e}")
        return False


def check_database_connections():
    """Check database connection health"""
    print("🔗 DATABASE CONNECTION ANALYSIS")
    print("=" * 60)
    
    try:
        db = SessionLocal()
        
        # Test basic connection
        start_time = time.time()
        result = db.execute("SELECT 1").fetchone()
        connection_time = time.time() - start_time
        
        print(f"✅ Database connection: {connection_time:.3f}s")
        
        # Check active connections
        try:
            active_connections = db.execute("""
                SELECT count(*) as active_connections 
                FROM pg_stat_activity 
                WHERE state = 'active'
            """).fetchone()
            
            total_connections = db.execute("""
                SELECT count(*) as total_connections 
                FROM pg_stat_activity
            """).fetchone()
            
            print(f"📊 Active connections: {active_connections[0]}")
            print(f"📊 Total connections: {total_connections[0]}")
            
        except Exception as e:
            print(f"⚠️ Could not check connection stats: {e}")
        
        # Check for locks
        try:
            locks = db.execute("""
                SELECT count(*) as lock_count
                FROM pg_locks 
                WHERE NOT granted
            """).fetchone()
            
            print(f"🔒 Blocked queries: {locks[0]}")
            
            if locks[0] > 0:
                print("⚠️ Found blocked queries - investigating...")
                blocked_queries = db.execute("""
                    SELECT pid, query, state, wait_event_type, wait_event
                    FROM pg_stat_activity 
                    WHERE wait_event_type IS NOT NULL
                    LIMIT 5
                """).fetchall()
                
                for query in blocked_queries:
                    print(f"   PID {query[0]}: {query[1][:100]}...")
                    print(f"      State: {query[2]}, Wait: {query[3]}/{query[4]}")
        
        except Exception as e:
            print(f"⚠️ Could not check locks: {e}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False


def check_server_logs():
    """Check server process and logs"""
    print("📝 SERVER PROCESS ANALYSIS")
    print("=" * 60)
    
    try:
        # Check if server is running
        server_running = False
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['cmdline'] and any('uvicorn' in arg for arg in proc.info['cmdline']):
                    print(f"✅ Server running: PID {proc.info['pid']}")
                    print(f"   Command: {' '.join(proc.info['cmdline'])}")
                    server_running = True
                    break
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        if not server_running:
            print("❌ Server not found running")
        
        return server_running
        
    except Exception as e:
        print(f"❌ Error checking server: {e}")
        return False


def analyze_hanging_pipelines(running_pipelines):
    """Analyze why pipelines might be hanging"""
    print("🔍 HANGING PIPELINE ANALYSIS")
    print("=" * 60)
    
    if not running_pipelines:
        print("✅ No pipelines currently running")
        return True
    
    db = SessionLocal()
    try:
        for run, pipeline_name in running_pipelines:
            print(f"🔄 Analyzing hanging pipeline: {pipeline_name} (Run {run.id})")
            
            # Check how long it's been running
            if run.started_at:
                runtime = datetime.utcnow() - run.started_at
                print(f"   Runtime: {runtime}")
                
                if runtime > timedelta(minutes=30):
                    print(f"   ⚠️ Pipeline has been running for over 30 minutes")
                
                if runtime > timedelta(hours=2):
                    print(f"   🚨 Pipeline has been running for over 2 hours - likely stuck")
            
            # Check progress
            if run.records_extracted and run.records_transformed:
                if run.records_loaded is None:
                    print(f"   🔍 Issue: Data extracted ({run.records_extracted}) and transformed ({run.records_transformed}) but not loaded")
                    print(f"   💡 Likely stuck in loading phase")
                elif run.records_loaded < run.records_transformed:
                    print(f"   🔍 Issue: Partial loading ({run.records_loaded}/{run.records_transformed})")
                    print(f"   💡 Loading process may be stuck or slow")
            elif run.records_extracted and not run.records_transformed:
                print(f"   🔍 Issue: Data extracted ({run.records_extracted}) but not transformed")
                print(f"   💡 Likely stuck in transformation phase")
            elif not run.records_extracted:
                print(f"   🔍 Issue: No data extracted yet")
                print(f"   💡 Likely stuck in extraction phase (Salesforce API issues?)")
            
            # Check logs if available
            if hasattr(run, 'logs') and run.logs:
                print(f"   📝 Recent log entries:")
                try:
                    import json
                    logs = json.loads(run.logs) if isinstance(run.logs, str) else run.logs
                    if logs and len(logs) > 0:
                        for log_entry in logs[-3:]:  # Last 3 log entries
                            print(f"      {log_entry}")
                    else:
                        print(f"      No log entries found")
                except Exception as e:
                    print(f"      Error parsing logs: {e}")
            
            print()
        
        return len(running_pipelines) == 0
        
    except Exception as e:
        print(f"❌ Error analyzing hanging pipelines: {e}")
        return False
    finally:
        db.close()


def main():
    """Main diagnostic function"""
    print("🚨 ETL PIPELINE DIAGNOSTIC INVESTIGATION")
    print("=" * 60)
    print(f"Timestamp: {datetime.utcnow()}")
    print()
    
    # Step 1: Check pipeline status
    running_pipelines = check_pipeline_status()
    
    print()
    
    # Step 2: Check database state
    db_healthy = check_database_state()
    
    print()
    
    # Step 3: Check system resources
    resources_ok = check_system_resources()
    
    print()
    
    # Step 4: Check database connections
    db_connections_ok = check_database_connections()
    
    print()
    
    # Step 5: Check server status
    server_ok = check_server_logs()
    
    print()
    
    # Step 6: Analyze hanging pipelines
    pipelines_ok = analyze_hanging_pipelines(running_pipelines)
    
    print()
    print("=" * 60)
    print("📊 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    checks = [
        ("Database Health", db_healthy),
        ("System Resources", resources_ok),
        ("Database Connections", db_connections_ok),
        ("Server Status", server_ok),
        ("Pipeline Status", pipelines_ok)
    ]
    
    passed_checks = sum(1 for _, passed in checks if passed)
    total_checks = len(checks)
    
    for check_name, passed in checks:
        status = "✅" if passed else "❌"
        print(f"{status} {check_name}")
    
    print(f"\nOverall Health: {passed_checks}/{total_checks} checks passed")
    
    if running_pipelines:
        print(f"\n🚨 IMMEDIATE ISSUES:")
        print(f"   - {len(running_pipelines)} pipeline(s) stuck in running state")
        print(f"   - Pipelines may need to be cancelled and restarted")
        print(f"   - Check Salesforce API connectivity")
        print(f"   - Consider database transaction timeouts")
    
    if passed_checks < total_checks:
        print(f"\n💡 RECOMMENDED ACTIONS:")
        if not db_healthy:
            print(f"   - Check database connectivity and table schemas")
        if not resources_ok:
            print(f"   - Monitor system resources and consider scaling")
        if not db_connections_ok:
            print(f"   - Check for database locks and connection limits")
        if not server_ok:
            print(f"   - Restart the application server")
        if not pipelines_ok:
            print(f"   - Cancel stuck pipelines and investigate root cause")
    
    return passed_checks == total_checks and len(running_pipelines) == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
