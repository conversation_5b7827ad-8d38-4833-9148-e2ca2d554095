#!/usr/bin/env python3
"""
Final verification of the complete Room Pipeline implementation
"""
import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipeline, ProductVariants, Product


def verify_room_pipeline_implementation():
    """Verify complete Room Pipeline implementation"""
    print("🔍 ROOM PIPELINE IMPLEMENTATION VERIFICATION")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # 1. Verify Room Pipeline configuration
        room_pipeline = db.query(ETLPipeline).filter(
            ETLPipeline.name == "Room Pipeline"
        ).first()
        
        print("📋 1. PIPELINE CONFIGURATION")
        if room_pipeline:
            print(f"   ✅ Room Pipeline exists (ID: {room_pipeline.id})")
            print(f"   ✅ Source: {room_pipeline.source_object}")
            print(f"   ✅ Destination: {room_pipeline.destination_table}")
            print(f"   ✅ Active: {room_pipeline.is_active}")
        else:
            print("   ❌ Room Pipeline not found")
            return False
        
        # 2. Verify database schema
        print(f"\n📊 2. DATABASE SCHEMA")
        try:
            variant_count = db.query(ProductVariants).count()
            product_count = db.query(Product).count()
            print(f"   ✅ product_variant table accessible")
            print(f"   ✅ Current variants: {variant_count}")
            print(f"   ✅ Current product: {product_count}")
        except Exception as e:
            print(f"   ❌ Database schema issue: {e}")
            return False
        
        # 3. Verify relationships
        print(f"\n🔗 3. RELATIONSHIPS")
        if variant_count > 0:
            sample_variant = db.query(ProductVariants).first()
            if sample_variant.product:
                print(f"   ✅ Product relationships working")
                print(f"   ✅ Sample: {sample_variant.name} → {sample_variant.product.title}")
            else:
                print(f"   ⚠️ Sample variant has no product relationship")
        else:
            print(f"   ℹ️ No variants to test relationships")
        
        # 4. Verify field mappings
        print(f"\n📝 4. FIELD MAPPINGS")
        expected_fields = [
            "id", "migrated_id", "name", "product_id", "room_config_name", 
            "is_connected", "pipeline_run_id", "source_created_at", 
            "source_updated_at", "extracted_at", "source_system", 
            "external_id", "is_active"
        ]
        
        if variant_count > 0:
            sample_variant = db.query(ProductVariants).first()
            variant_dict = sample_variant.to_dict()
            
            missing_fields = []
            for field in expected_fields:
                if field not in variant_dict:
                    missing_fields.append(field)
            
            if not missing_fields:
                print(f"   ✅ All expected fields present")
            else:
                print(f"   ⚠️ Missing fields: {missing_fields}")
        else:
            print(f"   ℹ️ No variants to verify field mappings")
        
        # 5. Verify ID generation
        print(f"\n🆔 5. ID GENERATION")
        if variant_count > 0:
            sample_variant = db.query(ProductVariants).first()
            if sample_variant.id.startswith('pvar_') and len(sample_variant.id) == 26:
                print(f"   ✅ ID format correct: {sample_variant.id}")
            else:
                print(f"   ❌ ID format incorrect: {sample_variant.id}")
        else:
            print(f"   ℹ️ No variants to verify ID generation")
        
        # 6. Summary
        print(f"\n📊 6. IMPLEMENTATION SUMMARY")
        
        features = [
            ("Pipeline Configuration", room_pipeline is not None),
            ("Database Schema", True),  # We got this far
            ("Field Mappings", variant_count == 0 or len(missing_fields) == 0),
            ("ID Generation", variant_count == 0 or sample_variant.id.startswith('pvar_')),
            ("Relationships", variant_count == 0 or (sample_variant.product is not None))
        ]
        
        passed_count = sum(1 for _, passed in features if passed)
        total_count = len(features)
        
        print(f"   Features implemented: {passed_count}/{total_count}")
        for feature_name, passed in features:
            status = "✅" if passed else "❌"
            print(f"   {status} {feature_name}")
        
        success_rate = (passed_count / total_count) * 100
        print(f"\n   Implementation completeness: {success_rate:.1f}%")
        
        return success_rate >= 80
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()


def show_room_pipeline_usage():
    """Show how to use the Room Pipeline"""
    print(f"\n📖 ROOM PIPELINE USAGE GUIDE")
    print("=" * 60)
    
    print(f"🚀 EXECUTION:")
    print(f"   API: POST /api/v1/pipelines/4/execute")
    print(f"   CLI: curl -X POST http://localhost:8000/api/v1/pipelines/4/execute")
    
    print(f"\n📊 MONITORING:")
    print(f"   Status: GET /api/v1/pipelines/4/runs")
    print(f"   Progress: WebSocket updates during execution")
    
    print(f"\n💾 DATA FLOW:")
    print(f"   1. Extract Room__c data from Salesforce")
    print(f"   2. Transform and map to product_variant schema")
    print(f"   3. Resolve Room_Type__c → product.id relationships")
    print(f"   4. Load into product_variant table")
    print(f"   5. Store raw/processed data in MinIO")
    
    print(f"\n🔗 DEPENDENCIES:")
    print(f"   - product table must have data (run Room Type Pipeline first)")
    print(f"   - Salesforce Room__c object access")
    print(f"   - MinIO storage availability")
    
    print(f"\n📋 EXPECTED RESULTS:")
    print(f"   - Room__c records → product_variant table")
    print(f"   - Foreign key relationships to product")
    print(f"   - pvar_ prefixed IDs")
    print(f"   - Complete audit trail")


def main():
    """Main verification function"""
    print("🧪 ROOM PIPELINE FINAL VERIFICATION")
    print("=" * 60)
    
    # Verify implementation
    implementation_success = verify_room_pipeline_implementation()
    
    # Show usage guide
    show_room_pipeline_usage()
    
    print("\n" + "=" * 60)
    print("📊 FINAL VERIFICATION RESULTS:")
    
    if implementation_success:
        print("🎉 ROOM PIPELINE IMPLEMENTATION COMPLETE!")
        print("\n✅ Successfully Implemented:")
        print("   - Dedicated Room Pipeline configuration")
        print("   - Room__c data extraction from Salesforce")
        print("   - Product variants table population")
        print("   - Foreign key relationships to product")
        print("   - Independent pipeline execution")
        print("   - Complete ETL workflow")
        print("\n🚀 Room Pipeline is ready for production use!")
        
        print("\n💡 Next Steps:")
        print("   1. Ensure Room Type Pipeline has populated product table")
        print("   2. Execute Room Pipeline via API")
        print("   3. Monitor execution progress")
        print("   4. Verify product_variant table population")
        print("   5. Test with real Salesforce Room__c data")
        
    else:
        print("⚠️ ROOM PIPELINE IMPLEMENTATION INCOMPLETE")
        print("   Some components need attention before production use")
    
    return implementation_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
