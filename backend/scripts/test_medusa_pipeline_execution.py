#!/usr/bin/env python3
"""
Test Room Type and Room pipelines with Medusa table integration
"""
import sys
import os
import time
import requests
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from sqlalchemy import text


def test_room_type_pipeline_execution():
    """Test Room Type Pipeline execution with Medusa product table"""
    print("🧪 TESTING ROOM TYPE PIPELINE (MEDUSA PRODUCT TABLE)")
    print("=" * 60)
    
    try:
        # Start pipeline execution
        print("🚀 Starting Room Type Pipeline...")
        response = requests.post("http://localhost:8000/api/v1/pipelines/3/execute", timeout=10)
        
        if response.status_code != 200:
            print(f"❌ Failed to start pipeline: {response.status_code}")
            print(f"Response: {response.text}")
            return False
        
        result = response.json()
        run_id = result.get('run_id')
        print(f"✅ Pipeline started: Run ID {run_id}")
        
        # Monitor execution
        print("⏳ Monitoring execution...")
        max_wait = 90  # 1.5 minute timeout
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            try:
                status_response = requests.get("http://localhost:8000/api/v1/pipelines/3/runs", timeout=5)
                if status_response.status_code == 200:
                    runs = status_response.json()
                    if runs:
                        latest_run = runs[0]
                        status = latest_run.get('status')
                        extracted = latest_run.get('records_extracted', 0)
                        transformed = latest_run.get('records_transformed', 0)
                        loaded = latest_run.get('records_loaded', 0)
                        
                        print(f"   Status: {status} | E:{extracted} T:{transformed} L:{loaded}")
                        
                        if status == 'completed':
                            print(f"✅ Pipeline completed successfully!")
                            print(f"   Records: {extracted} extracted → {transformed} transformed → {loaded} loaded")
                            return True
                        elif status == 'failed':
                            error = latest_run.get('error_message', 'Unknown error')
                            print(f"❌ Pipeline failed: {error}")
                            return False
                
                time.sleep(5)
                
            except Exception as e:
                print(f"⚠️ Monitoring error: {e}")
                time.sleep(5)
        
        print(f"⏰ Pipeline execution timeout after {max_wait} seconds")
        return False
        
    except Exception as e:
        print(f"❌ Error testing Room Type Pipeline: {e}")
        return False


def test_room_pipeline_execution():
    """Test Room Pipeline execution with Medusa product_variant table"""
    print("\n🧪 TESTING ROOM PIPELINE (MEDUSA PRODUCT_VARIANT TABLE)")
    print("=" * 60)
    
    try:
        # Start pipeline execution
        print("🚀 Starting Room Pipeline...")
        response = requests.post("http://localhost:8000/api/v1/pipelines/4/execute", timeout=10)
        
        if response.status_code != 200:
            print(f"❌ Failed to start pipeline: {response.status_code}")
            print(f"Response: {response.text}")
            return False
        
        result = response.json()
        run_id = result.get('run_id')
        print(f"✅ Pipeline started: Run ID {run_id}")
        
        # Monitor execution
        print("⏳ Monitoring execution...")
        max_wait = 90  # 1.5 minute timeout
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            try:
                status_response = requests.get("http://localhost:8000/api/v1/pipelines/4/runs", timeout=5)
                if status_response.status_code == 200:
                    runs = status_response.json()
                    if runs:
                        latest_run = runs[0]
                        status = latest_run.get('status')
                        extracted = latest_run.get('records_extracted', 0)
                        transformed = latest_run.get('records_transformed', 0)
                        loaded = latest_run.get('records_loaded', 0)
                        
                        print(f"   Status: {status} | E:{extracted} T:{transformed} L:{loaded}")
                        
                        if status == 'completed':
                            print(f"✅ Pipeline completed successfully!")
                            print(f"   Records: {extracted} extracted → {transformed} transformed → {loaded} loaded")
                            return True
                        elif status == 'failed':
                            error = latest_run.get('error_message', 'Unknown error')
                            print(f"❌ Pipeline failed: {error}")
                            return False
                
                time.sleep(5)
                
            except Exception as e:
                print(f"⚠️ Monitoring error: {e}")
                time.sleep(5)
        
        print(f"⏰ Pipeline execution timeout after {max_wait} seconds")
        return False
        
    except Exception as e:
        print(f"❌ Error testing Room Pipeline: {e}")
        return False


def verify_medusa_data_persistence():
    """Verify that data was loaded into Medusa tables"""
    print("\n🔍 VERIFYING MEDUSA DATA PERSISTENCE")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Check Medusa product table
        result = db.execute(text("SELECT COUNT(*) FROM product WHERE external_id IS NOT NULL"))
        product_count = result.scalar()
        print(f"Products in Medusa table: {product_count}")
        
        if product_count > 0:
            result = db.execute(text("""
                SELECT id, title, external_id, status, metadata->>'salesforce_id' as sf_id
                FROM product 
                WHERE external_id IS NOT NULL 
                ORDER BY created_at DESC 
                LIMIT 3
            """))
            products = result.fetchall()
            print("Recent products:")
            for product in products:
                print(f"  - {product[1]} (ID: {product[0]}, SF: {product[4]})")
        
        # Check Medusa product_variant table
        result = db.execute(text("SELECT COUNT(*) FROM product_variant WHERE metadata IS NOT NULL"))
        variant_count = result.scalar()
        print(f"Product variants in Medusa table: {variant_count}")
        
        if variant_count > 0:
            result = db.execute(text("""
                SELECT id, title, product_id, metadata->>'salesforce_id' as sf_id
                FROM product_variant 
                WHERE metadata IS NOT NULL 
                ORDER BY created_at DESC 
                LIMIT 3
            """))
            variants = result.fetchall()
            print("Recent product variants:")
            for variant in variants:
                print(f"  - {variant[1]} (ID: {variant[0]}, Product: {variant[2]}, SF: {variant[3]})")
        
        return product_count > 0 or variant_count > 0
        
    except Exception as e:
        print(f"❌ Error verifying Medusa data persistence: {e}")
        return False
    finally:
        db.close()


def check_server_status():
    """Check if the server is running"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        return response.status_code == 200
    except:
        return False


if __name__ == "__main__":
    print("🚀 TESTING MEDUSA TABLE INTEGRATION")
    print("=" * 70)
    
    # Check server status
    if not check_server_status():
        print("❌ Server is not running. Please start the server first.")
        print("   Run: uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
        sys.exit(1)
    
    print("✅ Server is running")
    
    # Test Room Type Pipeline
    room_type_ok = test_room_type_pipeline_execution()
    
    # Test Room Pipeline (only if Room Type succeeded to ensure dependencies)
    room_ok = test_room_pipeline_execution() if room_type_ok else False
    
    # Verify data persistence
    data_ok = verify_medusa_data_persistence()
    
    print(f"\n🎯 MEDUSA INTEGRATION SUMMARY:")
    print(f"   Room Type Pipeline → Medusa product: {'✅ OK' if room_type_ok else '❌ FAILED'}")
    print(f"   Room Pipeline → Medusa product_variant: {'✅ OK' if room_ok else '❌ FAILED'}")
    print(f"   Medusa Data Persistence: {'✅ OK' if data_ok else '❌ FAILED'}")
    
    if room_type_ok and room_ok and data_ok:
        print("\n🎉 MEDUSA INTEGRATION SUCCESS!")
        print("   Both pipelines are working correctly with Medusa tables.")
        print("   Salesforce data is being loaded into PowderByrne Medusa database.")
    else:
        print("\n❌ MEDUSA INTEGRATION ISSUES DETECTED")
        print("   Check the individual test results above for details.")
