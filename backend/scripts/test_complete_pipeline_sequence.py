#!/usr/bin/env python3
"""
Test complete pipeline sequence after repair
"""
import sys
import os
import time
import requests
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.etl_pipeline import (
    Destination, Hotel, ProductCategory, Product, ProductVariants, ProductCategoryproduct
)


def execute_pipeline_sequence():
    """Execute pipelines in correct sequence"""
    print("🚀 EXECUTING COMPLETE PIPELINE SEQUENCE")
    print("=" * 60)
    
    pipelines = [
        (1, "Resort Pipeline", 30),
        (2, "Hotel Pipeline", 60),
        (3, "Room Type Pipeline", 90),
        (4, "Room Pipeline", 60)
    ]
    
    results = []
    
    for pipeline_id, pipeline_name, timeout in pipelines:
        print(f"\n📡 Executing {pipeline_name}...")
        
        try:
            # Execute pipeline
            response = requests.post(f"http://localhost:8000/api/v1/pipelines/{pipeline_id}/execute")
            
            if response.status_code != 200:
                print(f"❌ Failed to start {pipeline_name}: {response.status_code}")
                results.append((pipeline_name, False, f"HTTP {response.status_code}"))
                continue
            
            result = response.json()
            print(f"✅ {pipeline_name} started: Run ID {result.get('run_id')}")
            
            # Monitor execution
            print(f"⏳ Monitoring {pipeline_name} (timeout: {timeout}s)...")
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    status_response = requests.get(f"http://localhost:8000/api/v1/pipelines/{pipeline_id}/runs")
                    
                    if status_response.status_code == 200:
                        runs = status_response.json()
                        if runs:
                            latest_run = runs[0]
                            status = latest_run.get('status')
                            extracted = latest_run.get('records_extracted', 0)
                            loaded = latest_run.get('records_loaded', 0)
                            
                            print(f"   Status: {status} | Extract: {extracted} | Load: {loaded}")
                            
                            if status == 'completed':
                                print(f"✅ {pipeline_name} completed successfully!")
                                results.append((pipeline_name, True, f"Extract: {extracted}, Load: {loaded}"))
                                break
                            elif status == 'failed':
                                error_msg = latest_run.get('error_message', 'Unknown error')
                                print(f"❌ {pipeline_name} failed: {error_msg}")
                                results.append((pipeline_name, False, error_msg))
                                break
                    
                    time.sleep(5)  # Check every 5 seconds
                    
                except Exception as e:
                    print(f"⚠️ Error monitoring {pipeline_name}: {e}")
                    time.sleep(5)
            
            else:
                print(f"⏰ {pipeline_name} timeout reached")
                results.append((pipeline_name, False, "Timeout"))
        
        except Exception as e:
            print(f"❌ Error executing {pipeline_name}: {e}")
            results.append((pipeline_name, False, str(e)))
    
    return results


def verify_data_loading():
    """Verify data was loaded into all tables"""
    print("\n🔍 VERIFYING DATA LOADING")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        tables = [
            ("destination", Destination),
            ("hotel", Hotel),
            ("product_category", ProductCategory),
            ("product", Product),
            ("product_variant", ProductVariants),
            ("product_category_product", ProductCategoryproduct)
        ]
        
        results = {}
        
        print("📊 Final Table Counts:")
        for table_name, model_class in tables:
            try:
                count = db.query(model_class).count()
                results[table_name] = count
                status = "✅" if count > 0 else "❌"
                print(f"   {status} {table_name:25}: {count:>6} records")
            except Exception as e:
                print(f"   ❌ {table_name:25}: ERROR - {e}")
                results[table_name] = 0
        
        # Check relationships
        print(f"\n🔗 Relationship Verification:")
        
        # product with categories
        try:
            product_with_categories = db.query(Product).filter(
                Product.category_id.isnot(None)
            ).count()
            total_product = results.get('product', 0)
            
            if total_product > 0:
                category_percentage = (product_with_categories / total_product) * 100
                print(f"   product with categories: {product_with_categories}/{total_product} ({category_percentage:.1f}%)")
            else:
                print(f"   product with categories: 0/0 (No product)")
        except Exception as e:
            print(f"   Error checking product categories: {e}")
        
        # Product variants with product
        try:
            variants_with_product = db.query(ProductVariants).filter(
                ProductVariants.product_id.isnot(None)
            ).count()
            total_variants = results.get('product_variant', 0)
            
            if total_variants > 0:
                variant_percentage = (variants_with_product / total_variants) * 100
                print(f"   Variants with product: {variants_with_product}/{total_variants} ({variant_percentage:.1f}%)")
            else:
                print(f"   Variants with product: 0/0 (No variants)")
        except Exception as e:
            print(f"   Error checking variant product: {e}")
        
        # Junction table coverage
        try:
            junction_count = results.get('product_category_product', 0)
            product_count = results.get('product', 0)
            
            if product_count > 0:
                junction_percentage = (junction_count / product_count) * 100
                print(f"   Junction table coverage: {junction_count}/{product_count} ({junction_percentage:.1f}%)")
            else:
                print(f"   Junction table coverage: 0/0 (No product)")
        except Exception as e:
            print(f"   Error checking junction table: {e}")
        
        return results
        
    except Exception as e:
        print(f"❌ Error verifying data: {e}")
        return {}
    finally:
        db.close()


def show_sample_data():
    """Show sample data from each table"""
    print(f"\n📋 SAMPLE DATA")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Sample destination
        destination = db.query(Destination).limit(3).all()
        if destination:
            print(f"🏖️ Sample destination:")
            for dest in destination:
                print(f"   - {dest.name} ({dest.country})")
        
        # Sample hotel
        hotel = db.query(Hotel).limit(3).all()
        if hotel:
            print(f"\n🏨 Sample hotel:")
            for hotel in hotel:
                print(f"   - {hotel.name} ({hotel.resort_name})")
        
        # Sample product
        product = db.query(Product).limit(3).all()
        if product:
            print(f"\n🛏️ Sample product:")
            for product in product:
                print(f"   - {product.title} ({product.hotel_name})")
        
        # Sample product variants
        variants = db.query(ProductVariants).limit(3).all()
        if variants:
            print(f"\n🏠 Sample Product Variants:")
            for variant in variants:
                product_name = variant.product.title if variant.product else "No Product"
                print(f"   - {variant.name} → {product_name}")
        
    except Exception as e:
        print(f"❌ Error showing sample data: {e}")
    finally:
        db.close()


def main():
    """Main test function"""
    print("🧪 COMPLETE PIPELINE SEQUENCE TEST")
    print("=" * 60)
    print(f"Timestamp: {datetime.utcnow()}")
    print()
    
    # Step 1: Execute pipeline sequence
    pipeline_results = execute_pipeline_sequence()
    
    # Step 2: Verify data loading
    data_results = verify_data_loading()
    
    # Step 3: Show sample data
    show_sample_data()
    
    print("\n" + "=" * 60)
    print("📊 COMPLETE PIPELINE TEST RESULTS")
    print("=" * 60)
    
    # Pipeline execution results
    print("🚀 Pipeline Execution:")
    successful_pipelines = 0
    for pipeline_name, success, details in pipeline_results:
        status = "✅" if success else "❌"
        print(f"   {status} {pipeline_name}: {details}")
        if success:
            successful_pipelines += 1
    
    # Data loading results
    print(f"\n📊 Data Loading:")
    tables_with_data = 0
    total_tables = len(data_results)
    
    for table_name, count in data_results.items():
        status = "✅" if count > 0 else "❌"
        print(f"   {status} {table_name}: {count} records")
        if count > 0:
            tables_with_data += 1
    
    # Overall success metrics
    pipeline_success_rate = (successful_pipelines / len(pipeline_results)) * 100 if pipeline_results else 0
    data_success_rate = (tables_with_data / total_tables) * 100 if total_tables > 0 else 0
    
    print(f"\n📈 Success Metrics:")
    print(f"   Pipeline Execution: {successful_pipelines}/{len(pipeline_results)} ({pipeline_success_rate:.1f}%)")
    print(f"   Data Loading: {tables_with_data}/{total_tables} ({data_success_rate:.1f}%)")
    
    overall_success = pipeline_success_rate >= 75 and data_success_rate >= 75
    
    if overall_success:
        print(f"\n🎉 COMPLETE PIPELINE SEQUENCE SUCCESS!")
        print(f"\n✅ Achievements:")
        print(f"   - All pipelines executed successfully")
        print(f"   - Data loaded into target tables")
        print(f"   - Relationships established correctly")
        print(f"   - System performing as expected")
        print(f"\n🚀 ETL Pipeline System is fully operational!")
    else:
        print(f"\n⚠️ PIPELINE SEQUENCE PARTIALLY SUCCESSFUL")
        print(f"   Some pipelines or data loading may need attention")
        if pipeline_success_rate < 75:
            print(f"   - Pipeline execution issues detected")
        if data_success_rate < 75:
            print(f"   - Data loading issues detected")
    
    return overall_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
