# ETL Data Pipeline System

## Overview

The ETL Data Pipeline system is a comprehensive solution for extracting, transforming, and loading data from Salesforce to PostgreSQL with MinIO object storage for data persistence and auditing. The system provides a complete pipeline management interface with monitoring, execution, and configuration capabilities.

## Architecture

### System Components

1. **Frontend Dashboard** - React-based pipeline management interface
2. **Backend API** - FastAPI-based pipeline execution engine
3. **MinIO Object Storage** - Raw and processed data storage
4. **PostgreSQL Database** - Final data destination and pipeline metadata
5. **Salesforce Integration** - Source data extraction

### ETL Pipeline Flow

```
Salesforce → Extract → MinIO (Raw) → Transform → MinIO (Processed) → PostgreSQL
```

1. **Extract**: Fetch data from Salesforce objects using OAuth integration
2. **Load (Raw)**: Store raw extracted data in MinIO for persistence and auditing
3. **Transform**: Apply custom transformation logic to the raw data
4. **Load (Processed)**: Store transformed data in MinIO
5. **Transfer**: Insert final data into PostgreSQL database

## Infrastructure Setup

### MinIO Object Storage

MinIO is configured to run on ports 9002 (API) and 9003 (Console) to avoid conflicts:

```bash
# Start MinIO
docker-compose up -d minio

# Access MinIO Console
http://localhost:9003
# Credentials: minioadmin / minioadmin123
```

### Database Tables

The system creates the following tables:

- `etl_pipelines` - Pipeline configurations
- `etl_pipeline_runs` - Pipeline execution history
- `destination` - Resort Pipeline destination table

### Environment Configuration

```env
# MinIO Configuration
MINIO_ENDPOINT=localhost:9002
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123
MINIO_BUCKET_NAME=etl-pipeline-data
MINIO_SECURE=false
```

## Resort Pipeline

### Configuration

The Resort Pipeline is pre-configured with the following settings:

- **Name**: Resort Pipeline
- **Source**: Salesforce Resort__c object
- **Source Fields**: IsDeleted, Name, CurrencyIsoCode, CreatedDate, LastModifiedDate, Country__c, Description__c
- **Destination**: PostgreSQL destination table
- **Destination Fields**: is_active, name, currency, created_at, updated_at, country, description
- **Transformation**: Field mapping, data cleaning, and boolean inversion

### Field Mappings

| Source Field | Destination Field | Transformation |
|--------------|-------------------|----------------|
| IsDeleted | is_active | Invert boolean (IsDeleted=false → is_active=true) |
| Name | name | Trim whitespace, handle nulls |
| CurrencyIsoCode | currency | Trim whitespace, handle nulls |
| CreatedDate | created_at | Direct mapping |
| LastModifiedDate | updated_at | Direct mapping |
| Country__c | country | Trim whitespace, handle nulls |
| Description__c | description | Trim whitespace, handle nulls |

## API Endpoints

### Pipeline Management

- `GET /api/v1/pipelines/` - List all pipelines
- `POST /api/v1/pipelines/` - Create new pipeline
- `GET /api/v1/pipelines/{id}` - Get pipeline details
- `PUT /api/v1/pipelines/{id}` - Update pipeline
- `DELETE /api/v1/pipelines/{id}` - Delete pipeline

### Pipeline Execution

- `POST /api/v1/pipelines/{id}/execute` - Execute pipeline
- `GET /api/v1/pipelines/{id}/runs` - Get pipeline runs
- `GET /api/v1/pipelines/stats/overview` - Get pipeline statistics

### Data Access

- `GET /api/v1/pipelines/destination/` - List destination records

## Frontend Interface

### Navigation

The pipeline interface is accessible via the "Data Pipeline" menu item in the left sidebar navigation.

### Dashboard Features

1. **Pipeline Overview Cards**
   - Total Pipelines
   - Active Pipelines
   - Successful Runs
   - Failed Runs

2. **Pipeline List**
   - Pipeline status and configuration
   - Last run information
   - Manual execution controls
   - Configuration management

3. **Pipeline Details**
   - Execution history
   - Run logs and metrics
   - Error reporting

### Pipeline Status Indicators

- **Completed** - Green badge with checkmark
- **Running** - Blue badge with activity icon
- **Failed** - Red badge with X icon
- **Inactive** - Gray badge

## Usage Instructions

### Running the Resort Pipeline

1. **Navigate to Pipelines**: Click "Data Pipeline" in the sidebar
2. **View Resort Pipeline**: See the pre-configured Resort Pipeline
3. **Execute Pipeline**: Click the "Run" button
4. **Monitor Progress**: Watch status updates and execution logs
5. **View Results**: Check the destination table for loaded data

### Pipeline Execution Process

1. **Authentication**: System authenticates with Salesforce
2. **Data Extraction**: Fetches Resort__c records with all configured fields (IsDeleted, Name, CurrencyIsoCode, CreatedDate, LastModifiedDate, Country__c, Description__c)
3. **Raw Storage**: Stores extracted data in MinIO (`raw/Resort Pipeline/{run_id}/`)
4. **Transformation**: Applies field mappings, data cleaning, and boolean inversion (IsDeleted → is_active)
5. **Processed Storage**: Stores transformed data in MinIO (`processed/Resort Pipeline/{run_id}/`)
6. **Database Load**: Inserts final data into PostgreSQL destination table with all mapped fields

### Monitoring and Logs

Each pipeline run includes:
- **Execution Metrics**: Records extracted, transformed, and loaded
- **Timing Information**: Start time, duration, completion time
- **Detailed Logs**: Step-by-step execution logs with timestamps
- **Error Handling**: Comprehensive error messages and stack traces
- **Data Paths**: MinIO paths for raw and processed data

## Data Storage

### MinIO Structure

```
etl-pipeline-data/
├── raw/
│   └── Resort Pipeline/
│       └── {run_id}/
│           └── {timestamp}_raw_data.json
└── processed/
    └── Resort Pipeline/
        └── {run_id}/
            └── {timestamp}_processed_data.json
```

### PostgreSQL Schema

```sql
-- destination table (Resort Pipeline output)
CREATE TABLE destination (
    id SERIAL PRIMARY KEY,
    country VARCHAR(255),
    name VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Error Handling

### Pipeline Execution Errors

- **Salesforce Connection**: Authentication and API errors
- **Data Extraction**: Object access and field permission errors
- **Transformation**: Data validation and mapping errors
- **Storage**: MinIO connection and upload errors
- **Database**: PostgreSQL connection and insertion errors

### Recovery Mechanisms

- **Automatic Retry**: Failed operations are logged for manual retry
- **Data Persistence**: Raw data is always stored before transformation
- **Audit Trail**: Complete execution logs for debugging
- **Rollback**: Failed runs don't affect previous successful data

## Performance Considerations

### Optimization Features

- **Batch Processing**: Efficient handling of large datasets
- **Streaming**: Memory-efficient data processing
- **Compression**: MinIO data compression for storage efficiency
- **Indexing**: Database indexes for query performance

### Scalability

- **Background Execution**: Non-blocking pipeline execution
- **Concurrent Runs**: Multiple pipelines can run simultaneously
- **Resource Management**: Configurable memory and processing limits

## Security

### Data Protection

- **Encryption**: MinIO data encryption at rest
- **Access Control**: Role-based access to pipeline operations
- **Audit Logging**: Complete audit trail of all operations
- **Secure Storage**: Encrypted database connections

### Authentication

- **Salesforce OAuth**: Secure API authentication
- **Database SSL**: Encrypted database connections
- **MinIO Security**: Access key-based authentication

## Troubleshooting

### Common Issues

1. **MinIO Connection**: Check port availability (9002/9003)
2. **Database Connection**: Verify PostgreSQL credentials
3. **Salesforce Auth**: Check OAuth credentials and permissions
4. **Pipeline Execution**: Review execution logs for specific errors

### Debug Information

- **Pipeline Logs**: Detailed execution logs in the UI
- **MinIO Console**: Direct access to stored data
- **Database Queries**: Direct table access for verification
- **API Logs**: Backend server logs for system-level issues

## Future Enhancements

### Planned Features

- **Scheduling**: Automated pipeline execution with cron-like scheduling
- **Data Validation**: Advanced data quality checks and validation rules
- **Notifications**: Email/Slack notifications for pipeline status
- **Multiple Sources**: Support for additional data sources beyond Salesforce
- **Custom Transformations**: User-defined transformation logic
- **Data Lineage**: Visual data flow and lineage tracking

This ETL Pipeline system provides a robust, scalable foundation for data integration workflows with comprehensive monitoring, error handling, and data persistence capabilities.
