# Data Extraction Wizard

A multi-step wizard interface for the Flinkk Transfer Hub ETL system that provides an intuitive way to extract data from Salesforce objects with field selection and preview capabilities.

## Features

### Step 1 - Object Selection
- **Salesforce Connection**: Automatically connects to Salesforce using OAuth credentials
- **Object Browser**: Displays all queryable Salesforce objects (both standard and custom)
- **Search & Filter**: 
  - Search objects by name or label
  - Filter by object type (All, Standard, Custom)
- **Object Information**: Shows object labels, API names, and custom indicators

### Step 2 - Field Selection
- **Field Discovery**: Automatically loads all available fields for the selected object
- **Field Details**: Displays field name, label, data type, and metadata
- **Smart Selection**:
  - Individual field selection with checkboxes
  - "Select All" and "Deselect All" options
  - Search fields by name, label, or type
  - Filter to show only selected fields
- **Field Metadata**: Shows field types, required status, custom indicators, and constraints
- **Validation**: Ensures at least one field is selected before extraction

### Step 3 - Data Preview & Export
- **Data Table**: Interactive table with sorting, filtering, and pagination
- **Preview Capabilities**:
  - Shows up to 1000 records for preview
  - Global search across all fields
  - Column sorting
  - Pagination with configurable page sizes
- **Excel Export**:
  - Downloads complete dataset (not just preview)
  - Uses original field names as headers
  - Maintains data formatting
  - Generates timestamped filenames

## Technical Implementation

### Components

#### Stepper Component (`/components/ui/stepper.tsx`)
- Visual progress indicator
- Shows current step and completion status
- Responsive design with step descriptions

#### Field Selection Component (`/components/field-selection/field-selection.tsx`)
- Comprehensive field browser
- Real-time search and filtering
- Field metadata display
- Bulk selection controls

#### Data Preview Component (`/components/data-preview/data-preview.tsx`)
- TanStack Table integration
- Advanced filtering and sorting
- Excel export functionality
- Responsive data display

#### Excel Export Utility (`/lib/excel-export.ts`)
- CSV-based export with .xlsx extension
- Handles data formatting and escaping
- Generates timestamped filenames
- Error handling for edge cases

### API Integration

The wizard integrates with existing Salesforce APIs:
- `GET /migration/salesforce/objects` - List all objects
- `GET /migration/salesforce/objects/{object}/fields` - Get object fields
- `POST /migration/salesforce/data/{object}` - Extract data with field selection

### State Management

- **Connection State**: Tracks Salesforce authentication status
- **Navigation State**: Manages wizard step progression
- **Data State**: Stores selected objects, fields, and extracted data
- **Loading States**: Provides feedback during API operations
- **Error Handling**: Comprehensive error display and recovery

## Usage

1. **Navigate to Wizard**: Click "Data Wizard" in the sidebar navigation
2. **Select Object**: Choose a Salesforce object from the grid
3. **Choose Fields**: Select specific fields to extract
4. **Preview Data**: Review extracted data in the table
5. **Export**: Download complete dataset as Excel file

## Navigation

The wizard provides intuitive navigation:
- **Forward Navigation**: Click objects/buttons to proceed
- **Backward Navigation**: "Back" buttons to return to previous steps
- **Breadcrumb Support**: Visual stepper shows current position
- **Error Recovery**: Retry mechanisms for failed operations

## Error Handling

- **Connection Errors**: Retry mechanism for Salesforce authentication
- **API Errors**: Clear error messages with recovery options
- **Validation Errors**: Prevents invalid operations (e.g., no fields selected)
- **Export Errors**: Graceful handling of export failures

## Performance Considerations

- **Lazy Loading**: Fields loaded only when object is selected
- **Pagination**: Large datasets handled with client-side pagination
- **Efficient Export**: Separate API call for complete dataset export
- **Caching**: Connection status and object lists cached during session

## Future Enhancements

- **Advanced Excel Features**: Consider using libraries like `xlsx` for richer Excel functionality
- **Saved Queries**: Allow users to save and reuse field selections
- **Batch Export**: Export multiple objects in a single operation
- **Data Transformation**: Basic field transformations before export
- **Scheduling**: Schedule regular data extractions
- **Progress Tracking**: Real-time progress for large extractions
