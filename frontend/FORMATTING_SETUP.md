# Formatting and Code Quality Setup

This project has been configured with comprehensive formatting and code quality tools to ensure consistent code style across the entire codebase.

## Tools Configured

### 1. Prettier

- **Purpose**: Code formatting
- **Config**: `.prettierrc.json`
- **Ignore**: `.prettierignore`
- **Features**:
  - Automatic code formatting
  - Tailwind CSS class sorting
  - Consistent indentation (2 spaces)
  - Single quotes for strings
  - Trailing commas where valid

### 2. ESLint

- **Purpose**: Code linting and error detection
- **Config**: `eslint.config.mjs`
- **Features**:
  - Next.js recommended rules
  - TypeScript support
  - Integration with Prettier
  - Custom rules for unused variables (prefix with `_`)

### 3. EditorConfig

- **Purpose**: Editor consistency
- **Config**: `.editorconfig`
- **Features**:
  - Consistent indentation across editors
  - Line ending normalization
  - Charset specification

### 4. TypeScript

- **Purpose**: Type checking
- **Config**: `tsconfig.json`
- **Features**:
  - Strict type checking
  - Path mapping for imports

## Available Scripts

```bash
# Development
npm run dev              # Start development server

# Code Quality
npm run lint             # Run ESLint
npm run lint:fix         # Run ESLint with auto-fix
npm run format           # Format all files with Prettier
npm run format:check     # Check if files are formatted
npm run type-check       # Run TypeScript type checking
npm run check-all        # Run all checks (type, lint, format)

# Build
npm run build            # Build for production
npm run start            # Start production server
```

## VS Code Integration

### Recommended Extensions

The project includes `.vscode/extensions.json` with recommended extensions:

- Prettier - Code formatter
- ESLint
- Tailwind CSS IntelliSense
- TypeScript and JavaScript Language Features
- EditorConfig for VS Code

### Workspace Settings

The project includes `.vscode/settings.json` with:

- Format on save enabled
- ESLint auto-fix on save
- Prettier as default formatter
- Consistent tab settings

## Pre-commit Hooks (Optional)

If you're using Git, you can set up pre-commit hooks:

```bash
# Initialize git repository (if not already done)
git init

# Install husky for git hooks
npx husky init

# Add pre-commit hook
echo "npx lint-staged" > .husky/pre-commit
```

This will automatically run linting and formatting on staged files before each commit.

## Formatting Rules

### Prettier Configuration

- **Print Width**: 100 characters
- **Tab Width**: 2 spaces
- **Semicolons**: Always
- **Quotes**: Single quotes
- **Trailing Commas**: ES5 compatible
- **Bracket Spacing**: True
- **Arrow Parens**: Avoid when possible

### ESLint Rules

- **Unused Variables**: Must be prefixed with `_` to be allowed
- **Any Type**: Warnings (not errors) to allow gradual typing
- **Prettier Integration**: Prettier rules take precedence for formatting

## Usage Guidelines

### For Developers

1. **Install recommended VS Code extensions** for the best experience
2. **Run `npm run check-all`** before committing changes
3. **Use `npm run lint:fix`** to automatically fix many linting issues
4. **Prefix unused variables with `_`** to avoid linting errors

### For CI/CD

Add these checks to your CI pipeline:

```bash
npm run type-check
npm run lint
npm run format:check
npm run build
```

## Troubleshooting

### Common Issues

1. **Prettier and ESLint conflicts**:
   - Solution: The configuration is set up to avoid conflicts
   - Run `npm run format` followed by `npm run lint:fix`

2. **Unused variable errors**:
   - Solution: Prefix unused variables with `_`
   - Example: `const _unusedVar = someValue;`

3. **Import/export formatting**:
   - Solution: Prettier handles this automatically
   - Run `npm run format` to fix

### Manual Fixes

If automatic fixes don't work:

1. Check the specific error message
2. Manually fix the issue
3. Run formatting again
4. Commit the changes

## Configuration Files Summary

- `.prettierrc.json` - Prettier configuration
- `.prettierignore` - Files to ignore for Prettier
- `.editorconfig` - Editor configuration
- `eslint.config.mjs` - ESLint configuration
- `.vscode/settings.json` - VS Code workspace settings
- `.vscode/extensions.json` - Recommended VS Code extensions
- `package.json` - Scripts and lint-staged configuration
