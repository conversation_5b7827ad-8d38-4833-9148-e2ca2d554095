'use client';

import { useState, useEffect } from 'react';
import AppLayout from '@/components/layout/AppLayout';
import { Stepper } from '@/components/ui/stepper';
import { FieldSelection } from '@/components/field-selection/field-selection';
import { DataPreview } from '@/components/data-preview/data-preview';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { salesforceOAuthApi } from '@/lib/api';
import { exportToExcel, generateExcelFilename, exportToActualExcel, generateActualExcelFilename, exportToSimpleExcel } from '@/lib/excel-export';
import {
  Database,
  Search,
  CheckCircle,
  AlertTriangle,
  Loader2,
  ArrowRight,
} from 'lucide-react';

type WizardStep = 'object-selection' | 'field-selection' | 'data-preview';

interface SalesforceObject {
  name: string;
  label: string;
  labelPlural: string;
  queryable: boolean;
  createable: boolean;
  updateable: boolean;
  deletable: boolean;
  custom: boolean;
  keyPrefix: string;
}

interface SalesforceField {
  name: string;
  label: string;
  type: string;
  required: boolean;
  custom: boolean;
  updateable: boolean;
  createable: boolean;
  length: number;
  picklistValues: string[];
}

const WIZARD_STEPS = [
  {
    id: 'object-selection',
    title: 'Select Object',
    description: 'Choose Salesforce object',
  },
  {
    id: 'field-selection',
    title: 'Select Fields',
    description: 'Choose fields to extract',
  },
  {
    id: 'data-preview',
    title: 'Preview & Export',
    description: 'Review and export data',
  },
];

// Object Selection Step Component
interface ObjectSelectionStepProps {
  objects: SalesforceObject[];
  isLoading: boolean;
  connectionStatus: 'checking' | 'connected' | 'failed';
  searchTerm: string;
  onSearchChange: (term: string) => void;
  selectedFilter: 'all' | 'standard' | 'custom';
  onFilterChange: (filter: 'all' | 'standard' | 'custom') => void;
  onObjectSelect: (objectName: string) => void;
  onRetryConnection: () => void;
}

function ObjectSelectionStep({
  objects,
  isLoading,
  connectionStatus,
  searchTerm,
  onSearchChange,
  selectedFilter,
  onFilterChange,
  onObjectSelect,
  onRetryConnection,
}: ObjectSelectionStepProps) {
  if (connectionStatus === 'checking') {
    return (
      <Card className="border-0 bg-white/80 shadow-xl backdrop-blur-sm">
        <CardContent className="flex items-center justify-center py-12">
          <div className="flex items-center space-x-3">
            <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
            <span className="text-lg text-slate-600">Connecting to Salesforce...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (connectionStatus === 'failed') {
    return (
      <Card className="border-0 bg-white/80 shadow-xl backdrop-blur-sm">
        <CardContent className="flex flex-col items-center justify-center py-12 space-y-4">
          <AlertTriangle className="h-12 w-12 text-red-500" />
          <div className="text-center">
            <h3 className="text-lg font-semibold text-slate-900">Connection Failed</h3>
            <p className="text-slate-600">Unable to connect to Salesforce</p>
          </div>
          <Button onClick={onRetryConnection} className="bg-blue-600 hover:bg-blue-700">
            Retry Connection
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-0 bg-white/80 shadow-xl backdrop-blur-sm">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-3 text-xl">
              <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-blue-600">
                <Database className="h-5 w-5 text-white" />
              </div>
              <span className="text-slate-900">Select Salesforce Object</span>
            </CardTitle>
            <CardDescription className="text-base text-slate-600 mt-2">
              Choose the Salesforce object you want to extract data from
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <span className="text-sm text-green-600 font-medium">Connected to Salesforce</span>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Search and Filters */}
        <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0 sm:space-x-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-slate-400" />
            <Input
              placeholder="Search objects by name or label..."
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant={selectedFilter === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => onFilterChange('all')}
            >
              All Objects ({objects.length})
            </Button>
            <Button
              variant={selectedFilter === 'standard' ? 'default' : 'outline'}
              size="sm"
              onClick={() => onFilterChange('standard')}
            >
              Standard
            </Button>
            <Button
              variant={selectedFilter === 'custom' ? 'default' : 'outline'}
              size="sm"
              onClick={() => onFilterChange('custom')}
            >
              Custom
            </Button>
          </div>
        </div>

        {/* Objects Grid */}
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center space-x-3">
              <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
              <span className="text-lg text-slate-600">Loading objects...</span>
            </div>
          </div>
        ) : objects.length > 0 ? (
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {objects.map((obj) => (
              <div
                key={obj.name}
                className="cursor-pointer rounded-lg border-2 border-slate-200 bg-white p-4 transition-all hover:border-blue-300 hover:shadow-md"
                onClick={() => onObjectSelect(obj.name)}
              >
                <div className="mb-2 flex items-center justify-between">
                  <h3 className="font-semibold text-slate-900">{obj.label}</h3>
                  <div className="flex space-x-1">
                    {obj.custom && (
                      <Badge variant="outline" className="border-purple-300 text-purple-600">
                        Custom
                      </Badge>
                    )}
                  </div>
                </div>
                <p className="text-sm text-slate-600 mb-3">{obj.labelPlural}</p>
                <div className="flex items-center justify-between">
                  <code className="text-xs text-slate-500 bg-slate-100 px-2 py-1 rounded">
                    {obj.name}
                  </code>
                  <ArrowRight className="h-4 w-4 text-slate-400" />
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Database className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-slate-900 mb-2">No objects found</h3>
            <p className="text-slate-600">Try adjusting your search or filter criteria</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default function WizardPage() {
  const [currentStep, setCurrentStep] = useState<WizardStep>('object-selection');
  const [selectedObject, setSelectedObject] = useState<string>('');
  const [selectedFields, setSelectedFields] = useState<string[]>([]);
  const [salesforceObjects, setSalesforceObjects] = useState<SalesforceObject[]>([]);
  const [objectFields, setObjectFields] = useState<SalesforceField[]>([]);
  const [extractedData, setExtractedData] = useState<Array<Record<string, any>>>([]);
  const [totalRecords, setTotalRecords] = useState(0);
  
  // Loading states
  const [isLoadingObjects, setIsLoadingObjects] = useState(false);
  const [isLoadingFields, setIsLoadingFields] = useState(false);
  const [isExtracting, setIsExtracting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  
  // Connection and error states
  const [connectionStatus, setConnectionStatus] = useState<'checking' | 'connected' | 'failed'>('checking');
  const [error, setError] = useState<string>('');
  
  // Search and filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'standard' | 'custom'>('all');

  // Check Salesforce connection and load objects on mount
  useEffect(() => {
    checkConnectionAndLoadObjects();
  }, []);

  const checkConnectionAndLoadObjects = async () => {
    try {
      setIsLoadingObjects(true);
      setError('');

      // Check connection
      const connectionResult = await salesforceOAuthApi.testConnection();
      
      if (connectionResult.authenticated) {
        setConnectionStatus('connected');
        
        // Load objects
        const objectsResult = await salesforceOAuthApi.getObjects();
        setSalesforceObjects(objectsResult.objects);
      } else {
        setConnectionStatus('failed');
        setError('Failed to connect to Salesforce. Please check your credentials.');
      }
    } catch (err) {
      setConnectionStatus('failed');
      setError(err instanceof Error ? err.message : 'Failed to connect to Salesforce');
    } finally {
      setIsLoadingObjects(false);
    }
  };

  const handleObjectSelect = async (objectName: string) => {
    try {
      setSelectedObject(objectName);
      setIsLoadingFields(true);
      setError('');

      const fieldsResult = await salesforceOAuthApi.getObjectFields(objectName);
      setObjectFields(fieldsResult.fields);
      setCurrentStep('field-selection');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load object fields');
    } finally {
      setIsLoadingFields(false);
    }
  };

  const handleExtractData = async () => {
    if (selectedFields.length === 0) {
      setError('Please select at least one field to extract');
      return;
    }

    try {
      setIsExtracting(true);
      setError('');

      const result = await salesforceOAuthApi.extractSelectedData(
        selectedObject,
        selectedFields,
        1000, // Extract up to 1000 records for preview
        false // Don't create migration job
      );

      setExtractedData(result.records);
      setTotalRecords(result.total_count);
      setCurrentStep('data-preview');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to extract data');
    } finally {
      setIsExtracting(false);
    }
  };

  const handleExportExcel = async (filteredData?: Array<Record<string, any>>, filters?: any[], format: 'csv' | 'excel' = 'excel') => {
    try {
      setIsExporting(true);
      setError('');

      let dataToExport = filteredData || extractedData;

      // If we have filtered data, use it directly
      // If we have less than the total records and no filters, extract all data
      if (!filteredData && extractedData.length < totalRecords) {
        const fullResult = await salesforceOAuthApi.extractSelectedData(
          selectedObject,
          selectedFields,
          totalRecords, // Extract all records
          false
        );
        dataToExport = fullResult.records;
      }

      const filterSuffix = filters && filters.length > 0 ? '_filtered' : '';

      if (format === 'excel') {
        const filename = generateActualExcelFilename(selectedObject, `salesforce_export${filterSuffix}`);

        try {
          // Try the xlsx library first
          await exportToActualExcel(dataToExport, selectedFields, { filename });

          // Show success message
          const filterText = filters && filters.length > 0 ? ` (${filters.length} filters applied)` : '';
          alert(`Successfully exported ${dataToExport.length} records to ${filename}${filterText}`);
        } catch (exportError) {
          console.log('xlsx library export failed, trying simple Excel export:', exportError);

          try {
            // Fallback to simple Excel export
            exportToSimpleExcel(dataToExport, selectedFields, { filename });

            const filterText = filters && filters.length > 0 ? ` (${filters.length} filters applied)` : '';
            alert(`Successfully exported ${dataToExport.length} records to ${filename}${filterText}\n\nNote: Using simplified Excel format.`);
          } catch (simpleError) {
            console.log('Simple Excel export failed, falling back to CSV:', simpleError);

            // Final fallback to CSV
            const csvFilename = filename.replace('.xlsx', '.csv');
            exportToExcel(dataToExport, selectedFields, { filename: csvFilename });

            const filterText = filters && filters.length > 0 ? ` (${filters.length} filters applied)` : '';
            alert(`Excel export failed. Successfully exported ${dataToExport.length} records to ${csvFilename}${filterText}\n\nNote: File is in CSV format.`);
          }
        }
      } else {
        const filename = generateExcelFilename(selectedObject, `salesforce_export${filterSuffix}`);
        exportToExcel(dataToExport, selectedFields, { filename });

        // Show success message
        const filterText = filters && filters.length > 0 ? ` (${filters.length} filters applied)` : '';
        alert(`Successfully exported ${dataToExport.length} records to ${filename}${filterText}\n\nNote: File is in CSV format, compatible with Excel.`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to export data');
    } finally {
      setIsExporting(false);
    }
  };

  // Filter objects based on search and filter criteria
  const filteredObjects = salesforceObjects.filter(obj => {
    const matchesSearch = !searchTerm || 
      obj.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      obj.label.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = selectedFilter === 'all' ||
      (selectedFilter === 'custom' && obj.custom) ||
      (selectedFilter === 'standard' && !obj.custom);
    
    return matchesSearch && matchesFilter && obj.queryable;
  });

  return (
    <AppLayout>
      <div className="p-8 max-w-7xl mx-auto">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-slate-900">Data Extraction Wizard</h1>
          <p className="mt-1 text-slate-600">Extract data from Salesforce objects with field selection and preview</p>
        </div>

        {/* Stepper */}
        <div className="mb-8">
          <Stepper steps={WIZARD_STEPS} currentStep={currentStep} />
        </div>

        {/* Error Alert */}
        {error && (
          <Alert className="mb-6 border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">{error}</AlertDescription>
          </Alert>
        )}

        {/* Step Content */}
        {currentStep === 'object-selection' && (
          <ObjectSelectionStep
            objects={filteredObjects}
            isLoading={isLoadingObjects}
            connectionStatus={connectionStatus}
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            selectedFilter={selectedFilter}
            onFilterChange={setSelectedFilter}
            onObjectSelect={handleObjectSelect}
            onRetryConnection={checkConnectionAndLoadObjects}
          />
        )}

        {currentStep === 'field-selection' && (
          <FieldSelection
            objectName={selectedObject}
            fields={objectFields}
            selectedFields={selectedFields}
            onFieldsChange={setSelectedFields}
            onExtract={handleExtractData}
            onBack={() => setCurrentStep('object-selection')}
            isLoading={isLoadingFields}
            isExtracting={isExtracting}
          />
        )}

        {currentStep === 'data-preview' && (
          <DataPreview
            objectName={selectedObject}
            data={extractedData}
            totalRecords={totalRecords}
            selectedFields={selectedFields}
            fieldMetadata={objectFields}
            onBack={() => setCurrentStep('field-selection')}
            onExportExcel={handleExportExcel}
            isExporting={isExporting}
          />
        )}
      </div>
    </AppLayout>
  );
}
