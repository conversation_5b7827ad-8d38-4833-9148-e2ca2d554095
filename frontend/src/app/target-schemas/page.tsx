'use client';

import { useState, useEffect } from 'react';
import { targetSchemasApi } from '@/lib/api';
import AppLayout from '@/components/layout/AppLayout';
// Using Tailwind CSS classes directly for consistent styling
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  Database,
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  <PERSON>tings,
  Target,
  CheckCircle,
  AlertTriangle,
} from 'lucide-react';

interface TargetField {
  name: string;
  label: string;
  type: string; // API returns string, we'll validate on the backend
  required: boolean;
  description?: string;
  defaultValue?: string;
  validation?: {
    minLength?: number;
    maxLength?: number;
    pattern?: string;
  };
}

interface TargetSchema {
  id: string;
  name: string;
  label: string;
  description: string;
  target_system: 'ops' | 'crm';
  fields: TargetField[];
  created_at: string;
  updated_at: string;
}

export default function TargetSchemasPage() {
  const [schemas, setSchemas] = useState<TargetSchema[]>([]);
  const [selectedSchema, setSelectedSchema] = useState<TargetSchema | null>(null);
  const [showNewSchemaDialog, setShowNewSchemaDialog] = useState(false);
  const [showNewFieldDialog, setShowNewFieldDialog] = useState(false);
  const [editingField, setEditingField] = useState<TargetField | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [selectedTargetSystem, setSelectedTargetSystem] = useState<'all' | 'ops' | 'crm'>('all');

  // Confirmation dialog states
  const [showDeleteFieldDialog, setShowDeleteFieldDialog] = useState(false);
  const [showDeleteSchemaDialog, setShowDeleteSchemaDialog] = useState(false);
  const [fieldToDelete, setFieldToDelete] = useState<string | null>(null);
  const [schemaToDelete, setSchemaToDelete] = useState<string | null>(null);

  // New schema form
  const [newSchemaForm, setNewSchemaForm] = useState({
    name: '',
    label: '',
    description: '',
    target_system: 'ops' as 'ops' | 'crm',
  });

  // New field form
  const [newFieldForm, setNewFieldForm] = useState<TargetField>({
    name: '',
    label: '',
    type: 'string',
    required: false,
    description: '',
    defaultValue: '',
  });

  // Load schemas from API on mount
  useEffect(() => {
    loadSchemas();
  }, []);

  const loadSchemas = async () => {
    setIsLoading(true);
    setError('');
    try {
      const data = await targetSchemasApi.getAll();
      setSchemas(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load schemas');
    } finally {
      setIsLoading(false);
    }
  };

  // Create new schema
  const handleCreateSchema = async () => {
    if (!newSchemaForm.name || !newSchemaForm.label) return;

    setIsLoading(true);
    setError('');

    try {
      const newSchema = await targetSchemasApi.create({
        id: newSchemaForm.name.toLowerCase().replace(/\s+/g, '_'),
        name: newSchemaForm.name,
        label: newSchemaForm.label,
        description: newSchemaForm.description,
        target_system: newSchemaForm.target_system,
        fields: [],
      });

      setSchemas([...schemas, newSchema]);
      setSelectedSchema(newSchema);
      setShowNewSchemaDialog(false);
      setNewSchemaForm({ name: '', label: '', description: '', target_system: 'ops' });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create schema');
    } finally {
      setIsLoading(false);
    }
  };

  // Add field to schema
  const handleAddField = async () => {
    if (!selectedSchema || !newFieldForm.name || !newFieldForm.label) return;

    setIsLoading(true);
    setError('');

    try {
      const updatedFields = [...selectedSchema.fields, { ...newFieldForm }];

      const updatedSchema = await targetSchemasApi.update(selectedSchema.id, {
        fields: updatedFields,
      });

      const updatedSchemas = schemas.map(s => (s.id === selectedSchema.id ? updatedSchema : s));
      setSchemas(updatedSchemas);
      setSelectedSchema(updatedSchema);
      setShowNewFieldDialog(false);
      setNewFieldForm({
        name: '',
        label: '',
        type: 'string',
        required: false,
        description: '',
        defaultValue: '',
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add field');
    } finally {
      setIsLoading(false);
    }
  };

  // Show delete field confirmation
  const handleDeleteFieldClick = (fieldName: string) => {
    setFieldToDelete(fieldName);
    setShowDeleteFieldDialog(true);
  };

  // Delete field from schema (after confirmation)
  const handleDeleteField = async () => {
    if (!selectedSchema || !fieldToDelete) return;

    setIsLoading(true);
    setError('');

    try {
      const updatedFields = selectedSchema.fields.filter(f => f.name !== fieldToDelete);

      const updatedSchema = await targetSchemasApi.update(selectedSchema.id, {
        fields: updatedFields,
      });

      const updatedSchemas = schemas.map(s => (s.id === selectedSchema.id ? updatedSchema : s));
      setSchemas(updatedSchemas);
      setSelectedSchema(updatedSchema);

      // Close dialog and reset state
      setShowDeleteFieldDialog(false);
      setFieldToDelete(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete field');
    } finally {
      setIsLoading(false);
    }
  };

  // Show delete schema confirmation
  const handleDeleteSchemaClick = (schemaId: string) => {
    setSchemaToDelete(schemaId);
    setShowDeleteSchemaDialog(true);
  };

  // Delete schema (after confirmation)
  const handleDeleteSchema = async () => {
    if (!schemaToDelete) return;

    setIsLoading(true);
    setError('');

    try {
      await targetSchemasApi.delete(schemaToDelete);
      const updatedSchemas = schemas.filter(s => s.id !== schemaToDelete);
      setSchemas(updatedSchemas);
      if (selectedSchema?.id === schemaToDelete) {
        setSelectedSchema(null);
      }

      // Close dialog and reset state
      setShowDeleteSchemaDialog(false);
      setSchemaToDelete(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete schema');
    } finally {
      setIsLoading(false);
    }
  };

  // Filter schemas by target system
  const filteredSchemas = schemas.filter(schema =>
    selectedTargetSystem === 'all' || schema.target_system === selectedTargetSystem
  );

  // Group schemas by target system
  const groupedSchemas = {
    ops: filteredSchemas.filter(s => s.target_system === 'ops'),
    crm: filteredSchemas.filter(s => s.target_system === 'crm')
  };

  const getFieldTypeColor = (type: string) => {
    const colors = {
      string: 'bg-blue-100 text-blue-800',
      number: 'bg-green-100 text-green-800',
      boolean: 'bg-purple-100 text-purple-800',
      date: 'bg-orange-100 text-orange-800',
      email: 'bg-pink-100 text-pink-800',
      url: 'bg-indigo-100 text-indigo-800',
      text: 'bg-gray-100 text-gray-800',
      json: 'bg-yellow-100 text-yellow-800',
    };
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <AppLayout>
      <div className='p-8'>
        {/* Page Header */}
        <div className='mb-8'>
          <div className='flex items-center justify-between'>
            <div>
              <h1 className='text-3xl font-bold text-slate-900'>Target Schemas</h1>
              <p className='mt-1 text-slate-600'>
                Configure your target system schemas for data transformation
              </p>
            </div>
            <Dialog open={showNewSchemaDialog} onOpenChange={setShowNewSchemaDialog}>
              <DialogTrigger asChild>
                <Button className='bg-blue-600 text-white hover:bg-blue-700'>
                  <Plus className='mr-2 h-4 w-4' />
                  New Schema
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Target Schema</DialogTitle>
                  <DialogDescription>
                    Define a new target system schema for data transformation
                  </DialogDescription>
                </DialogHeader>
                <div className='space-y-4'>
                  <div className='flex flex-col space-y-2'>
                    <Label htmlFor='schema-name'>Schema Name</Label>
                    <Input
                      id='schema-name'
                      placeholder='e.g., destination, customer, product'
                      value={newSchemaForm.name}
                      onChange={e => setNewSchemaForm({ ...newSchemaForm, name: e.target.value })}
                    />
                  </div>
                  <div className='flex flex-col space-y-2'>
                    <Label htmlFor='schema-label'>Display Label</Label>
                    <Input
                      id='schema-label'
                      placeholder='e.g., Destination, Customer, Product'
                      value={newSchemaForm.label}
                      onChange={e => setNewSchemaForm({ ...newSchemaForm, label: e.target.value })}
                    />
                  </div>
                  <div className='flex flex-col space-y-2'>
                    <Label htmlFor='schema-description'>Description</Label>
                    <Textarea
                      id='schema-description'
                      placeholder='Describe what this schema represents...'
                      value={newSchemaForm.description}
                      onChange={e =>
                        setNewSchemaForm({ ...newSchemaForm, description: e.target.value })
                      }
                    />
                  </div>
                  <div className='flex flex-col space-y-2'>
                    <Label htmlFor='target-system'>Target System</Label>
                    <Select
                      value={newSchemaForm.target_system}
                      onValueChange={(value: 'ops' | 'crm') =>
                        setNewSchemaForm({ ...newSchemaForm, target_system: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder='Select target system' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='ops'>Ops</SelectItem>
                        <SelectItem value='crm'>CRM</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className='flex justify-end space-x-2'>
                    <Button variant='outline' onClick={() => setShowNewSchemaDialog(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleCreateSchema} disabled={isLoading}>
                      {isLoading ? 'Creating...' : 'Create Schema'}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
        {/* Error Alert */}
        {error && (
          <Alert className='mb-6 border-red-200 bg-red-50'>
            <AlertTriangle className='h-4 w-4 text-red-600' />
            <AlertDescription className='text-red-800'>{error}</AlertDescription>
          </Alert>
        )}

        <div className='grid grid-cols-1 gap-8 lg:grid-cols-3'>
          {/* Schema List */}
          <div className='lg:col-span-1'>
            <Card className='rounded-xl border border-slate-200/60 bg-white/80 shadow-lg backdrop-blur-sm transition-all duration-300 hover:shadow-xl'>
              <CardHeader>
                <CardDescription className='text-base text-slate-600'>
                  Select a schema to view and edit its fields
                </CardDescription>
                <div className='flex flex-col gap-2 mt-4'>
                  <Label htmlFor='system-filter'>Filter by Target System</Label>
                  <Select
                    value={selectedTargetSystem}
                    onValueChange={(value: 'all' | 'ops' | 'crm') => setSelectedTargetSystem(value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='all'>All Systems</SelectItem>
                      <SelectItem value='ops'>Ops</SelectItem>
                      <SelectItem value='crm'>CRM</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardHeader>
              <CardContent className='space-y-2'>
                {isLoading ? (
                  <div className='py-4 text-center'>
                    <div className='mx-auto h-6 w-6 animate-spin rounded-full border-b-2 border-blue-600'></div>
                    <p className='mt-2 text-sm text-slate-500'>Loading schemas...</p>
                  </div>
                ) : filteredSchemas.length === 0 ? (
                  <div className='py-4 text-center'>
                    <p className='text-sm text-slate-500'>
                      {selectedTargetSystem === 'all' ? 'No schemas found' : `No ${selectedTargetSystem.toUpperCase()} schemas found`}
                    </p>
                  </div>
                ) : (
                  filteredSchemas.map(schema => (
                    <div
                      key={schema.id}
                      className={`group cursor-pointer rounded-xl border p-3 transition-all duration-300 ${
                        selectedSchema?.id === schema.id
                          ? 'border-blue-300 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-lg ring-2 ring-blue-200/50'
                          : 'border-slate-200/60 bg-white/80 backdrop-blur-sm hover:border-blue-200 hover:bg-white hover:shadow-lg'
                      }`}
                      onClick={() => setSelectedSchema(schema)}
                    >
                      <div className='flex items-center justify-between'>
                        <div className='flex items-center space-x-3'>
                          {/* <div
                            className={`flex h-10 w-10 items-center justify-center rounded-xl shadow-md transition-all duration-300 ${
                              selectedSchema?.id === schema.id
                                ? 'scale-110 bg-gradient-to-br from-blue-500 to-blue-600'
                                : 'bg-gradient-to-br from-slate-400 to-slate-500 group-hover:from-blue-400 group-hover:to-blue-500'
                            }`}
                          >
                            <Target className='h-5 w-5 text-white' />
                          </div> */}
                          <div>
                            <h3 className='text-base font-semibold text-slate-900'>
                              {schema.label}
                            </h3>
                            <div className='mt-1 flex items-center space-x-2'>
                              <span className='text-sm text-slate-500'>
                                {schema.fields.length} fields
                              </span>
                              <span className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                                schema.target_system === 'ops'
                                  ? 'bg-blue-100 text-blue-800'
                                  : 'bg-green-100 text-green-800'
                              }`}>
                                {schema.target_system === 'ops' ? 'Ops' : 'CRM'}
                              </span>
                            </div>
                          </div>
                        </div>
                        <Button
                          variant='ghost'
                          size='sm'
                          className='text-red-500 opacity-0 transition-all duration-200 group-hover:opacity-100 hover:bg-red-50 hover:text-red-700'
                          onClick={e => {
                            e.stopPropagation();
                            handleDeleteSchemaClick(schema.id);
                          }}
                        >
                          <Trash2 className='h-4 w-4' />
                        </Button>
                      </div>
                    </div>
                  ))
                )}
              </CardContent>
            </Card>
          </div>

          {/* Schema Details */}
          <div className='lg:col-span-2'>
            {selectedSchema ? (
              <Card className='rounded-xl border border-slate-200/60 bg-white/80 shadow-lg backdrop-blur-sm'>
                <CardHeader>
                  <div className='flex items-center justify-between'>
                    <div className='flex items-center space-x-4'>
                      <div className='flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-indigo-500 to-purple-600 shadow-lg'>
                        <Settings className='h-6 w-6 text-white' />
                      </div>
                      <div>
                        <CardTitle className='text-2xl font-bold text-slate-900'>
                          {selectedSchema.label}
                        </CardTitle>
                        <CardDescription className='mt-1 text-base text-slate-600'>
                          {selectedSchema.description}
                        </CardDescription>
                      </div>
                    </div>
                    <Dialog open={showNewFieldDialog} onOpenChange={setShowNewFieldDialog}>
                      <DialogTrigger asChild>
                        <Button className='bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg hover:from-blue-700 hover:to-indigo-700'>
                          <Plus className='mr-2 h-4 w-4' />
                          Add Field
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Add New Field</DialogTitle>
                          <DialogDescription>
                            Add a new field to the {selectedSchema.label} schema
                          </DialogDescription>
                        </DialogHeader>
                        <div className='space-y-4'>
                          <div className='grid grid-cols-2 gap-4'>
                            <div className='flex flex-col space-y-2'>
                              <Label htmlFor='field-name'>Field Name</Label>
                              <Input
                                id='field-name'
                                placeholder='e.g., title, price, email'
                                value={newFieldForm.name}
                                onChange={e =>
                                  setNewFieldForm({ ...newFieldForm, name: e.target.value })
                                }
                              />
                            </div>
                            <div className='flex flex-col space-y-2'>
                              <Label htmlFor='field-label'>Display Label</Label>
                              <Input
                                id='field-label'
                                placeholder='e.g., Title, Price, Email'
                                value={newFieldForm.label}
                                onChange={e =>
                                  setNewFieldForm({ ...newFieldForm, label: e.target.value })
                                }
                              />
                            </div>
                          </div>
                          <div className='grid grid-cols-2 gap-4'>
                            <div className='flex flex-col space-y-2'>
                              <Label htmlFor='field-type'>Field Type</Label>
                              <Select
                                value={newFieldForm.type}
                                onValueChange={(value: any) =>
                                  setNewFieldForm({ ...newFieldForm, type: value })
                                }
                              >
                                <SelectTrigger className='w-full'>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value='string'>String</SelectItem>
                                  <SelectItem value='number'>Number</SelectItem>
                                  <SelectItem value='boolean'>Boolean</SelectItem>
                                  <SelectItem value='date'>Date</SelectItem>
                                  <SelectItem value='email'>Email</SelectItem>
                                  <SelectItem value='url'>URL</SelectItem>
                                  <SelectItem value='text'>Text (Long)</SelectItem>
                                  <SelectItem value='json'>JSON</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className='flex items-center space-x-2 pt-6'>
                              <input
                                type='checkbox'
                                id='field-required'
                                checked={newFieldForm.required}
                                onChange={e =>
                                  setNewFieldForm({ ...newFieldForm, required: e.target.checked })
                                }
                                className='rounded border-slate-300'
                              />
                              <Label htmlFor='field-required'>Required Field</Label>
                            </div>
                          </div>
                          <div className='flex flex-col space-y-2'>
                            <Label htmlFor='field-description'>Description</Label>
                            <Textarea
                              id='field-description'
                              placeholder='Describe what this field represents...'
                              value={newFieldForm.description}
                              onChange={e =>
                                setNewFieldForm({ ...newFieldForm, description: e.target.value })
                              }
                            />
                          </div>
                          <div className='flex flex-col space-y-2'>
                            <Label htmlFor='field-default'>Default Value (Optional)</Label>
                            <Input
                              id='field-default'
                              placeholder='Default value for this field'
                              value={newFieldForm.defaultValue}
                              onChange={e =>
                                setNewFieldForm({ ...newFieldForm, defaultValue: e.target.value })
                              }
                            />
                          </div>
                          <div className='flex justify-end space-x-2'>
                            <Button variant='outline' onClick={() => setShowNewFieldDialog(false)}>
                              Cancel
                            </Button>
                            <Button onClick={handleAddField} disabled={isLoading}>
                              {isLoading ? 'Adding...' : 'Add Field'}
                            </Button>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </CardHeader>
                <CardContent className='p-6 pt-0'>
                  {selectedSchema.fields.length === 0 ? (
                    <div className='py-12 text-center'>
                      <div className='mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-slate-100 to-slate-200'>
                        <Database className='h-8 w-8 text-slate-400' />
                      </div>
                      <h3 className='mb-3 text-xl font-semibold text-slate-900'>
                        No Fields Defined
                      </h3>
                      <p className='mx-auto mb-6 max-w-md text-slate-600'>
                        Start by adding fields to define your target schema structure. Fields
                        represent the data columns in your destination system.
                      </p>
                      <Button
                        onClick={() => setShowNewFieldDialog(true)}
                        className='bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg hover:from-blue-700 hover:to-indigo-700'
                      >
                        <Plus className='mr-2 h-4 w-4' />
                        Add Your First Field
                      </Button>
                    </div>
                  ) : (
                    <div className='space-y-4'>
                      <div className='mb-6 flex items-center justify-between'>
                        <h3 className='text-lg font-semibold text-slate-900'>Schema Fields</h3>
                        <span className='rounded-full bg-slate-100 px-3 py-1 text-sm text-slate-500'>
                          {selectedSchema.fields.length} field
                          {selectedSchema.fields.length !== 1 ? 's' : ''}
                        </span>
                      </div>
                      {selectedSchema.fields.map(field => (
                        <div
                          key={field.name}
                          className='group flex items-center justify-between rounded-xl border border-slate-200/60 bg-white/80 p-5 backdrop-blur-sm transition-all duration-200 hover:shadow-md'
                        >
                          <div className='flex-1'>
                            <div className='flex items-center space-x-3'>
                              <div className='flex items-center space-x-2'>
                                <div className='h-2 w-2 rounded-full bg-gradient-to-r from-blue-500 to-indigo-500'></div>
                                <span className='text-base font-semibold text-slate-900'>
                                  {field.label}
                                </span>
                              </div>
                              <code className='rounded-md bg-slate-100 px-2 py-1 font-mono text-sm text-slate-700'>
                                {field.name}
                              </code>
                              <Badge className={`${getFieldTypeColor(field.type)} font-medium`}>
                                {field.type}
                              </Badge>
                              {field.required && (
                                <Badge className='border-red-200 bg-red-100 text-xs font-medium text-red-700'>
                                  Required
                                </Badge>
                              )}
                            </div>
                            {field.description && (
                              <p className='mb-2 text-sm leading-relaxed text-slate-600'>
                                {field.description}
                              </p>
                            )}
                            {field.defaultValue && (
                              <div className='flex items-center space-x-2'>
                                <span className='text-xs text-slate-500'>Default:</span>
                                <code className='rounded border bg-slate-50 px-2 py-1 text-xs text-slate-600'>
                                  {field.defaultValue}
                                </code>
                              </div>
                            )}
                          </div>
                          <div className='flex items-center space-x-2 opacity-0 transition-opacity duration-200 group-hover:opacity-100'>
                            <Button
                              variant='ghost'
                              size='sm'
                              onClick={() => setEditingField(field)}
                              className='text-slate-500 hover:bg-slate-100 hover:text-slate-700'
                            >
                              <Edit className='h-4 w-4' />
                            </Button>
                            <Button
                              variant='ghost'
                              size='sm'
                              onClick={() => handleDeleteFieldClick(field.name)}
                              className='text-red-500 hover:bg-red-50 hover:text-red-700'
                            >
                              <Trash2 className='h-4 w-4' />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            ) : (
              <Card className='rounded-xl border border-slate-200/60 bg-white/80 shadow-lg backdrop-blur-sm'>
                <CardContent className='flex items-center justify-center py-20'>
                  <div className='max-w-md text-center'>
                    <div className='mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-to-br from-slate-100 to-slate-200'>
                      <Target className='h-10 w-10 text-slate-400' />
                    </div>
                    <h3 className='mb-3 text-2xl font-bold text-slate-900'>
                      Select a Target Schema
                    </h3>
                    <p className='mb-8 leading-relaxed text-slate-600'>
                      Choose a schema from the list to view and edit its field configuration. Target
                      schemas define the structure of your destination data.
                    </p>
                    <Button
                      onClick={() => setShowNewSchemaDialog(true)}
                      className='bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg hover:from-blue-700 hover:to-indigo-700'
                    >
                      <Plus className='mr-2 h-4 w-4' />
                      Create Your First Schema
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Schema Usage Info */}
        {selectedSchema && (
          <Card className='mt-8'>
            <CardHeader>
              <CardTitle className='flex items-center space-x-2'>
                <CheckCircle className='h-5 w-5 text-green-600' />
                <span>Schema Usage</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Alert>
                <AlertTriangle className='h-4 w-4' />
                <AlertDescription>
                  <strong>How to use this schema:</strong> This schema definition will be available
                  during the transformation step in your migration flow. You can map Salesforce
                  fields to these target fields when configuring data transformations.
                  <br />
                  <br />
                  <strong>Schema ID:</strong>{' '}
                  <code className='rounded bg-slate-100 px-1 py-0.5'>{selectedSchema.id}</code> -
                  Use this ID when referencing this schema in transformations.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Delete Field Confirmation Dialog */}
      <Dialog open={showDeleteFieldDialog} onOpenChange={setShowDeleteFieldDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Field</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the field "{fieldToDelete}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowDeleteFieldDialog(false);
                setFieldToDelete(null);
              }}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteField}
              disabled={isLoading}
            >
              {isLoading ? 'Deleting...' : 'Delete Field'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Schema Confirmation Dialog */}
      <Dialog open={showDeleteSchemaDialog} onOpenChange={setShowDeleteSchemaDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Schema</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the schema "{schemas.find(s => s.id === schemaToDelete)?.label}"?
              This will permanently remove the schema and all its fields. This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowDeleteSchemaDialog(false);
                setSchemaToDelete(null);
              }}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteSchema}
              disabled={isLoading}
            >
              {isLoading ? 'Deleting...' : 'Delete Schema'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AppLayout>
  );
}
