import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Database,
  Upload,
  Settings,
  Activity,
  Play,
  RefreshCw,
  FileText,
  Target,
  Eye,
} from 'lucide-react';
import Link from 'next/link';
import AppLayout from '@/components/layout/AppLayout';

export default function Home() {
  return (
    <AppLayout>
      <div className='p-8'>
        {/* Page Header */}
        <div className='mb-8'>
          <div className='flex items-center justify-between'>
            <div>
              <h1 className='text-3xl font-bold text-slate-900'>Dashboard</h1>
              <p className='mt-1 text-slate-600'>Data Migration & ETL Platform</p>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className='mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4'>
          <Card className='rounded-lg border border-slate-200 bg-white shadow-sm'>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium text-slate-700'>Active Jobs</CardTitle>
              <div className='flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100'>
                <Activity className='h-4 w-4 text-blue-600' />
              </div>
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold text-slate-900'>0</div>
              <p className='text-xs text-slate-500'>Currently running</p>
            </CardContent>
          </Card>

          <Card className='rounded-lg border border-slate-200 bg-white shadow-sm'>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium text-slate-700'>Total Jobs</CardTitle>
              <div className='flex h-8 w-8 items-center justify-center rounded-lg bg-green-100'>
                <Database className='h-4 w-4 text-green-600' />
              </div>
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold text-slate-900'>20,000</div>
              <p className='text-xs text-slate-500'>Created</p>
            </CardContent>
          </Card>

          <Card className='rounded-lg border border-slate-200 bg-white shadow-sm'>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium text-slate-700'>Records Staged</CardTitle>
              <div className='flex h-8 w-8 items-center justify-center rounded-lg bg-yellow-100'>
                <RefreshCw className='h-4 w-4 text-yellow-600' />
              </div>
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold text-slate-900'>20,000</div>
              <p className='text-xs text-slate-500'>Ready for review</p>
            </CardContent>
          </Card>

          <Card className='rounded-lg border border-slate-200 bg-white shadow-sm'>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium text-slate-700'>Records Synced</CardTitle>
              <div className='flex h-8 w-8 items-center justify-center rounded-lg bg-green-100'>
                <Upload className='h-4 w-4 text-green-600' />
              </div>
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold text-slate-900'>10,000</div>
              <p className='text-xs text-slate-500'>Successfully migrated</p>
            </CardContent>
          </Card>
        </div>

        {/* Main Actions */}
        <div className='mb-8 grid grid-cols-1 gap-8 lg:grid-cols-2'>
          <Card className='rounded-lg border border-slate-200 bg-white shadow-lg'>
            <CardHeader>
              <CardTitle className='flex items-center space-x-3'>
                <div className='flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-blue-600'>
                  <Play className='h-5 w-5 text-white' />
                </div>
                <span className='text-slate-900'>Start Migration</span>
              </CardTitle>
              <CardDescription className='text-slate-600'>
                Begin a new data migration from Salesforce
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <p className='text-sm text-slate-500'>
                  Select object → Review data → Sync to target
                </p>
                <Link href='/migration'>
                  <Button className='w-full bg-blue-600 text-white hover:bg-blue-700'>
                    <Upload className='mr-2 h-4 w-4' />
                    New Migration
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>

          <Card className='rounded-lg border border-slate-200 bg-white shadow-lg'>
            <CardHeader>
              <CardTitle className='flex items-center space-x-3'>
                <div className='flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-slate-500 to-slate-600'>
                  <Target className='h-5 w-5 text-white' />
                </div>
                <span className='text-slate-900'>Target Schemas</span>
              </CardTitle>
              <CardDescription className='text-slate-600'>
                Configure target system schemas for data transformation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <p className='text-sm text-slate-500'>
                  Define destination fields → Map during transformation
                </p>
                <Link href='/target-schemas'>
                  <Button variant='outline' className='w-full'>
                    <Settings className='mr-2 h-4 w-4' />
                    Configure Schemas
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Secondary Actions */}
        <div className='mb-8 grid grid-cols-1 gap-8 lg:grid-cols-2'>
          <Card className='rounded-lg border border-slate-200 bg-white shadow-sm'>
            <CardHeader>
              <CardTitle className='flex items-center space-x-3'>
                <div className='flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-indigo-500 to-indigo-600'>
                  <FileText className='h-5 w-5 text-white' />
                </div>
                <span className='text-slate-900'>Staged Data</span>
              </CardTitle>
              <CardDescription className='text-slate-600'>
                Review and manage staged migration data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <div className='flex justify-between text-sm'>
                  <span className='text-slate-600'>Pending Review:</span>
                  <span className='font-medium text-slate-900'>0 records</span>
                </div>
                <Link href='/staged-data'>
                  <Button variant='outline' className='w-full'>
                    <Database className='mr-2 h-4 w-4' />
                    View Data
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>

          <Card className='rounded-lg border border-slate-200 bg-white shadow-sm'>
            <CardHeader>
              <CardTitle className='flex items-center space-x-3'>
                <div className='flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-purple-500 to-purple-600'>
                  <Settings className='h-5 w-5 text-white' />
                </div>
                <span className='text-slate-900'>System Settings</span>
              </CardTitle>
              <CardDescription className='text-slate-600'>
                Configure connections and preferences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <p className='text-sm text-slate-500'>
                  Salesforce connections, API settings, and more
                </p>
                <Button variant='outline' className='w-full' disabled>
                  <Settings className='mr-2 h-4 w-4' />
                  Coming Soon
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card className='rounded-lg border border-slate-200 bg-white shadow-sm'>
          <CardHeader className='flex flex-row items-center justify-between'>
            <div>
              <CardTitle className='text-slate-900'>Recent Jobs</CardTitle>
              <CardDescription className='text-slate-600'>
                Latest migration activity
              </CardDescription>
            </div>
            <Link href='/migrations'>
              <Button variant='outline' size='sm'>
                <Eye className='mr-2 h-4 w-4' />
                View All
              </Button>
            </Link>
          </CardHeader>
          <CardContent>
            <div className='py-8 text-center'>
              <div className='mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-slate-100'>
                <Activity className='h-8 w-8 text-slate-400' />
              </div>
              <p className='text-sm font-medium text-slate-600'>No migration jobs found</p>
              <p className='mt-1 text-xs text-slate-500'>
                Jobs will appear here once you start a migration
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
