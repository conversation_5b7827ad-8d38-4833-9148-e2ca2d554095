'use client';

import { useState } from 'react';
import AppLayout from '@/components/layout/AppLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Layers,
  Database,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from 'lucide-react';

// Mock data for staged items
const MOCK_STAGED_ITEMS = [
  {
    id: '1',
    name: 'Salesforce Accounts',
    records: 15432,
    status: 'Ready',
    stagedAt: '2 hours ago',
    statusColor: 'green',
  },
  {
    id: '2',
    name: 'Contact Records',
    records: 8921,
    status: 'Ready',
    stagedAt: '1 day ago',
    statusColor: 'green',
  },
  {
    id: '3',
    name: 'Opportunity Data',
    records: 3245,
    status: 'Processing',
    stagedAt: '3 days ago',
    statusColor: 'yellow',
  },
  {
    id: '4',
    name: 'Lead Information',
    records: 12567,
    status: 'Ready',
    stagedAt: '5 hours ago',
    statusColor: 'green',
  },
  {
    id: '5',
    name: 'Product Catalog',
    records: 2890,
    status: 'Failed',
    stagedAt: '1 week ago',
    statusColor: 'red',
  },
  {
    id: '6',
    name: 'Campaign Data',
    records: 1456,
    status: 'Ready',
    stagedAt: '2 days ago',
    statusColor: 'green',
  },
  {
    id: '7',
    name: 'Case Records',
    records: 7823,
    status: 'Processing',
    stagedAt: '6 hours ago',
    statusColor: 'yellow',
  },
  {
    id: '8',
    name: 'Task Management',
    records: 4567,
    status: 'Ready',
    stagedAt: '4 hours ago',
    statusColor: 'green',
  },
  {
    id: '9',
    name: 'Event Calendar',
    records: 2134,
    status: 'Ready',
    stagedAt: '1 day ago',
    statusColor: 'green',
  },
  {
    id: '10',
    name: 'User Permissions',
    records: 892,
    status: 'Failed',
    stagedAt: '3 days ago',
    statusColor: 'red',
  },
];

export default function StagePage() {
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);
  const [searchTerm, setSearchTerm] = useState('');

  // Filter data based on search term
  const filteredItems = MOCK_STAGED_ITEMS.filter(
    item =>
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.status.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Calculate pagination
  const totalItems = filteredItems.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentItems = filteredItems.slice(startIndex, endIndex);

  // Pagination handlers
  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  const goToFirstPage = () => goToPage(1);
  const goToPreviousPage = () => goToPage(currentPage - 1);
  const goToNextPage = () => goToPage(currentPage + 1);
  const goToLastPage = () => goToPage(totalPages);

  // Status badge styling
  const getStatusBadgeClass = (statusColor: string) => {
    switch (statusColor) {
      case 'green':
        return 'bg-green-100 text-green-700';
      case 'yellow':
        return 'bg-yellow-100 text-yellow-700';
      case 'red':
        return 'bg-red-100 text-red-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  return (
    <AppLayout>
      <div className='p-8'>
        {/* Page Header */}
        <div className='mb-8'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center space-x-4'>
              <div className='flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 shadow-lg'>
                <Layers className='h-6 w-6 text-white' />
              </div>
              <div>
                <h1 className='text-3xl font-bold text-slate-900'>Stage</h1>
                <p className='mt-1 text-slate-600'>
                  Manage and review staged data before migration
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Stages List */}
        <Card className='rounded-xl border border-slate-200/60 bg-white/80 shadow-lg backdrop-blur-sm'>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <div className='flex items-center space-x-3'>
                <div>
                  <CardTitle className='text-xl font-bold text-slate-900'>Staged Data</CardTitle>
                </div>
              </div>
              <Button className='bg-blue-600 text-white hover:bg-blue-700'>
                <Database className='mr-2 h-4 w-4' />
                New Stage
              </Button>
            </div>
          </CardHeader>
          <CardContent className='space-y-4'>
            {/* Search Filter */}
            <div className='flex items-center space-x-2'>
              <Input
                placeholder='Search staged data...'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className='max-w-sm'
              />
            </div>

            {/* Table */}
            <div className='rounded-md border'>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Records</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Staged</TableHead>
                    <TableHead className='text-right'>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {currentItems.length > 0 ? (
                    currentItems.map(item => (
                      <TableRow key={item.id} className='group'>
                        <TableCell>
                          <div className='font-semibold text-slate-900'>{item.name}</div>
                        </TableCell>
                        <TableCell>
                          <span className='text-sm text-slate-600'>
                            {item.records.toLocaleString()} records
                          </span>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant='secondary'
                            className={getStatusBadgeClass(item.statusColor)}
                          >
                            {item.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <span className='text-sm text-slate-500'>{item.stagedAt}</span>
                        </TableCell>
                        <TableCell className='text-right'>
                          <div className='flex items-center justify-end space-x-2 opacity-0 transition-opacity duration-200 group-hover:opacity-100'>
                            <Button variant='outline' size='sm'>
                              View
                            </Button>
                            <Button
                              variant='outline'
                              size='sm'
                              className='text-red-500 hover:bg-red-50 hover:text-red-700'
                            >
                              Delete
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} className='h-24 text-center'>
                        No staged data found.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            <div className='flex items-center justify-between space-x-2 py-4'>
              <div className='text-muted-foreground text-sm'>
                Showing {startIndex + 1} to {Math.min(endIndex, totalItems)} of {totalItems} results
              </div>
              <div className='flex items-center space-x-2'>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={goToFirstPage}
                  disabled={currentPage === 1}
                >
                  <ChevronsLeft className='h-4 w-4' />
                </Button>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={goToPreviousPage}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className='h-4 w-4' />
                </Button>
                <span className='text-sm'>
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={goToNextPage}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className='h-4 w-4' />
                </Button>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={goToLastPage}
                  disabled={currentPage === totalPages}
                >
                  <ChevronsRight className='h-4 w-4' />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
