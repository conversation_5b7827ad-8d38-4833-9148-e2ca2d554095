'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Database,
  Download,
  Eye,
  Upload,
  CheckCircle,
  Loader2,
  ArrowRight,
  RefreshCw,
  AlertTriangle,
  Edit,
  Save,
  X,
  Settings,
} from 'lucide-react';
import { salesforceOAuthApi, targetSchemasApi, loadApi, stagedDataApi } from '@/lib/api';
import AppLayout from '@/components/layout/AppLayout';
import { FieldMappingInterface } from '@/components/field-mapping/field-mapping-interface';
import { transformationService, type FieldMapping } from '@/lib/transformation-service';
import { MigrationDataTable } from '@/components/data-table/migration-data-table';
// Using Tailwind CSS classes directly for consistent styling

type Step = 'select' | 'extract' | 'transform' | 'edit' | 'load' | 'complete';

interface TargetField {
  name: string;
  label: string;
  type: string;
  required: boolean;
  description?: string;
  defaultValue?: string;
}

interface SalesforceObject {
  name: string;
  label: string;
  labelPlural: string;
  queryable: boolean;
  createable: boolean;
  updateable: boolean;
  deletable: boolean;
  custom: boolean;
  keyPrefix: string;
}

interface SalesforceField {
  name: string;
  label: string;
  type: string;
  required: boolean;
  custom: boolean;
  updateable: boolean;
  createable: boolean;
  length: number;
  picklistValues: string[];
}

export default function MigrationFlowPage() {
  const [currentStep, setCurrentStep] = useState<Step>('select');
  const [selectedObject, setSelectedObject] = useState<string>('');
  const [salesforceObjects, setSalesforceObjects] = useState<SalesforceObject[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [editedData, setEditedData] = useState<any[]>([]);
  const [error, setError] = useState<string>('');
  const [connectionStatus, setConnectionStatus] = useState<'checking' | 'connected' | 'failed'>(
    'checking'
  );
  const [isPageLoading, setIsPageLoading] = useState(true); // Add page loading state

  // Migration job tracking
  const [migrationJobId, setMigrationJobId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [objectFilter, setObjectFilter] = useState<'all' | 'custom' | 'standard'>('all');
  const [isLoadingObjects, setIsLoadingObjects] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(12); // Show 12 objects per page

  const [originalData, setOriginalData] = useState<any[]>([]);
  const [extractedData, setExtractedData] = useState<any[]>([]); // Preserve original extracted data

  // Field selection modal state
  const [showFieldModal, setShowFieldModal] = useState(false);
  const [availableFields, setAvailableFields] = useState<SalesforceField[]>([]);
  const [selectedFields, setSelectedFields] = useState<string[]>([]);
  const [isLoadingFields, setIsLoadingFields] = useState(false);
  const [fieldSearchQuery, setFieldSearchQuery] = useState('');
  const [fieldFilter, setFieldFilter] = useState<'all' | 'required' | 'custom'>('all');

  // Table pagination state
  const [tablePage, setTablePage] = useState(1);
  const [tablePageSize, setTablePageSize] = useState(10);
  const [totalRecords, setTotalRecords] = useState(0);

  // Transformation state
  const [selectedTargetSchema, setSelectedTargetSchema] = useState<string | null>(null);
  const [fieldMappings, setFieldMappings] = useState<FieldMapping[]>([]);
  const [removeUnmappedFields, setRemoveUnmappedFields] = useState(true);
  const [transformedData, setTransformedData] = useState<any[]>([]);
  const [targetSchemas, setTargetSchemas] = useState<any[]>([]);
  const [transformationSummary, setTransformationSummary] = useState<any>(null);
  const [loadResult, setLoadResult] = useState<{
    success: boolean;
    total_records: number;
    successful_records: number;
    failed_records: number;
    errors: string[];
    message: string;
  } | null>(null);
  const [modifiedCells, setModifiedCells] = useState<Set<string>>(new Set()); // Track modified cells
  const [stagedDataRecords, setStagedDataRecords] = useState<any[]>([]); // Store actual staged data records with IDs

  // Function to load existing migration data when continuing from migrations list
  const loadExistingMigration = async (jobId: string, forceStep?: Step) => {
    try {
      setIsLoading(true);
      setMigrationJobId(jobId);

      // Get migration details to determine current step and load data
      const migrationDetails = await salesforceOAuthApi.getMigrationDetails(jobId);

      if (migrationDetails && migrationDetails.migration) {
        const migration = migrationDetails.migration;

        console.log('Loading existing migration:', {
          jobId,
          migration,
          forceStep,
          records_extracted: migration.records_extracted,
          records_staged: migration.records_staged,
          raw_data_count: migration.raw_data_count,
          status: migration.status
        });

        // Set the object and step based on migration status
        setSelectedObject(migration.object_name);

        // Use forced step if provided, otherwise determine based on migration status
        if (forceStep) {
          console.log('Using forced step:', forceStep);
          setCurrentStep(forceStep);
          // Load appropriate data based on the forced step
          if (forceStep === 'edit') {
            console.log('Loading staged data for forced edit step');
            // Load staged data for editing - pass object name directly
            await loadStagedDataForEditing(jobId, migration.object_name);
          }
        } else {
          // Determine which step to show based on migration status and data availability
          if (migration.status === 'completed') {
            setCurrentStep('complete');
          } else if (migration.records_staged > 0) {
            setCurrentStep('edit');
            // Load staged data for editing
            await loadStagedDataForEditing(jobId, migration.object_name);
          } else if (migration.records_extracted > 0 && migration.raw_data_count > 0) {
            setCurrentStep('transform');
            // Try to load raw data for transformation
            await loadRawDataForTransform(jobId, migration);
          } else {
            // Migration exists but no data extracted yet, start from extract step
            setCurrentStep('extract');
          }
        }
      }
    } catch (err) {
      console.error('Failed to load existing migration:', err);
      setError('Failed to load existing migration. Starting new migration.');
      setCurrentStep('select');
    } finally {
      setIsLoading(false);
    }
  };

  // Function to load raw data for transformation step
  const loadRawDataForTransform = async (jobId: string, migration: any) => {
    try {
      // Since we don't have a direct raw data API yet, we can re-extract using the same parameters
      // This is a temporary solution until we add a getRawData API endpoint
      if (migration.source_config && migration.source_config.fields && migration.source_config.object_name) {
        const result = await salesforceOAuthApi.extractSelectedData(
          migration.source_config.object_name,
          migration.source_config.fields,
          migration.source_config.limit || 100,
          false // Don't create a new job
        );

        setExtractedData([...result.records]);
        setOriginalData([...result.records]);
        setEditedData([...result.records]);
        setTotalRecords(result.records.length);
        setTablePage(1);
      } else {
        // Fallback: if we can't re-extract, go to extract step
        setCurrentStep('extract');
      }
    } catch (err) {
      console.error('Failed to load raw data for transformation:', err);
      // Fallback to extract step if we can't load the data
      setCurrentStep('extract');
    }
  };

  // Test Salesforce connection and load objects on component mount
  useEffect(() => {
    const initializeSalesforce = async () => {
      try {
        setConnectionStatus('checking');
        setIsPageLoading(true); // Start page loading

        // Check if we have a job_id in the URL to continue an existing migration
        const urlParams = new URLSearchParams(window.location.search);
        const jobId = urlParams.get('job_id');
        const stepParam = urlParams.get('step') as Step;

        console.log('URL params:', { jobId, stepParam });

        // Test connection
        const connectionResult = await salesforceOAuthApi.testConnection();

        if (connectionResult.authenticated) {
          setConnectionStatus('connected');

          // Load available objects with loading state
          setIsLoadingObjects(true);
          const objectsResult = await salesforceOAuthApi.getObjects();
          setSalesforceObjects(objectsResult.objects);
          setIsLoadingObjects(false);

          // Load target schemas
          try {
            const schemas = await targetSchemasApi.getAll();
            setTargetSchemas(schemas);
            console.log('Loaded target schemas:', schemas.length);
          } catch (schemaError) {
            console.error('Failed to load target schemas:', schemaError);
            setError('Failed to load target schemas. Please check your connection.');
          }

          // If we have a job ID, load the existing migration data
          if (jobId) {
            console.log('Loading existing migration with jobId:', jobId, 'stepParam:', stepParam);
            await loadExistingMigration(jobId, stepParam);
          } else {
            console.log('No jobId found in URL');
          }
        } else {
          setConnectionStatus('failed');
          setError('Failed to connect to Salesforce');
        }
      } catch (err) {
        setConnectionStatus('failed');
        setError(err instanceof Error ? err.message : 'Failed to connect to Salesforce');
        setIsLoadingObjects(false);
      } finally {
        setIsPageLoading(false); // End page loading
      }
    };

    initializeSalesforce();
  }, []);

  // Filter objects based on search query and filter type (search across ALL objects)
  const filteredObjects = salesforceObjects.filter(obj => {
    // Apply object type filter first
    if (objectFilter === 'custom' && !obj.custom) {
      return false;
    }
    if (objectFilter === 'standard' && obj.custom) {
      return false;
    }

    // Apply text search filter
    if (searchQuery === '') {
      return true;
    }

    return (
      obj.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      obj.label.toLowerCase().includes(searchQuery.toLowerCase())
    );
  });

  // Calculate pagination for display (only paginate the filtered results)
  const totalPages = Math.ceil(filteredObjects.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedObjects = filteredObjects.slice(startIndex, endIndex);

  // Reset to page 1 when search/filter changes
  const resetPagination = () => {
    setCurrentPage(1);
  };

  // Step 1: Handle object selection
  const handleObjectSelect = async (objectName: string) => {
    setSelectedObject(objectName);
    setShowFieldModal(true); // Open field selection modal
    await loadObjectFields(objectName);
  };

  // Load fields for selected object
  const loadObjectFields = async (objectName: string) => {
    setIsLoadingFields(true);
    setError('');

    try {
      const result = await salesforceOAuthApi.getObjectFields(objectName);
      setAvailableFields(result.fields);
      console.log('Loaded available fields:', result.fields.length);

      // Don't preselect any fields - let user choose explicitly
      setSelectedFields([]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load object fields');
    } finally {
      setIsLoadingFields(false);
    }
  };

  // Field selection handlers
  const filteredFields = availableFields.filter(field => {
    // Apply category filter
    if (fieldFilter === 'required' && !field.required) return false;
    if (fieldFilter === 'custom' && !field.custom) return false;

    // Apply search filter
    if (fieldSearchQuery) {
      const query = fieldSearchQuery.toLowerCase();
      return field.name.toLowerCase().includes(query) || field.label.toLowerCase().includes(query);
    }

    return true;
  });

  const handleFieldToggle = (fieldName: string, checked: boolean) => {
    if (checked) {
      setSelectedFields([...selectedFields, fieldName]);
    } else {
      setSelectedFields(selectedFields.filter(f => f !== fieldName));
    }
  };

  const handleExtractSelectedFields = async () => {
    if (selectedFields.length === 0) return;

    setIsLoading(true);
    setError('');

    try {
      // Extract data and create migration job
      const result = await salesforceOAuthApi.extractSelectedData(
        selectedObject,
        selectedFields,
        10,
        true // Create migration job
      );

      setExtractedData([...result.records]); // Store original extracted data
      setOriginalData([...result.records]);
      setEditedData([...result.records]);
      setTotalRecords(result.records.length);
      setTablePage(1); // Reset to first page
      setShowFieldModal(false); // Close modal

      // Store migration job ID if created
      if (result.migration_job_id) {
        setMigrationJobId(result.migration_job_id);
      }

      setCurrentStep('transform'); // Go to transform step
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to extract data');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle modal close
  const handleCloseFieldModal = () => {
    setShowFieldModal(false);
    setSelectedObject(''); // Reset selected object
    setAvailableFields([]);
    setSelectedFields([]);
    setFieldSearchQuery('');
    setFieldFilter('all');
  };

  // Step 3: Fetch data from Salesforce (legacy)
  const handleFetchData = async () => {
    if (!selectedObject) return;

    setIsLoading(true);
    setError('');

    try {
      // Fetch real data from Salesforce
      const result = await salesforceOAuthApi.getData(selectedObject, 100); // Fetch more records for pagination demo

      setExtractedData([...result.records]); // Store original extracted data
      setOriginalData([...result.records]); // Store original data for comparison
      setEditedData([...result.records]);
      setTotalRecords(result.records.length);
      setTablePage(1); // Reset to first page
      setCurrentStep('transform');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch data from Salesforce');
    } finally {
      setIsLoading(false);
    }
  };



  // Function to load staged data records for editing
  const loadStagedDataForEditing = async (jobId: string, objectName?: string) => {
    try {
      const objectToUse = objectName || selectedObject;
      console.log('Loading staged data for editing:', { jobId, objectToUse });

      if (!objectToUse) {
        console.error('No object name available for loading staged data');
        setError('No object name available for loading staged data');
        return;
      }

      console.log('About to call stagedDataApi.getStagedData with:', {
        objectName: objectToUse,
        params: {
          migration_job_id: jobId,
          page: 1,
          limit: 1000
        }
      });

      const stagedDataResponse = await stagedDataApi.getStagedData(objectToUse, {
        migration_job_id: jobId,
        page: 1,
        limit: 1000 // Load all records for now, can add pagination later
      });

      console.log('Staged data response:', stagedDataResponse);

      if (stagedDataResponse.items && stagedDataResponse.items.length > 0) {
        setStagedDataRecords(stagedDataResponse.items);

        // Extract current transformed_data (includes user edits) for editing
        const currentRecords = stagedDataResponse.items.map((record: any) => record.transformed_data);
        // Extract original_data for comparison (fallback to transformed_data if no original_data)
        const originalRecords = stagedDataResponse.items.map((record: any) =>
          record.original_data || record.transformed_data
        );

        setEditedData(currentRecords);
        setOriginalData(originalRecords);
        setTotalRecords(stagedDataResponse.total);

        // Rebuild modified cells tracking based on user_modifications from database
        const newModifiedCells = new Set<string>();
        stagedDataResponse.items.forEach((record: any, recordIndex: number) => {
          // Check if this record has user modifications
          if (record.user_modified && record.user_modifications) {
            // Add all fields that have been modified by users
            Object.keys(record.user_modifications).forEach(field => {
              newModifiedCells.add(`${recordIndex}-${field}`);
            });
          }
        });
        setModifiedCells(newModifiedCells);

        console.log(`Found ${newModifiedCells.size} previously modified cells:`, Array.from(newModifiedCells));

        console.log(`Loaded ${stagedDataResponse.items.length} staged records for editing`);
        console.log(`Found ${newModifiedCells.size} previously modified cells`);
      } else {
        console.log('No staged data items found');
        setEditedData([]);
        setOriginalData([]);
        setTotalRecords(0);
      }
    } catch (error) {
      console.error('Failed to load staged data for editing:', error);
      setError('Failed to load staged data for editing');
    }
  };

  // Handle cell editing for TanStack table with database save
  const handleTableCellEdit = async (rowIndex: number, field: string, value: any) => {
    const cellKey = `${rowIndex}-${field}`;

    try {
      // Update local state immediately for UI responsiveness
      const updated = [...editedData];
      updated[rowIndex] = { ...updated[rowIndex], [field]: value };
      setEditedData(updated);

      // Track as modified locally
      const newModifiedCells = new Set(modifiedCells);
      newModifiedCells.add(cellKey);
      setModifiedCells(newModifiedCells);

      // Save to database if we have staged data records with IDs
      if (stagedDataRecords.length > 0 && stagedDataRecords[rowIndex]) {
        const stagedRecord = stagedDataRecords[rowIndex];

        await stagedDataApi.updateStagedRecord(selectedObject, stagedRecord.id, {
          field_updates: { [field]: value },
          user_id: '<EMAIL>' // TODO: Get from auth context
        });

        console.log(`Saved cell edit to database: ${field} = ${value}`);
      } else {
        console.log('No staged record ID available, changes saved locally only');
      }
    } catch (error) {
      console.error('Failed to save cell edit:', error);
      // Optionally revert the UI change on error
      // For now, we'll keep the local change and show an error
      setError('Failed to save changes to database');
    }
  };



  // Check if a cell has been modified
  const isCellModified = (recordIndex: number, field: string) => {
    if (originalData.length === 0 || !originalData[recordIndex]) return false;
    return originalData[recordIndex][field] !== editedData[recordIndex][field];
  };

  // Count total modified cells
  const getModifiedCellsCount = () => {
    let count = 0;
    editedData.forEach((record, recordIndex) => {
      Object.keys(record).forEach(field => {
        if (isCellModified(recordIndex, field)) {
          count++;
        }
      });
    });
    return count;
  };



  // Table pagination handlers
  const handleTablePageChange = (newPage: number) => {
    setTablePage(newPage);
  };

  const handleTablePageSizeChange = (newSize: number) => {
    setTablePageSize(newSize);
    setTablePage(1); // Reset to first page
  };

  // Step 4: Load to target system
  const handleLoadData = async () => {
    setIsLoading(true);
    setCurrentStep('load');
    setError('');

    try {
      // Use bulk load API to send transformed data to OPS
      const result = await loadApi.bulkLoadData({
        target_system: 'ops',
        object_type: 'destination',
        records: transformedData.length > 0 ? transformedData : editedData,
        operation: 'create',
        config: {
          // OPS configuration will be loaded from backend settings
        }
      });

      setLoadResult(result);

      if (result.success) {
        setCurrentStep('complete');
      } else {
        setError(`Load failed: ${result.errors.join(', ')}`);
        setCurrentStep('edit'); // Go back to edit step
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data to target system');
      setCurrentStep('edit'); // Go back to edit step
    } finally {
      setIsLoading(false);
    }
  };

  const resetFlow = () => {
    setCurrentStep('select');
    setSelectedObject('');
    setEditedData([]);
    setOriginalData([]);
    setExtractedData([]);
    setTablePage(1);
    setTotalRecords(0);
    setModifiedCells(new Set()); // Reset modified cells tracking
    setStagedDataRecords([]); // Reset staged data records
    // Reset field selection modal state
    setShowFieldModal(false);
    setAvailableFields([]);
    setSelectedFields([]);
    setIsLoadingFields(false);
    setFieldSearchQuery('');
    setFieldFilter('all');
    // Reset transformation state
    setSelectedTargetSchema(null);
    setFieldMappings([]);
    setRemoveUnmappedFields(true);
    setTransformedData([]);
    setLoadResult(null);
    // Reset migration job
    setMigrationJobId(null);
  };

  // Show loading screen while initializing
  if (isPageLoading) {
    return (
      <AppLayout>
        <div className='flex items-center justify-center min-h-screen'>
          <div className='text-center'>
            <Loader2 className='h-8 w-8 animate-spin mx-auto mb-4 text-blue-600' />
            <h2 className='text-lg font-semibold text-slate-900 mb-2'>Loading Migration</h2>
            <p className='text-slate-600'>Initializing your migration workflow...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className='p-8'>
        {/* Page Header */}
        <div className='mb-8'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center space-x-4'>
              <div>
                <h1 className='text-3xl font-bold text-slate-900'>Migration</h1>
                <p className='mt-1 text-slate-600'>
                  Extract, transform, and sync your Salesforce data
                </p>
              </div>
            </div>

            {/* Progress Steps */}
            <div className='flex items-center space-x-2 overflow-x-auto'>
              {/* Step 1: Extract */}
              <div
                className={`flex items-center space-x-2 ${currentStep === 'select' || currentStep === 'extract' ? 'text-blue-600' : ['transform', 'edit', 'load', 'complete'].includes(currentStep) ? 'text-green-600' : 'text-slate-400'}`}
              >
                <div
                  className={`flex h-8 w-8 items-center justify-center rounded-full ${currentStep === 'select' || currentStep === 'extract' ? 'border-2 border-blue-600 bg-blue-100' : ['transform', 'edit', 'load', 'complete'].includes(currentStep) ? 'border-2 border-green-600 bg-green-100' : 'border-2 border-slate-300 bg-slate-100'}`}
                >
                  <Database className='h-4 w-4' />
                </div>
                <span className='font-medium text-sm'>Extract</span>
              </div>

              <ArrowRight className='h-4 w-4 text-slate-400' />

              {/* Step 2: Transform */}
              <div
                className={`flex items-center space-x-2 ${currentStep === 'transform' ? 'text-blue-600' : ['edit', 'load', 'complete'].includes(currentStep) ? 'text-green-600' : 'text-slate-400'}`}
              >
                <div
                  className={`flex h-8 w-8 items-center justify-center rounded-full ${currentStep === 'transform' ? 'border-2 border-blue-600 bg-blue-100' : ['edit', 'load', 'complete'].includes(currentStep) ? 'border-2 border-green-600 bg-green-100' : 'border-2 border-slate-300 bg-slate-100'}`}
                >
                  <Settings className='h-4 w-4' />
                </div>
                <span className='font-medium text-sm'>Transform</span>
              </div>

              <ArrowRight className='h-4 w-4 text-slate-400' />

              {/* Step 3: Edit */}
              <div
                className={`flex items-center space-x-2 ${currentStep === 'edit' ? 'text-blue-600' : ['load', 'complete'].includes(currentStep) ? 'text-green-600' : 'text-slate-400'}`}
              >
                <div
                  className={`flex h-8 w-8 items-center justify-center rounded-full ${currentStep === 'edit' ? 'border-2 border-blue-600 bg-blue-100' : ['load', 'complete'].includes(currentStep) ? 'border-2 border-green-600 bg-green-100' : 'border-2 border-slate-300 bg-slate-100'}`}
                >
                  <Eye className='h-4 w-4' />
                </div>
                <span className='font-medium text-sm'>Edit</span>
              </div>

              <ArrowRight className='h-4 w-4 text-slate-400' />

              {/* Step 4: Load */}
              <div
                className={`flex items-center space-x-2 ${currentStep === 'load' ? 'text-blue-600' : currentStep === 'complete' ? 'text-green-600' : 'text-slate-400'}`}
              >
                <div
                  className={`flex h-8 w-8 items-center justify-center rounded-full ${currentStep === 'load' ? 'border-2 border-blue-600 bg-blue-100' : currentStep === 'complete' ? 'border-2 border-green-600 bg-green-100' : 'border-2 border-slate-300 bg-slate-100'}`}
                >
                  <Upload className='h-4 w-4' />
                </div>
                <span className='font-medium text-sm'>Load</span>
              </div>
            </div>
          </div>
        </div>
        {/* Step 1: Object Selection */}
        {currentStep === 'select' && (
          <Card className='border-0 bg-white/80 shadow-xl backdrop-blur-sm'>
            <CardHeader>
              <CardTitle className='flex items-center space-x-3 text-xl'>
                <span className='text-slate-900'>Select Salesforce Object</span>
              </CardTitle>
              <CardDescription className='text-base text-slate-600'>
                Choose the Salesforce object you want to migrate
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              {/* Connection Status */}
              {connectionStatus === 'checking' && (
                <Alert>
                  <Loader2 className='h-4 w-4 animate-spin' />
                  <AlertDescription>Connecting to Salesforce...</AlertDescription>
                </Alert>
              )}

              {connectionStatus === 'failed' && (
                <Alert variant='destructive'>
                  <AlertTriangle className='h-4 w-4' />
                  <AlertDescription>{error || 'Failed to connect to Salesforce'}</AlertDescription>
                </Alert>
              )}

              {connectionStatus === 'connected' && (
                <>
                  <Alert className='border-green-200 bg-green-50'>
                    <CheckCircle className='h-4 w-4 text-green-600' />
                    <AlertDescription className='text-green-800'>
                      Successfully connected to Salesforce
                    </AlertDescription>
                  </Alert>

                  {/* Loading Objects */}
                  {isLoadingObjects && (
                    <>
                      <Alert>
                        <Loader2 className='h-4 w-4 animate-spin' />
                        <AlertDescription>Loading Salesforce objects...</AlertDescription>
                      </Alert>

                      {/* Loading Skeleton */}
                      <div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3'>
                        {[1, 2, 3, 4, 5, 6].map(i => (
                          <div
                            key={i}
                            className='animate-pulse rounded-lg border-2 border-slate-200 p-4'
                          >
                            <div className='mb-2 flex items-center justify-between'>
                              <div className='h-4 w-24 rounded bg-slate-200'></div>
                              <div className='h-6 w-16 rounded bg-slate-200'></div>
                            </div>
                            <div className='mb-2 h-3 w-32 rounded bg-slate-200'></div>
                            <div className='flex space-x-2'>
                              <div className='h-3 w-12 rounded bg-slate-200'></div>
                              <div className='h-3 w-12 rounded bg-slate-200'></div>
                              <div className='h-3 w-12 rounded bg-slate-200'></div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </>
                  )}

                  {/* Search Input - Only show when objects are loaded */}
                  {!isLoadingObjects && salesforceObjects.length > 0 && (
                    <>
                      <div className='space-y-4'>
                        <div className='space-y-2'>
                          <Label htmlFor='object-search'>Search Objects</Label>
                          <Input
                            id='object-search'
                            type='text'
                            placeholder='Search by object name or label...'
                            value={searchQuery}
                            onChange={e => {
                              setSearchQuery(e.target.value);
                              resetPagination();
                            }}
                            className='w-full'
                          />
                        </div>

                        {/* Quick Filters */}
                        <div className='space-y-2'>
                          <Label>Quick Filters</Label>
                          <div className='flex flex-wrap gap-2'>
                            <Button
                              variant={objectFilter === 'all' ? 'default' : 'outline'}
                              size='sm'
                              onClick={() => {
                                setObjectFilter('all');
                                resetPagination();
                              }}
                              className='cursor-pointer'
                            >
                              All Objects ({salesforceObjects.length})
                            </Button>
                            <Button
                              variant={objectFilter === 'custom' ? 'default' : 'outline'}
                              size='sm'
                              onClick={() => {
                                setObjectFilter('custom');
                                resetPagination();
                              }}
                              className='cursor-pointer'
                            >
                              Custom Objects ({salesforceObjects.filter(obj => obj.custom).length})
                            </Button>
                            <Button
                              variant={objectFilter === 'standard' ? 'default' : 'outline'}
                              size='sm'
                              onClick={() => {
                                setObjectFilter('standard');
                                resetPagination();
                              }}
                              className='cursor-pointer'
                            >
                              Standard Objects (
                              {salesforceObjects.filter(obj => !obj.custom).length})
                            </Button>
                          </div>
                        </div>

                        <p className='text-sm text-slate-500'>
                          Showing {startIndex + 1}-{Math.min(endIndex, filteredObjects.length)} of{' '}
                          {filteredObjects.length} objects
                          {filteredObjects.length !== salesforceObjects.length &&
                            ` (filtered from ${salesforceObjects.length} total)`}
                        </p>
                      </div>

                      {filteredObjects.length > 0 ? (
                        <>
                          <div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3'>
                            {paginatedObjects.map(obj => (
                              <div
                                key={obj.name}
                                className={`cursor-pointer rounded-lg border-2 p-4 transition-all ${
                                  selectedObject === obj.name
                                    ? 'border-blue-500 bg-blue-50'
                                    : 'border-slate-200 bg-white hover:border-slate-300'
                                }`}
                                onClick={() => handleObjectSelect(obj.name)}
                              >
                                <div className='mb-2 flex items-center justify-between'>
                                  <h3 className='font-semibold text-slate-900'>{obj.label}</h3>
                                  <div className='flex space-x-1'>
                                    {obj.custom && (
                                      <Badge
                                        variant='outline'
                                        className='border-purple-300 text-purple-600'
                                      >
                                        Custom
                                      </Badge>
                                    )}
                                    <Badge
                                      variant='secondary'
                                      className='border-green-300 text-green-600'
                                    >
                                      Queryable
                                    </Badge>
                                  </div>
                                </div>
                                <p className='mb-1 text-sm text-slate-600'>API Name: {obj.name}</p>
                              </div>
                            ))}
                          </div>

                          {/* Pagination Controls */}
                          {totalPages > 1 && (
                            <div className='flex items-center justify-between'>
                              <div className='flex items-center space-x-2'>
                                <Button
                                  variant='outline'
                                  size='sm'
                                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                  disabled={currentPage === 1}
                                >
                                  Previous
                                </Button>
                                <span className='text-sm text-slate-600'>
                                  Page {currentPage} of {totalPages}
                                </span>
                                <Button
                                  variant='outline'
                                  size='sm'
                                  onClick={() =>
                                    setCurrentPage(Math.min(totalPages, currentPage + 1))
                                  }
                                  disabled={currentPage === totalPages}
                                >
                                  Next
                                </Button>
                              </div>
                              <div className='text-sm text-slate-500'>
                                {itemsPerPage} objects per page
                              </div>
                            </div>
                          )}
                        </>
                      ) : (
                        <div className='py-8 text-center'>
                          <Database className='mx-auto mb-4 h-12 w-12 text-slate-400' />
                          <h3 className='mb-2 text-lg font-medium text-slate-900'>
                            No objects found
                          </h3>
                          <p className='mb-4 text-slate-600'>
                            No Salesforce objects match your search &quot;{searchQuery}&quot;
                          </p>
                          <Button
                            variant='outline'
                            onClick={() => {
                              setSearchQuery('');
                              setObjectFilter('all');
                            }}
                            className='text-slate-600'
                          >
                            Clear All Filters
                          </Button>
                        </div>
                      )}
                    </>
                  )}
                </>
              )}

              {error && (
                <Alert variant='destructive'>
                  <AlertTriangle className='h-4 w-4' />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {selectedObject && (
                <div className='flex items-center justify-between rounded-lg border border-blue-200 bg-blue-50 p-4'>
                  <div>
                    <p className='font-medium text-blue-900'>Selected: {selectedObject}</p>
                    <p className='text-sm text-blue-700'>Ready to fetch data from Salesforce</p>
                  </div>
                  <Button
                    onClick={handleFetchData}
                    disabled={isLoading}
                    className='bg-blue-600 hover:bg-blue-700'
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                        Fetching...
                      </>
                    ) : (
                      <>
                        <Download className='mr-2 h-4 w-4' />
                        Fetch Data
                      </>
                    )}
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Field Selection Modal */}
        <Dialog open={showFieldModal} onOpenChange={setShowFieldModal}>
          <DialogContent className='flex max-h-[80vh] max-w-4xl flex-col overflow-hidden'>
            <DialogHeader>
              <DialogTitle className='flex items-center space-x-3'>
                Select Fields from {selectedObject}
              </DialogTitle>
              <DialogDescription>
                Choose which fields to extract from the {selectedObject} object
              </DialogDescription>
            </DialogHeader>

            <div className='flex flex-1 flex-col space-y-4 overflow-hidden'>
              {isLoadingFields ? (
                <div className='flex flex-1 items-center justify-center'>
                  <div className='text-center'>
                    <Loader2 className='mx-auto mb-4 h-8 w-8 animate-spin' />
                    <p>Loading object fields...</p>
                  </div>
                </div>
              ) : (
                <>
                  {/* Field Search */}
                  <div className='space-y-2'>
                    <Label htmlFor='field-search'>Search Fields</Label>
                    <Input
                      id='field-search'
                      placeholder='Search by field name or label...'
                      value={fieldSearchQuery}
                      onChange={e => setFieldSearchQuery(e.target.value)}
                    />
                  </div>

                  {/* Field Categories */}
                  <div className='flex space-x-2'>
                    <Button
                      variant={fieldFilter === 'all' ? 'default' : 'outline'}
                      size='sm'
                      onClick={() => setFieldFilter('all')}
                    >
                      All Fields ({availableFields.length})
                    </Button>
                    <Button
                      variant={fieldFilter === 'required' ? 'default' : 'outline'}
                      size='sm'
                      onClick={() => setFieldFilter('required')}
                    >
                      Required ({availableFields.filter(f => f.required).length})
                    </Button>
                    <Button
                      variant={fieldFilter === 'custom' ? 'default' : 'outline'}
                      size='sm'
                      onClick={() => setFieldFilter('custom')}
                    >
                      Custom ({availableFields.filter(f => f.custom).length})
                    </Button>
                  </div>

                  {/* Selected Fields Summary */}
                  <div className={`rounded-lg border p-3 ${selectedFields.length > 0 ? 'border-blue-200 bg-blue-50' : 'border-amber-200 bg-amber-50'}`}>
                    <p className={`text-sm font-medium ${selectedFields.length > 0 ? 'text-blue-900' : 'text-amber-900'}`}>
                      {selectedFields.length} fields selected
                    </p>
                    {selectedFields.length > 0 ? (
                      <p className='truncate text-xs text-blue-700'>{selectedFields.join(', ')}</p>
                    ) : (
                      <p className='text-xs text-amber-700'>Please select at least one field to extract data</p>
                    )}
                  </div>

                  {/* Field List */}
                  <div className='flex-1 overflow-y-auto rounded-lg border border-slate-200'>
                    {filteredFields.map(field => (
                      <div
                        key={field.name}
                        className='flex items-center space-x-3 border-b border-slate-100 p-3 last:border-b-0 hover:bg-slate-50'
                      >
                        <input
                          type='checkbox'
                          checked={selectedFields.includes(field.name)}
                          onChange={e => handleFieldToggle(field.name, e.target.checked)}
                          className='rounded border-slate-300'
                        />
                        <div className='min-w-0 flex-1'>
                          <div className='flex items-center space-x-2'>
                            <span className='truncate font-medium'>{field.label}</span>
                            <code className='shrink-0 rounded bg-slate-100 px-1 py-0.5 text-xs'>
                              {field.name}
                            </code>
                            {field.required && (
                              <Badge variant='destructive' className='shrink-0 text-xs'>
                                Required
                              </Badge>
                            )}
                            {field.custom && (
                              <Badge variant='outline' className='shrink-0 text-xs'>
                                Custom
                              </Badge>
                            )}
                          </div>
                          <p className='truncate text-sm text-slate-500'>Type: {field.type}</p>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Modal Actions */}
                  <div className='flex items-center justify-between rounded-lg border bg-slate-50 p-4'>
                    <div>
                      <p className='text-sm font-medium text-slate-900'>Ready to Extract</p>
                      <p className='text-xs text-slate-600'>
                        {selectedFields.length} fields selected from {selectedObject}
                      </p>
                    </div>
                    <div className='flex space-x-2'>
                      <Button
                        variant='outline'
                        onClick={handleCloseFieldModal}
                        disabled={isLoading}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={handleExtractSelectedFields}
                        disabled={selectedFields.length === 0 || isLoading}
                        className='bg-green-600 hover:bg-green-700'
                      >
                        {isLoading ? (
                          <>
                            <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                            Extracting...
                          </>
                        ) : (
                          <>
                            <Download className='mr-2 h-4 w-4' />
                            Extract Fields
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </div>
          </DialogContent>
        </Dialog>

        {/* Step 2: Transform Data */}
        {currentStep === 'transform' && (
          <Card className='border-0 bg-white/80 shadow-xl backdrop-blur-sm'>
            <CardHeader className=''>
              <div className='flex items-center justify-between'>
                <CardTitle className='flex items-center space-x-3 text-xl'>
                  <div className='flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-green-500 to-green-600'>
                    <Settings className='h-5 w-5 text-white' />
                  </div>
                  <span className='text-slate-900'>Configure Field Mappings</span>
                </CardTitle>
                <Button
                  onClick={() => setCurrentStep('select')}
                  variant='outline'
                  className='border-slate-300'
                >
                  <ArrowRight className='mr-2 h-4 w-4 rotate-180' />
                  Back to Selection
                </Button>
              </div>
              <CardDescription className='text-base text-slate-600'>
                Map Salesforce fields to your target schema and configure transformations
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              {/* Target Schema Selection */}
              <div className='space-y-4'>
                <div className='flex flex-col space-y-2'>
                  <Label htmlFor='target-schema' className='pb-1'>Select Target Schema</Label>
                  <Select
                    value={selectedTargetSchema || ''}
                    onValueChange={setSelectedTargetSchema}
                  >
                    <SelectTrigger className='cursor-pointer'>
                      <SelectValue placeholder='Choose a target schema for transformation' />
                    </SelectTrigger>
                    <SelectContent className='cursor-pointer'>
                      {targetSchemas.map(schema => (
                        <SelectItem key={schema.id} value={schema.id}>
                          <div className='flex items-center space-x-2 cursor-pointer'>
                            <span>{schema.label}</span>
                            <Badge variant='secondary' className={
                              schema.target_system === 'ops'
                                ? 'bg-blue-100 text-blue-800'
                                : 'bg-green-100 text-green-800'
                            }>
                              {schema.target_system === 'ops' ? 'Ops' : 'CRM'}
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {selectedTargetSchema && (
                  <div className='rounded-lg border border-green-200 bg-green-50 p-4'>
                    <div className='flex items-center space-x-2'>
                      <CheckCircle className='h-4 w-4 text-green-600' />
                      <span className='font-medium text-green-900'>Target Schema Selected</span>
                    </div>
                    <p className='text-sm text-green-700 mt-1'>
                      {targetSchemas.find(s => s.id === selectedTargetSchema)?.label} - Ready for field mapping
                    </p>
                  </div>
                )}
              </div>

              {/* Field Mapping Section */}
              {selectedTargetSchema && availableFields.length > 0 ? (
                <div className='space-y-4'>
                  <h3 className='text-lg font-semibold text-slate-900'>Field Mappings</h3>
                  <div className='rounded-lg border border-slate-200 bg-white p-6'>
                    <p className='text-sm text-slate-600 mb-6'>
                      Map your Salesforce fields to the target schema fields:
                    </p>

                    {/* Interactive field mapping */}
                    <div className='space-y-4'>
                      {targetSchemas.find(s => s.id === selectedTargetSchema)?.fields?.map((targetField: TargetField) => (
                        <div key={targetField.name} className='border border-slate-200 rounded-lg p-4 bg-slate-50'>
                          <div className='grid grid-cols-3 gap-4 items-center'>
                            {/* Source Field Selection */}
                            <div className='flex items-center space-x-2'>
                              <Select
                                value={fieldMappings.find(m => m.targetField === targetField.name)?.sourceField || '__NO_MAPPING__'}
                                onValueChange={(value) => {
                                  const newMappings = fieldMappings.filter(m => m.targetField !== targetField.name);
                                  if (value && value !== '__NO_MAPPING__') {
                                    newMappings.push({
                                      sourceField: value,
                                      targetField: targetField.name,
                                      transformation: {
                                        function: 'direct',
                                        parameters: {}
                                      },
                                      required: targetField.required || false
                                    });
                                  }
                                  setFieldMappings(newMappings);
                                }}
                              >
                                <SelectTrigger className='w-full cursor-pointer'>
                                  <SelectValue placeholder='Select Salesforce field' />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value='__NO_MAPPING__'>No mapping</SelectItem>
                                  {selectedFields.map(fieldName => {
                                    const fieldInfo = availableFields.find(f => f.name === fieldName);
                                    return (
                                      <SelectItem key={fieldName} value={fieldName}>
                                        <div className='flex items-center space-x-2'>
                                          <span>{fieldName}</span>
                                          {fieldInfo && (
                                            <Badge variant='outline' className='text-xs'>
                                              {fieldInfo.type}
                                            </Badge>
                                          )}
                                        </div>
                                      </SelectItem>
                                    );
                                  })}
                                </SelectContent>
                              </Select>
                              {/* Remove button - only show if field is mapped */}
                              {fieldMappings.find(m => m.targetField === targetField.name) && (
                                <Button
                                  variant='ghost'
                                  size='sm'
                                  onClick={() => {
                                    const newMappings = fieldMappings.filter(m => m.targetField !== targetField.name);
                                    setFieldMappings(newMappings);
                                  }}
                                  className='h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50'
                                >
                                  <X className='h-4 w-4' />
                                </Button>
                              )}
                            </div>

                            {/* Mapping Arrow */}
                            <div className='flex justify-center'>
                              <ArrowRight className='h-4 w-4 text-slate-400' />
                            </div>

                            {/* Target Field */}
                            <div>
                              <div className='font-medium text-slate-900'>{targetField.label}</div>
                              <div className='text-sm text-slate-500'>
                                {targetField.type}
                                {targetField.required && <span className='text-red-500 ml-1'>*</span>}
                              </div>
                            </div>
                          </div>


                        </div>
                      )) || <div className='text-sm text-slate-500'>No target fields defined</div>}
                    </div>

                    {/* Mapping Summary */}
                    <div className='mt-6 pt-4 border-t border-slate-200'>
                      <div className='flex items-center justify-between text-sm'>
                        <span className='text-slate-600'>
                          {fieldMappings.length} of {targetSchemas.find(s => s.id === selectedTargetSchema)?.fields?.length || 0} fields mapped
                        </span>
                        <div className='flex items-center space-x-4'>
                          <label className='flex items-center space-x-2'>
                            <input
                              type='checkbox'
                              checked={removeUnmappedFields}
                              onChange={(e) => setRemoveUnmappedFields(e.target.checked)}
                              className='rounded border-slate-300'
                            />
                            <span className='text-slate-600'>Remove unmapped fields</span>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className='rounded-lg border border-orange-200 bg-orange-50 p-4'>
                  <h3 className='font-medium text-orange-900 mb-2'>Configuration Required</h3>
                  <div className='text-sm text-orange-700 space-y-1'>
                    {!selectedTargetSchema && <p>• Please select a target schema</p>}
                    {availableFields.length === 0 && <p>• No Salesforce fields available (go back to select fields)</p>}
                    {targetSchemas.length === 0 && <p>• No target schemas available (create one in Target Schemas page)</p>}
                  </div>
                </div>
              )}

              <div className='flex justify-end'>
                <Button
                  onClick={async () => {
                    // Apply transformations and create staged data
                    setIsLoading(true);
                    setError('');

                    try {
                      const transformed = transformationService.transformData(extractedData, {
                        mappings: fieldMappings,
                        removeUnmappedFields
                      });

                      // Generate transformation summary
                      const summary = transformationService.getTransformationSummary(
                        extractedData,
                        transformed,
                        { mappings: fieldMappings, removeUnmappedFields }
                      );

                      setTransformedData(transformed);
                      setTransformationSummary(summary);

                      // Create staged data if we have a migration job
                      if (migrationJobId && transformed.length > 0) {
                        const stagedResult = await salesforceOAuthApi.createStagedData(
                          migrationJobId,
                          selectedObject,
                          transformed,
                          fieldMappings,
                          {
                            mappings: fieldMappings,
                            removeUnmappedFields,
                            targetSchema: selectedTargetSchema
                          }
                        );

                        console.log(`Created ${stagedResult.staged_records_created} staged records`);
                        console.log('Staged result:', stagedResult);

                        // Load the actual staged data records with IDs for editing
                        try {
                          await loadStagedDataForEditing(migrationJobId, selectedObject);
                        } catch (loadError) {
                          console.error('Failed to load staged data, using fallback:', loadError);
                          // Fallback: use transformed data without database IDs
                          setEditedData([...transformed]);
                          setOriginalData([...transformed]);
                          setTotalRecords(transformed.length);
                        }
                      } else {
                        // Fallback: use transformed data without database IDs
                        setEditedData([...transformed]); // Copy for editing
                        setOriginalData([...transformed]); // Update baseline for modification detection in edit step
                        setTotalRecords(transformed.length);
                      }

                      setCurrentStep('edit');
                    } catch (err) {
                      setError(err instanceof Error ? err.message : 'Failed to apply transformations');
                    } finally {
                      setIsLoading(false);
                    }
                  }}
                  disabled={!selectedTargetSchema || fieldMappings.length === 0}
                  className='bg-green-600 hover:bg-green-700'
                >
                  Apply Transformations
                  <ArrowRight className='ml-2 h-4 w-4' />
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 3: Edit Data */}
        {currentStep === 'edit' && (
          <Card className='border-0 bg-white/80 shadow-xl backdrop-blur-sm'>
            <CardHeader>
              <div className='flex items-center justify-between'>
                <CardTitle className='flex items-center space-x-3 text-xl'>
                  <div className='flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-purple-500 to-purple-600'>
                    <Eye className='h-5 w-5 text-white' />
                  </div>
                  <span className='text-slate-900'>Edit Transformed Data</span>
                </CardTitle>
                <Button
                  variant='outline'
                  onClick={() => setCurrentStep('transform')}
                  className='text-slate-600'
                >
                  <ArrowRight className='mr-2 h-4 w-4 rotate-180' />
                  Back to Transform
                </Button>
              </div>
              <CardDescription className='text-base text-slate-600'>
                Review and edit the transformed data before loading to target system
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              {/* Transformation Summary */}
              {transformationSummary && (
                <div className='rounded-lg border border-green-200 bg-green-50 p-4'>
                  <h3 className='font-medium text-green-900 mb-2'>Transformation Summary</h3>
                  <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-sm'>
                    <div>
                      <span className='text-green-700'>Records:</span>
                      <span className='ml-1 font-medium'>{transformationSummary.transformedRecords}</span>
                    </div>
                    <div>
                      <span className='text-green-700'>Fields Mapped:</span>
                      <span className='ml-1 font-medium'>{transformationSummary.fieldsMapped}</span>
                    </div>
                    <div>
                      <span className='text-green-700'>Fields Removed:</span>
                      <span className='ml-1 font-medium'>{transformationSummary.fieldsRemoved}</span>
                    </div>
                    <div>
                      <span className='text-green-700'>Unmapped:</span>
                      <span className='ml-1 font-medium'>{transformationSummary.unmappedFieldsAnalysis.unmappedFields.length}</span>
                    </div>
                  </div>
                  {transformationSummary.unmappedFieldsAnalysis.unmappedFields.length > 0 && (
                    <div className='mt-3 pt-3 border-t border-green-200'>
                      <p className='text-sm text-green-700 mb-2'>
                        <strong>Unmapped fields:</strong> {transformationSummary.unmappedFieldsAnalysis.unmappedFields.join(', ')}
                      </p>
                    </div>
                  )}
                </div>
              )}

              <div className='flex items-center space-x-4'>
                <Badge variant='outline' className='border-green-300 text-green-700'>
                  <CheckCircle className='mr-1 h-3 w-3' />
                  {editedData.length} records fetched
                </Badge>
                <Badge variant='outline'>Object: {selectedObject}</Badge>
              </div>

              {/* Legend */}
              <div className='mb-4 flex items-center space-x-6 text-sm text-slate-600'>
                <div className='flex items-center space-x-2'>
                  <Edit className='h-4 w-4' />
                  <span>Click any cell to edit</span>
                </div>
                <div className='flex items-center space-x-2'>
                  <Save className='h-3 w-3 text-green-600' />
                  <span>Save (Enter)</span>
                </div>
                <div className='flex items-center space-x-2'>
                  <X className='h-3 w-3 text-red-600' />
                  <span>Cancel (Esc)</span>
                </div>
                <div className='flex items-center space-x-2'>
                  <div className='h-3 w-3 rounded-full bg-blue-500'></div>
                  <span>Modified cells</span>
                </div>
              </div>

              {/* TanStack Data Table */}
              <MigrationDataTable
                data={editedData}
                total={totalRecords}
                page={tablePage}
                limit={tablePageSize}
                onPageChange={handleTablePageChange}
                onLimitChange={handleTablePageSizeChange}
                onCellEdit={handleTableCellEdit}
                modifiedCells={modifiedCells}
                title={`${selectedObject} Records`}
                description={`Review and edit your ${selectedObject} data before syncing`}
                isLoading={isLoading}
              />

              <div className='flex items-center justify-between rounded-lg border border-purple-200 bg-purple-50 p-4'>
                <div>
                  <p className='font-medium text-purple-900'>Data Ready for Sync</p>
                  <p className='text-sm text-purple-700'>
                    {totalRecords} records will be migrated to the target system
                  </p>
                  {getModifiedCellsCount() > 0 && (
                    <p className='mt-1 text-xs text-purple-600'>
                      {getModifiedCellsCount()} cells have been modified
                    </p>
                  )}
                </div>
                <Button
                  onClick={handleLoadData}
                  disabled={isLoading}
                  className='bg-purple-600 hover:bg-purple-700'
                >
                  {isLoading ? (
                    <>
                      <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                      Loading...
                    </>
                  ) : (
                    <>
                      <Upload className='mr-2 h-4 w-4' />
                      Load to Target
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 4: Load Progress */}
        {currentStep === 'load' && (
          <Card className='border-0 bg-white/80 shadow-xl backdrop-blur-sm'>
            <CardHeader className='pb-6'>
              <CardTitle className='flex items-center space-x-3 text-xl'>
                <div className='flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-orange-500 to-orange-600'>
                  <Upload className='h-5 w-5 text-white' />
                </div>
                <span className='text-slate-900'>Loading Data</span>
              </CardTitle>
              <CardDescription className='text-base text-slate-600'>
                Loading transformed data to OPS system...
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='py-12 text-center'>
                <div className='mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-orange-500 to-orange-600'>
                  <Loader2 className='h-8 w-8 animate-spin text-white' />
                </div>
                <h3 className='mb-2 text-lg font-semibold text-slate-900'>Loading in Progress</h3>
                <p className='mb-4 text-slate-600'>
                  Loading {transformedData.length > 0 ? transformedData.length : editedData.length} {selectedObject} records to OPS system...
                </p>
                <div className='mx-auto h-2 w-full max-w-md rounded-full bg-slate-200'>
                  <div
                    className='h-2 animate-pulse rounded-full bg-gradient-to-r from-orange-500 to-orange-600'
                    style={{ width: '75%' }}
                  ></div>
                </div>
                <p className='mt-2 text-sm text-slate-500'>This may take a few moments...</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 4: Complete */}
        {currentStep === 'complete' && (
          <Card className='border-0 bg-white/80 shadow-xl backdrop-blur-sm'>
            <CardHeader className='pb-6'>
              <CardTitle className='flex items-center space-x-3 text-xl'>
                <div className='flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-green-500 to-green-600'>
                  <CheckCircle className='h-5 w-5 text-white' />
                </div>
                <span className='text-slate-900'>Migration Complete</span>
              </CardTitle>
              <CardDescription className='text-base text-slate-600'>
                Your data has been successfully migrated!
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='py-12 text-center'>
                <div className='mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-green-500 to-green-600'>
                  <CheckCircle className='h-8 w-8 text-white' />
                </div>
                <h3 className='mb-2 text-lg font-semibold text-slate-900'>Success!</h3>
                <p className='mb-6 text-slate-600'>
                  Successfully migrated {loadResult?.successful_records || totalRecords} {selectedObject} records to OPS system.
                </p>

                {loadResult && (
                  <div className='mx-auto mb-6 max-w-md space-y-4'>
                    <Alert className='border-green-200 bg-green-50'>
                      <CheckCircle className='h-4 w-4 text-green-600' />
                      <AlertDescription className='text-green-800'>
                        {loadResult.message}
                      </AlertDescription>
                    </Alert>

                    <div className='rounded-lg border border-slate-200 bg-slate-50 p-4'>
                      <div className='grid grid-cols-2 gap-4 text-sm'>
                        <div>
                          <span className='font-medium text-slate-700'>Total Records:</span>
                          <span className='ml-2 text-slate-900'>{loadResult.total_records}</span>
                        </div>
                        <div>
                          <span className='font-medium text-green-700'>Successful:</span>
                          <span className='ml-2 text-green-900'>{loadResult.successful_records}</span>
                        </div>
                        {loadResult.failed_records > 0 && (
                          <div className='col-span-2'>
                            <span className='font-medium text-red-700'>Failed:</span>
                            <span className='ml-2 text-red-900'>{loadResult.failed_records}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                <div className='flex items-center justify-center space-x-4'>
                  <Button onClick={resetFlow} variant='outline' className='border-slate-300'>
                    <RefreshCw className='mr-2 h-4 w-4' />
                    Start New Migration
                  </Button>
                  <Button
                    onClick={() => (window.location.href = '/migrations')}
                    className='bg-green-600 hover:bg-green-700'
                  >
                    <Eye className='mr-2 h-4 w-4' />
                    View All Migrations
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}
