'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Play,
  Plus,
  Activity,
  Database,
  Settings,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw,
  Eye
} from 'lucide-react';
import AppLayout from '@/components/layout/AppLayout';

import { pipelineApi, ETLPipelineWithRuns, PipelineStats } from '@/lib/pipeline-api';
import { PipelineRunConfirmationDialog } from '@/components/ui/pipeline-run-confirmation-dialog';
import { PipelineProgressIndicator, createDefaultPipelineSteps, PipelineStep } from '@/components/ui/pipeline-progress-indicator';
import { useWebSocket, PipelineUpdate } from '@/hooks/useWebSocket';

export default function PipelinesPage() {
  const router = useRouter();
  const [pipelines, setPipelines] = useState<ETLPipelineWithRuns[]>([]);
  const [stats, setStats] = useState<PipelineStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [executingPipelines, setExecutingPipelines] = useState<Set<number>>(new Set());
  const [confirmationDialog, setConfirmationDialog] = useState<{
    open: boolean;
    pipeline: ETLPipelineWithRuns | null;
  }>({ open: false, pipeline: null });
  const [progressIndicator, setProgressIndicator] = useState<{
    visible: boolean;
    pipelineName: string;
    steps: PipelineStep[];
    overallProgress: number;
  }>({ visible: false, pipelineName: '', steps: [], overallProgress: 0 });

  // Debug: Force show progress indicator for testing
  // React.useEffect(() => {
  //   const steps = createDefaultPipelineSteps();
  //   setProgressIndicator({
  //     visible: true,
  //     pipelineName: 'Debug Test Pipeline',
  //     steps,
  //     overallProgress: 25
  //   });
  // }, []);

  // WebSocket connection for real-time updates
  const { isConnected, subscribeToPipeline, unsubscribeFromPipeline } = useWebSocket({
    url: `ws://localhost:8000/api/v1/ws/pipeline-updates`,
    onPipelineUpdate: (pipelineId: number, update: PipelineUpdate) => {
      handlePipelineUpdate(pipelineId, update);
    },
    onPipelineCompletion: (pipelineId: number, status: string, runData: any) => {
      handlePipelineCompletion(pipelineId, status, runData);
    },
    onStatsUpdate: (statsData: any) => {
      setStats(statsData);
    },
    onConnect: () => {
      console.log('WebSocket connected');
    },
    onDisconnect: () => {
      console.log('WebSocket disconnected');
    }
  });

  useEffect(() => {
    loadData();

    // Set up auto-refresh for statistics every 30 seconds
    const refreshInterval = setInterval(() => {
      // Only refresh stats if no pipelines are currently executing
      if (executingPipelines.size === 0) {
        pipelineApi.getStats().then(setStats).catch(console.error);
      }
    }, 30000);

    // Set up polling for executing pipelines to ensure UI updates
    const pollingInterval = setInterval(() => {
      if (executingPipelines.size > 0) {
        // Poll less frequently to give progress indicator time to show
        loadData();
      }
    }, 10000);

    return () => {
      clearInterval(refreshInterval);
      clearInterval(pollingInterval);
    };
  }, [executingPipelines.size]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [pipelinesData, statsData] = await Promise.all([
        pipelineApi.listPipelines(true), // Include inactive pipelines
        pipelineApi.getStats()
      ]);

      setPipelines(pipelinesData);
      setStats(statsData);

      // Check if any executing pipelines have actually completed
      // This is a fallback in case WebSocket notifications don't work
      setExecutingPipelines(prev => {
        const newSet = new Set(prev);
        let hasChanges = false;

        prev.forEach(pipelineId => {
          const pipeline = pipelinesData.find(p => p.id === pipelineId);
          if (pipeline && pipeline.last_run) {
            // If the last run is not in 'running' status, the pipeline has completed
            if (pipeline.last_run.status !== 'running') {
              newSet.delete(pipelineId);
              hasChanges = true;

              // Update progress indicator to show completion instead of hiding immediately
              setProgressIndicator(prev => {
                if (prev.visible && prev.pipelineName === pipeline.name) {
                  const completedSteps = prev.steps.map(step => ({
                    ...step,
                    status: pipeline.last_run!.status === 'completed' ? 'completed' as const : 'failed' as const
                  }));

                  const updatedIndicator = {
                    ...prev,
                    steps: completedSteps,
                    overallProgress: 100
                  };

                  // Don't auto-hide - wait for user to close manually

                  return updatedIndicator;
                }
                return prev;
              });
            }
          }
        });

        return hasChanges ? newSet : prev;
      });

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load pipeline data');
    } finally {
      setLoading(false);
    }
  };

  const navigateToRunDetails = (pipelineId: number, runId: number) => {
    router.push(`/pipelines/${pipelineId}/runs/${runId}`);
  };

  const handlePipelineUpdate = (pipelineId: number, update: PipelineUpdate) => {
    setProgressIndicator(prev => {
      if (!prev.visible) return prev;

      const updatedSteps = prev.steps.map(step => {
        if (step.id === update.step) {
          return {
            ...step,
            status: update.status,
            progress: update.progress,
            message: update.message
          };
        }
        return step;
      });

      return {
        ...prev,
        steps: updatedSteps,
        overallProgress: update.progress || prev.overallProgress
      };
    });
  };

  const handlePipelineCompletion = (pipelineId: number, status: string, runData: any) => {
    // Update progress indicator to show completion
    setProgressIndicator(prev => ({
      ...prev,
      overallProgress: 100,
      steps: prev.steps.map(step => ({
        ...step,
        status: status === 'completed' ? 'completed' as const : 'failed' as const
      }))
    }));

    // Don't auto-hide - wait for user to close manually

    // Refresh pipeline data
    loadData();

    // Remove from executing pipelines
    setExecutingPipelines(prev => {
      const newSet = new Set(prev);
      newSet.delete(pipelineId);
      return newSet;
    });
  };



  const handleExecutePipeline = (pipelineId: number) => {
    const pipeline = pipelines.find(p => p.id === pipelineId);
    if (pipeline) {
      setConfirmationDialog({ open: true, pipeline });
    }
  };

  const handleConfirmExecution = async () => {
    if (!confirmationDialog.pipeline) return;

    const pipelineId = confirmationDialog.pipeline.id;
    const pipeline = confirmationDialog.pipeline;

    try {
      setExecutingPipelines(prev => new Set(prev).add(pipelineId));

      // Close the confirmation dialog first
      setConfirmationDialog({ open: false, pipeline: null });

      // Initialize progress indicator with fresh steps
      const steps = createDefaultPipelineSteps();
      console.log('Setting progress indicator visible for pipeline:', pipeline.name);
      setProgressIndicator({
        visible: true,
        pipelineName: pipeline.name,
        steps: steps.map(step => ({ ...step, status: 'pending' as const })), // Ensure all steps start as pending
        overallProgress: 0
      });

      // Start the pipeline execution
      const executionPromise = pipelineApi.executePipeline(pipelineId);

      // Subscribe to pipeline updates via WebSocket
      console.log('WebSocket connected:', isConnected);
      if (isConnected) {
        console.log('Subscribing to pipeline updates via WebSocket');
        subscribeToPipeline(pipelineId);
      } else {
        console.log('WebSocket not connected, using simulation fallback');
        // Fallback to simulation if WebSocket is not connected
        // Add a small delay to ensure progress indicator is visible
        setTimeout(() => {
          simulateProgressUpdates(steps);
        }, 500);
      }

      await executionPromise;

      // Note: Pipeline completion will be handled via WebSocket callbacks
      // or by the polling mechanism in loadData()

    } catch (err) {
      // Mark current step as failed
      setProgressIndicator(prev => ({
        ...prev,
        steps: prev.steps.map(step =>
          step.status === 'running' ? { ...step, status: 'failed' as const } : step
        )
      }));

      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      alert(`Failed to execute pipeline: ${errorMessage}`);

      // Remove from executing pipelines on error
      setExecutingPipelines(prev => {
        const newSet = new Set(prev);
        newSet.delete(pipelineId);
        return newSet;
      });

      // Close the confirmation dialog
      setConfirmationDialog({ open: false, pipeline: null });
    }
  };

  const simulateProgressUpdates = (initialSteps: PipelineStep[]) => {
    const stepDurations = [2000, 1500, 3000, 1500, 2000]; // Simulated durations for each step
    let currentStepIndex = 0;

    const updateStep = () => {
      if (currentStepIndex >= initialSteps.length) {
        // All steps completed
        setProgressIndicator(prev => ({
          ...prev,
          overallProgress: 100,
          steps: prev.steps.map(step => ({ ...step, status: 'completed' as const }))
        }));
        return;
      }

      // Mark current step as running
      setProgressIndicator(prev => ({
        ...prev,
        overallProgress: (currentStepIndex / initialSteps.length) * 100,
        steps: prev.steps.map((step, index) => {
          if (index === currentStepIndex) {
            return { ...step, status: 'running' as const, message: 'Processing...' };
          } else if (index < currentStepIndex) {
            return { ...step, status: 'completed' as const };
          }
          return step;
        })
      }));

      // Complete current step after duration
      setTimeout(() => {
        setProgressIndicator(prev => ({
          ...prev,
          overallProgress: ((currentStepIndex + 1) / initialSteps.length) * 100,
          steps: prev.steps.map((step, index) =>
            index === currentStepIndex ? { ...step, status: 'completed' as const, message: 'Completed' } : step
          )
        }));

        currentStepIndex++;
        setTimeout(updateStep, 500); // Small delay before next step
      }, stepDurations[currentStepIndex]);
    };

    updateStep();
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Completed</Badge>;
      case 'running':
        return <Badge variant="default" className="bg-blue-100 text-blue-800"><Activity className="w-3 h-3 mr-1" />Running</Badge>;
      case 'failed':
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Failed</Badge>;
      default:
        return <Badge variant="secondary"><AlertCircle className="w-3 h-3 mr-1" />{status}</Badge>;
    }
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'N/A';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  if (loading) {
    return (
      <AppLayout>
        <div className="container mx-auto p-6">
          <div className="flex items-center justify-center h-64">
            <RefreshCw className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading pipelines...</span>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="container mx-auto p-6">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Error Loading Pipelines</h3>
                <p className="text-gray-600 mb-4">{error}</p>
                <Button onClick={loadData}>
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Retry
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="container mx-auto p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Data Pipelines</h1>
          <p className="text-gray-600">Manage and monitor your ETL data pipelines</p>
        </div>
        <div className="flex space-x-2">
          <Button onClick={loadData} variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          {/* Debug button to test progress indicator - client-only */}
          {typeof window !== 'undefined' && process.env.NODE_ENV === 'development' && (
            <Button
              onClick={() => {
                const steps = createDefaultPipelineSteps();
                setProgressIndicator({
                  visible: true,
                  pipelineName: 'Test Pipeline',
                  steps: steps.map(step => ({ ...step, status: 'pending' as const })),
                  overallProgress: 0
                });
              }}
              variant="outline"
            >
              Test Progress
            </Button>
          )}
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            Create Pipeline
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Database className="h-8 w-8 text-blue-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Pipelines</p>
                  <p className="text-2xl font-bold">{stats.total_pipelines}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Activity className="h-8 w-8 text-green-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Pipelines</p>
                  <p className="text-2xl font-bold">{stats.active_pipelines}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Successful Runs</p>
                  <p className="text-2xl font-bold">{stats.successful_runs}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <XCircle className="h-8 w-8 text-red-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Failed Runs</p>
                  <p className="text-2xl font-bold">{stats.failed_runs}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Pipeline List */}
      <div className="grid gap-6">
        {pipelines.map((pipeline) => (
          <Card key={pipeline.id}>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="flex items-center">
                    {pipeline.name}
                    {!pipeline.is_active && (
                      <Badge variant="secondary" className="ml-2">Inactive</Badge>
                    )}
                  </CardTitle>
                  <CardDescription>{pipeline.description}</CardDescription>
                </div>
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    onClick={() => handleExecutePipeline(pipeline.id)}
                    disabled={executingPipelines.has(pipeline.id) || !pipeline.is_active}
                  >
                    {executingPipelines.has(pipeline.id) ? (
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                      <Play className="w-4 h-4 mr-2" />
                    )}
                    Run
                  </Button>
                  <Button size="sm" variant="outline">
                    <Settings className="w-4 h-4 mr-2" />
                    Configure
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                  <p className="text-sm font-medium text-gray-600">Source</p>
                  <p className="text-sm">{pipeline.source_type}: {pipeline.source_object}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Destination</p>
                  <p className="text-sm">{pipeline.destination_type}: {pipeline.destination_table}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Runs</p>
                  <p className="text-sm">{pipeline.total_runs}</p>
                </div>
              </div>

              {pipeline.last_run && (
                <div className="border-t pt-4">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="text-sm font-medium">Last Run</h4>
                    {getStatusBadge(pipeline.last_run.status)}
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Started</p>
                      <p>{formatDateTime(pipeline.last_run.started_at)}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Duration</p>
                      <p>{formatDuration(pipeline.last_run.duration_seconds)}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Records Processed</p>
                      <p>
                        {pipeline.last_run.records_extracted || 0} extracted → {pipeline.last_run.records_transformed || 0} transformed → {pipeline.last_run.records_loaded || 0} loaded
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-600">Status</p>
                      <p>{pipeline.last_run.error_message || 'Success'}</p>
                    </div>
                  </div>
                  {pipeline.last_run.status === 'completed' && (
                    <div className="mt-4 flex justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigateToRunDetails(pipeline.id, pipeline.last_run!.id)}
                      >
                        <Eye className="w-4 h-4 mr-2" />
                        View Details
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {pipelines.length === 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Pipelines Found</h3>
              <p className="text-gray-600 mb-4">Get started by creating your first ETL pipeline</p>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Create Pipeline
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Pipeline Run Confirmation Dialog */}
      <PipelineRunConfirmationDialog
        open={confirmationDialog.open}
        onOpenChange={(open) => setConfirmationDialog({ open, pipeline: null })}
        pipeline={confirmationDialog.pipeline}
        onConfirm={handleConfirmExecution}
        isExecuting={confirmationDialog.pipeline ? executingPipelines.has(confirmationDialog.pipeline.id) : false}
      />

      {/* Pipeline Progress Indicator */}

      <PipelineProgressIndicator
        pipelineName={progressIndicator.pipelineName}
        steps={progressIndicator.steps}
        overallProgress={progressIndicator.overallProgress}
        isVisible={progressIndicator.visible}
        onClose={() => setProgressIndicator(prev => ({ ...prev, visible: false }))}
      />

    </div>
    </AppLayout>
  );
}
