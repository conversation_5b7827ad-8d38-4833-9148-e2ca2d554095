'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ArrowLeft,
  FileText, 
  Settings, 
  Database, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  RefreshCw,
  Download,
  Eye
} from 'lucide-react';
import AppLayout from '@/components/layout/AppLayout';

import { pipelineApi, ETLPipelineRun } from '@/lib/pipeline-api';

interface RunDetailsData {
  run: ETLPipelineRun;
  raw_data: any[];
  processed_data: any[];
  destination_data: any[];
}

export default function PipelineRunDetailPage() {
  const params = useParams();
  const router = useRouter();
  const pipelineId = parseInt(params.id as string);
  const runId = parseInt(params.runId as string);

  const [runDetails, setRunDetails] = useState<RunDetailsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadRunDetails();
  }, [pipelineId, runId]);

  const loadRunDetails = async () => {
    try {
      setLoading(true);
      setError(null);
      const details = await pipelineApi.getRunDetails(pipelineId, runId);
      setRunDetails(details);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load run details');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'running':
        return <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case 'failed':
        return <Badge className="bg-red-100 text-red-800">Failed</Badge>;
      case 'running':
        return <Badge className="bg-blue-100 text-blue-800">Running</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">Unknown</Badge>;
    }
  };

  const formatDuration = (startTime: string, endTime?: string) => {
    if (!endTime) return 'N/A';
    const start = new Date(startTime);
    const end = new Date(endTime);
    const duration = end.getTime() - start.getTime();
    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    }
    return `${remainingSeconds}s`;
  };

  if (loading) {
    return (
      <AppLayout>
        <div className="container mx-auto p-6">
          <div className="flex items-center justify-center h-64">
            <RefreshCw className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading run details...</span>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="container mx-auto p-6">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Error Loading Run Details</h3>
                <p className="text-gray-600 mb-4">{error}</p>
                <div className="space-x-2">
                  <Button onClick={loadRunDetails}>
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Retry
                  </Button>
                  <Button variant="outline" onClick={() => router.back()}>
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Go Back
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    );
  }

  if (!runDetails) {
    return (
      <AppLayout>
        <div className="container mx-auto p-6">
          <div className="text-center py-8">
            <p>No run details available</p>
            <Button variant="outline" onClick={() => router.back()} className="mt-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Go Back
            </Button>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="container mx-auto p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <Button variant="outline" onClick={() => router.back()}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Pipelines
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Pipeline Run Details</h1>
              <p className="text-gray-600">Run #{runDetails.run.id} - Pipeline {pipelineId}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {getStatusIcon(runDetails.run.status)}
            {getStatusBadge(runDetails.run.status)}
          </div>
        </div>

        {/* Run Summary */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="w-5 h-5 mr-2" />
              Execution Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              <div>
                <p className="text-sm text-gray-600">Status</p>
                <p className="font-medium">{runDetails.run.status}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Started</p>
                <p className="font-medium">{new Date(runDetails.run.started_at).toLocaleString()}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Duration</p>
                <p className="font-medium">{formatDuration(runDetails.run.started_at, runDetails.run.completed_at)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Records Extracted</p>
                <p className="font-medium text-blue-600">{runDetails.run.records_extracted || 0}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Records Transformed</p>
                <p className="font-medium text-orange-600">{runDetails.run.records_transformed || 0}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Records Loaded</p>
                <p className="font-medium text-green-600">{runDetails.run.records_loaded || 0}</p>
              </div>
            </div>
            
            {runDetails.run.error_message && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded">
                <p className="text-sm text-red-800">
                  <strong>Error:</strong> {runDetails.run.error_message}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Data Tabs */}
        <Tabs defaultValue="raw" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="raw" className="flex items-center">
              <FileText className="w-4 h-4 mr-2" />
              Raw Data ({runDetails.raw_data.length})
            </TabsTrigger>
            <TabsTrigger value="processed" className="flex items-center">
              <Settings className="w-4 h-4 mr-2" />
              Processed Data ({runDetails.processed_data.length})
            </TabsTrigger>
            <TabsTrigger value="destination" className="flex items-center">
              <Database className="w-4 h-4 mr-2" />
              Destination Data ({runDetails.destination_data.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="raw">
            <Card>
              <CardHeader>
                <CardTitle>Raw Data from Salesforce</CardTitle>
                <CardDescription>
                  Original data extracted from Salesforce before any transformations
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 p-4 rounded-lg overflow-x-auto">
                  <pre className="text-sm">
                    {JSON.stringify(runDetails.raw_data, null, 2)}
                  </pre>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="processed">
            <Card>
              <CardHeader>
                <CardTitle>Processed Data</CardTitle>
                <CardDescription>
                  Data after applying transformations and field mappings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 p-4 rounded-lg overflow-x-auto">
                  <pre className="text-sm">
                    {JSON.stringify(runDetails.processed_data, null, 2)}
                  </pre>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="destination">
            <Card>
              <CardHeader>
                <CardTitle>Destination Data</CardTitle>
                <CardDescription>
                  Final data loaded into the PostgreSQL database
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 p-4 rounded-lg overflow-x-auto">
                  <pre className="text-sm">
                    {JSON.stringify(runDetails.destination_data, null, 2)}
                  </pre>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
}
