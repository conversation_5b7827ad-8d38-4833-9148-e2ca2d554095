'use client';

import { useState } from 'react';
import AppLayout from '@/components/layout/AppLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Briefcase,
  Play,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from 'lucide-react';

// Mock data for migration jobs
const MOCK_MIGRATION_JOBS = [
  {
    id: '1',
    name: 'Salesforce Account Migration',
    records: 15432,
    status: 'Running',
    progress: 75,
    startedAt: '2 hours ago',
    statusColor: 'yellow',
  },
  {
    id: '2',
    name: 'Contact Records Migration',
    records: 8921,
    status: 'Completed',
    progress: 100,
    startedAt: '1 day ago',
    statusColor: 'green',
  },
  {
    id: '3',
    name: 'Opportunity Migration',
    records: 3245,
    status: 'Failed',
    progress: 45,
    startedAt: '2 days ago',
    statusColor: 'red',
  },
  {
    id: '4',
    name: 'Lead Information Migration',
    records: 12567,
    status: 'Completed',
    progress: 100,
    startedAt: '3 days ago',
    statusColor: 'green',
  },
  {
    id: '5',
    name: 'Product Catalog Migration',
    records: 2890,
    status: 'Running',
    progress: 30,
    startedAt: '1 hour ago',
    statusColor: 'yellow',
  },
  {
    id: '6',
    name: 'Campaign Data Migration',
    records: 1456,
    status: 'Pending',
    progress: 0,
    startedAt: '5 minutes ago',
    statusColor: 'blue',
  },
  {
    id: '7',
    name: 'Case Records Migration',
    records: 7823,
    status: 'Completed',
    progress: 100,
    startedAt: '1 week ago',
    statusColor: 'green',
  },
  {
    id: '8',
    name: 'Task Management Migration',
    records: 4567,
    status: 'Failed',
    progress: 20,
    startedAt: '2 days ago',
    statusColor: 'red',
  },
  {
    id: '9',
    name: 'Event Calendar Migration',
    records: 2134,
    status: 'Completed',
    progress: 100,
    startedAt: '5 days ago',
    statusColor: 'green',
  },
  {
    id: '10',
    name: 'User Permissions Migration',
    records: 892,
    status: 'Running',
    progress: 60,
    startedAt: '30 minutes ago',
    statusColor: 'yellow',
  },
];

export default function JobsPage() {
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);
  const [searchTerm, setSearchTerm] = useState('');

  // Filter data based on search term
  const filteredJobs = MOCK_MIGRATION_JOBS.filter(
    job =>
      job.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      job.status.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Calculate pagination
  const totalItems = filteredJobs.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentItems = filteredJobs.slice(startIndex, endIndex);

  // Pagination handlers
  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  const goToFirstPage = () => goToPage(1);
  const goToPreviousPage = () => goToPage(currentPage - 1);
  const goToNextPage = () => goToPage(currentPage + 1);
  const goToLastPage = () => goToPage(totalPages);

  // Status badge styling
  const getStatusBadgeClass = (statusColor: string) => {
    switch (statusColor) {
      case 'green':
        return 'bg-green-100 text-green-700';
      case 'yellow':
        return 'bg-yellow-100 text-yellow-700';
      case 'red':
        return 'bg-red-100 text-red-700';
      case 'blue':
        return 'bg-blue-100 text-blue-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  // Get action buttons based on status
  const getActionButtons = (job: (typeof MOCK_MIGRATION_JOBS)[0]) => {
    switch (job.status) {
      case 'Running':
        return (
          <>
            <Button variant='outline' size='sm'>
              View Logs
            </Button>
            <Button
              variant='outline'
              size='sm'
              className='text-orange-500 hover:bg-orange-50 hover:text-orange-700'
            >
              Cancel
            </Button>
          </>
        );
      case 'Failed':
        return (
          <>
            <Button className='bg-blue-600 text-white hover:bg-blue-700' size='sm'>
              Retry
            </Button>
            <Button variant='outline' size='sm'>
              View Logs
            </Button>
            <Button
              variant='outline'
              size='sm'
              className='text-red-500 hover:bg-red-50 hover:text-red-700'
            >
              Delete
            </Button>
          </>
        );
      default:
        return (
          <>
            <Button variant='outline' size='sm'>
              View Logs
            </Button>
            <Button
              variant='outline'
              size='sm'
              className='text-red-500 hover:bg-red-50 hover:text-red-700'
            >
              Delete
            </Button>
          </>
        );
    }
  };

  return (
    <AppLayout>
      <div className='p-8'>
        {/* Page Header */}
        <div className='mb-8'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center space-x-4'>
              <div className='flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-green-500 to-green-600 shadow-lg'>
                <Briefcase className='h-6 w-6 text-white' />
              </div>
              <div>
                <h1 className='text-3xl font-bold text-slate-900'>Jobs</h1>
                <p className='mt-1 text-slate-600'>Monitor and manage migration job history</p>
              </div>
            </div>
            <Button className='bg-blue-600 text-white hover:bg-blue-700'>
              <Play className='mr-2 h-4 w-4' />
              New Job
            </Button>
          </div>
        </div>

        {/* Job List */}
        <Card className='rounded-xl border border-slate-200/60 bg-white/80 shadow-lg backdrop-blur-sm'>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <div className='flex items-center space-x-3'>
                <div>
                  <CardTitle className='text-xl font-bold text-slate-900'>Migration Jobs</CardTitle>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className='space-y-4'>
            {/* Search Filter */}
            <div className='flex items-center space-x-2'>
              <Input
                placeholder='Search migration jobs...'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className='max-w-sm'
              />
            </div>

            {/* Table */}
            <div className='rounded-md border'>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Job Name</TableHead>
                    <TableHead>Records</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Progress</TableHead>
                    <TableHead>Started</TableHead>
                    <TableHead className='text-right'>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {currentItems.length > 0 ? (
                    currentItems.map(job => (
                      <TableRow key={job.id} className='group'>
                        <TableCell>
                          <div className='font-semibold text-slate-900'>{job.name}</div>
                        </TableCell>
                        <TableCell>
                          <span className='text-sm text-slate-600'>
                            {job.records.toLocaleString()} records
                          </span>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant='secondary'
                            className={getStatusBadgeClass(job.statusColor)}
                          >
                            {job.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className='flex items-center space-x-2'>
                            <div className='h-2 w-16 rounded-full bg-gray-200'>
                              <div
                                className={`h-2 rounded-full ${
                                  job.statusColor === 'green'
                                    ? 'bg-green-500'
                                    : job.statusColor === 'yellow'
                                      ? 'bg-yellow-500'
                                      : job.statusColor === 'red'
                                        ? 'bg-red-500'
                                        : 'bg-blue-500'
                                }`}
                                style={{ width: `${job.progress}%` }}
                              ></div>
                            </div>
                            <span className='text-sm text-slate-500'>{job.progress}%</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className='text-sm text-slate-500'>{job.startedAt}</span>
                        </TableCell>
                        <TableCell className='text-right'>
                          <div className='flex items-center justify-end space-x-2 opacity-0 transition-opacity duration-200 group-hover:opacity-100'>
                            {getActionButtons(job)}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className='h-24 text-center'>
                        No migration jobs found.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            <div className='flex items-center justify-between space-x-2 py-4'>
              <div className='text-muted-foreground text-sm'>
                Showing {startIndex + 1} to {Math.min(endIndex, totalItems)} of {totalItems} results
              </div>
              <div className='flex items-center space-x-2'>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={goToFirstPage}
                  disabled={currentPage === 1}
                >
                  <ChevronsLeft className='h-4 w-4' />
                </Button>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={goToPreviousPage}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className='h-4 w-4' />
                </Button>
                <span className='text-sm'>
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={goToNextPage}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className='h-4 w-4' />
                </Button>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={goToLastPage}
                  disabled={currentPage === totalPages}
                >
                  <ChevronsRight className='h-4 w-4' />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
