'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Database,
  Eye,
  Trash2,
  RefreshCw,
  CheckCircle,
  Clock,
  XCircle,
  Zap,
  AlertTriangle,
  Play,
} from 'lucide-react';
import AppLayout from '@/components/layout/AppLayout';
import { salesforceOAuthApi } from '@/lib/api';

interface Migration {
  id: string;
  job_name: string;
  object_name: string;
  status: string; // API returns string, we'll handle the casting
  progress_percentage: number;
  current_step: string;
  created_at: string;
  completed_at?: string;

  // Combined job + staged data info
  records_extracted: number;
  records_staged: number;
  records_synced: number;
  records_failed: number;

  error_message?: string;

  // Derived status for UI
  can_sync: boolean;
  has_errors: boolean;
}

// Mock data combining job + staged data information
const MOCK_MIGRATIONS: Migration[] = [
  {
    id: '1',
    job_name: 'Extract Account - 150 records',
    object_name: 'Account',
    status: 'completed',
    progress_percentage: 100,
    current_step: 'Ready to sync',
    created_at: '2024-01-15T10:30:00Z',
    completed_at: '2024-01-15T10:32:15Z',
    records_extracted: 150,
    records_staged: 150,
    records_synced: 0,
    records_failed: 0,
    can_sync: true,
    has_errors: false,
  },
  {
    id: '2',
    job_name: 'Extract Contact - 75 records',
    object_name: 'Contact',
    status: 'failed',
    progress_percentage: 45,
    current_step: 'Failed during staging',
    created_at: '2024-01-15T09:15:00Z',
    completed_at: '2024-01-15T09:16:30Z',
    records_extracted: 75,
    records_staged: 0,
    records_synced: 0,
    records_failed: 75,
    error_message: 'Database connection timeout',
    can_sync: false,
    has_errors: true,
  },
  {
    id: '3',
    job_name: 'Extract Opportunity - 200 records',
    object_name: 'Opportunity',
    status: 'staging',
    progress_percentage: 75,
    current_step: 'Creating staged data',
    created_at: '2024-01-15T11:00:00Z',
    records_extracted: 200,
    records_staged: 150,
    records_synced: 0,
    records_failed: 0,
    can_sync: false,
    has_errors: false,
  },
];

const getStatusIcon = (status: string, hasErrors: boolean) => {
  if (hasErrors) return <XCircle className="h-5 w-5 text-red-600" />;

  switch (status) {
    case 'completed':
      return <CheckCircle className="h-5 w-5 text-green-600" />;
    case 'failed':
    case 'cancelled':
      return <XCircle className="h-5 w-5 text-red-600" />;
    case 'pending':
      return <Clock className="h-5 w-5 text-gray-500" />;
    case 'extracting':
    case 'transforming':
    case 'staging':
    case 'syncing':
      return <Clock className="h-5 w-5 text-yellow-600" />;
    default:
      return <Play className="h-5 w-5 text-blue-600" />;
  }
};

const getStatusBadge = (status: string, canSync: boolean, hasErrors: boolean) => {
  if (hasErrors) {
    return <Badge className="bg-red-100 text-red-800">Failed</Badge>;
  }
  if (canSync) {
    return <Badge className="bg-green-100 text-green-800">Ready to Sync</Badge>;
  }

  const variants: Record<string, string> = {
    completed: 'bg-green-100 text-green-800',
    failed: 'bg-red-100 text-red-800',
    cancelled: 'bg-gray-100 text-gray-800',
    pending: 'bg-gray-100 text-gray-800',
    extracting: 'bg-blue-100 text-blue-800',
    transforming: 'bg-purple-100 text-purple-800',
    staging: 'bg-yellow-100 text-yellow-800',
    syncing: 'bg-indigo-100 text-indigo-800',
  };

  const className = variants[status] || 'bg-gray-100 text-gray-800';

  return (
    <Badge className={className}>
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </Badge>
  );
};

export default function MigrationsPage() {
  const [migrations, setMigrations] = useState<Migration[]>(MOCK_MIGRATIONS);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    loadMigrations();
  }, []);

  const loadMigrations = async () => {
    setIsLoading(true);
    setError('');
    try {
      const response = await salesforceOAuthApi.getMigrations();
      setMigrations(response.migrations);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load migrations');
      // Fallback to mock data if API fails
      console.warn('API failed, using mock data:', err);
      setMigrations(MOCK_MIGRATIONS);
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewMigration = (migration: any) => {
    // Navigate to appropriate step based on migration status
    if (migration.status === 'completed') {
      // For completed migrations, go to edit step to review/modify staged data
      window.location.href = `/migration?job_id=${migration.id}&step=edit`;
    } else if (migration.records_staged > 0) {
      // Has staged data, go to edit step
      window.location.href = `/migration?job_id=${migration.id}&step=edit`;
    } else if (migration.records_extracted > 0) {
      // Has extracted data, go to transform step
      window.location.href = `/migration?job_id=${migration.id}&step=transform`;
    } else {
      // No data extracted yet, go to extract step
      window.location.href = `/migration?job_id=${migration.id}&step=extract`;
    }
  };

  const getViewButtonText = (migration: any) => {
    if (migration.status === 'completed') {
      return 'Review Data';
    } else if (migration.records_staged > 0) {
      return 'Edit Data';
    } else if (migration.records_extracted > 0) {
      return 'Transform';
    } else {
      return 'Continue';
    }
  };

  const handleSyncMigration = async (migrationId: string) => {
    if (!confirm('Are you sure you want to sync this migration to the target system?')) {
      return;
    }

    try {
      const result = await salesforceOAuthApi.syncMigration(migrationId);

      // Show success message
      alert(`Sync completed: ${result.records_synced} records synced successfully`);

      // Reload migrations to get updated status
      await loadMigrations();

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to sync migration');
    }
  };

  const handleDeleteMigration = async (migrationId: string) => {
    if (!confirm('Are you sure you want to delete this migration? This action cannot be undone.')) {
      return;
    }

    try {
      await salesforceOAuthApi.deleteMigration(migrationId);
      // Remove from local state after successful deletion
      setMigrations(migrations.filter(m => m.id !== migrationId));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete migration');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const totalMigrations = migrations.length;
  const readyToSync = migrations.filter(m => m.can_sync).length;
  const running = migrations.filter(m => ['extracting', 'transforming', 'staging', 'syncing'].includes(m.status)).length;
  const failed = migrations.filter(m => m.has_errors).length;

  return (
    <AppLayout>
      <div className="space-y-6 p-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-slate-900">Migrations</h1>
            <p className="text-slate-600 mt-2">
              Manage your ETL migrations from extraction to sync
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button
              onClick={loadMigrations}
              disabled={isLoading}
              variant="outline"
              className="border-slate-300 text-slate-700 hover:bg-slate-50"
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button
              onClick={() => window.location.href = '/migration'}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              <Play className="mr-2 h-4 w-4" />
              New Migration
            </Button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <Card className="border border-red-200 bg-red-50 shadow-sm">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3 text-red-700">
                <XCircle className="h-5 w-5 flex-shrink-0" />
                <p className="font-medium">{error}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="border border-slate-200 shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="px-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600 mb-1">Total</p>
                  <p className="text-3xl font-bold text-slate-900">{totalMigrations}</p>
                </div>
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                  <Database className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border border-slate-200 shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="px-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600 mb-1">Ready to Sync</p>
                  <p className="text-3xl font-bold text-green-600">{readyToSync}</p>
                </div>
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                  <Zap className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border border-slate-200 shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="px-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600 mb-1">Running</p>
                  <p className="text-3xl font-bold text-yellow-600">{running}</p>
                </div>
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100">
                  <Clock className="h-6 w-6 text-yellow-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border border-slate-200 shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="px-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600 mb-1">Failed</p>
                  <p className="text-3xl font-bold text-red-600">{failed}</p>
                </div>
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
                  <XCircle className="h-6 w-6 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Migrations List */}
        <div className="space-y-4">
          {isLoading ? (
            <Card className="border border-slate-200 shadow-sm">
              <CardContent className="p-8">
                <div className="text-center py-12">
                  <RefreshCw className="h-12 w-12 text-slate-400 mx-auto mb-4 animate-spin" />
                  <h3 className="text-lg font-semibold text-slate-900 mb-2">Loading Migrations</h3>
                  <p className="text-slate-500">Fetching migration data...</p>
                </div>
              </CardContent>
            </Card>
          ) : migrations.length === 0 ? (
            <Card className="border border-slate-200 shadow-sm">
              <CardContent className="p-8">
                <div className="text-center py-12">
                  <div className="flex h-16 w-16 items-center justify-center rounded-full bg-slate-100 mx-auto mb-4">
                    <Database className="h-8 w-8 text-slate-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-slate-900 mb-2">No Migrations</h3>
                  <p className="text-slate-500 mb-6">
                    No migrations found. Start your first migration to see it here.
                  </p>
                  <Button
                    onClick={() => window.location.href = '/migration'}
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    <Play className="mr-2 h-4 w-4" />
                    Start Migration
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            migrations.map((migration) => (
              <Card key={migration.id} className="border border-slate-200 shadow-sm hover:shadow-md transition-shadow">
                <CardContent className="px-6">
                  {/* Header Row */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(migration.status, migration.has_errors)}
                      <div>
                        <h3 className="text-lg font-semibold text-slate-900">{migration.job_name}</h3>
                        <p className="text-sm text-slate-500">{migration.object_name} • {formatDate(migration.created_at)}</p>
                      </div>
                    </div>
                    {getStatusBadge(migration.status, migration.can_sync, migration.has_errors)}
                  </div>

                  {/* Progress Section */}
                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-slate-700">{migration.current_step}</span>
                      <span className="text-sm text-slate-500">{migration.progress_percentage}%</span>
                    </div>
                    <div className="w-full bg-slate-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${migration.progress_percentage}%` }}
                      />
                    </div>
                  </div>

                  {/* Records Summary */}
                  <div className="flex items-center space-x-6 text-sm mb-4">
                    <div className="flex items-center space-x-1">
                      <span className="text-blue-600">📥</span>
                      <span className="text-slate-600">{migration.records_extracted} extracted</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span className="text-purple-600">📋</span>
                      <span className="text-slate-600">{migration.records_staged} staged</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span className="text-green-600">✅</span>
                      <span className="text-slate-600">{migration.records_synced} synced</span>
                    </div>
                    {migration.records_failed > 0 && (
                      <div className="flex items-center space-x-1">
                        <span className="text-red-600">❌</span>
                        <span className="text-red-600">{migration.records_failed} failed</span>
                      </div>
                    )}
                  </div>

                  {/* Error Message */}
                  {migration.error_message && (
                    <div className="flex items-center space-x-2 text-sm text-red-700 bg-red-50 border border-red-200 p-3 rounded-md mb-4">
                      <AlertTriangle className="h-4 w-4 flex-shrink-0" />
                      <span>{migration.error_message}</span>
                    </div>
                  )}

                  {/* Actions Row */}
                  <div className="flex items-center justify-end space-x-2 pt-2 border-t border-slate-100">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewMigration(migration)}
                      className="border-slate-300 text-slate-700 hover:bg-slate-50"
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      {getViewButtonText(migration)}
                    </Button>

                    {migration.can_sync && (
                      <Button
                        size="sm"
                        onClick={() => handleSyncMigration(migration.id)}
                        className="bg-green-600 hover:bg-green-700 text-white"
                      >
                        <Zap className="h-4 w-4 mr-2" />
                        Sync
                      </Button>
                    )}

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteMigration(migration.id)}
                      className="border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </AppLayout>
  );
}
