import { MigrationStartForm } from '@/components/forms/migration-start-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  ArrowLeft,
  Upload,
  Database,
  Settings,
  CheckCircle,
  Zap,
  Shield,
  Clock,
} from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

export default function NewMigrationPage() {
  return (
    <div className='min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50'>
      {/* Header Section */}
      <div className='sticky top-0 z-10 border-b border-slate-200 bg-white/80 backdrop-blur-sm'>
        <div className='container mx-auto px-6 py-6'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center space-x-4'>
              <Link href='/'>
                <Button variant='ghost' size='sm' className='text-slate-600 hover:text-slate-900'>
                  <ArrowLeft className='mr-2 h-4 w-4' />
                  Back to Dashboard
                </Button>
              </Link>
            </div>
            <Badge variant='outline' className='border-blue-200 bg-blue-50 text-blue-700'>
              New Migration
            </Badge>
          </div>
        </div>
      </div>

      <div className='container mx-auto px-6 py-12'>
        {/* Hero Section */}
        <div className='mb-16 text-center'>
          <div className='mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600'>
            <Database className='h-8 w-8 text-white' />
          </div>
          <h1 className='mb-4 text-4xl font-bold tracking-tight text-slate-900'>
            Start New Migration
          </h1>
          <p className='mx-auto max-w-2xl text-xl leading-relaxed text-slate-600'>
            Configure and launch a seamless ETL migration from Salesforce to your target system with
            our intelligent pipeline
          </p>
        </div>

        {/* Process Overview */}
        <div className='mb-16'>
          <div className='mb-12 text-center'>
            <h2 className='mb-3 text-2xl font-semibold text-slate-900'>How It Works</h2>
            <p className='text-slate-600'>
              Our intelligent 4-step process ensures seamless data migration
            </p>
          </div>

          <div className='relative grid grid-cols-1 gap-8 md:grid-cols-4'>
            {/* Connection Lines */}
            <div className='absolute top-12 right-0 left-0 hidden h-0.5 bg-gradient-to-r from-blue-200 to-green-200 md:block'></div>

            {/* Step 1 */}
            <div className='relative'>
              <div className='flex flex-col items-center text-center'>
                <div className='relative z-10 mb-6 flex h-24 w-24 items-center justify-center rounded-2xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg shadow-blue-500/25'>
                  <Database className='h-10 w-10 text-white' />
                </div>
                <h3 className='mb-2 text-lg font-semibold text-slate-900'>Extract</h3>
                <p className='text-sm leading-relaxed text-slate-600'>
                  Securely pull data from Salesforce using optimized API calls
                </p>
              </div>
            </div>

            {/* Step 2 */}
            <div className='relative'>
              <div className='flex flex-col items-center text-center'>
                <div className='relative z-10 mb-6 flex h-24 w-24 items-center justify-center rounded-2xl bg-gradient-to-br from-yellow-500 to-orange-500 shadow-lg shadow-yellow-500/25'>
                  <Settings className='h-10 w-10 text-white' />
                </div>
                <h3 className='mb-2 text-lg font-semibold text-slate-900'>Transform</h3>
                <p className='text-sm leading-relaxed text-slate-600'>
                  Clean, validate, and map data fields to target schema
                </p>
              </div>
            </div>

            {/* Step 3 */}
            <div className='relative'>
              <div className='flex flex-col items-center text-center'>
                <div className='relative z-10 mb-6 flex h-24 w-24 items-center justify-center rounded-2xl bg-gradient-to-br from-purple-500 to-purple-600 shadow-lg shadow-purple-500/25'>
                  <Upload className='h-10 w-10 text-white' />
                </div>
                <h3 className='mb-2 text-lg font-semibold text-slate-900'>Stage</h3>
                <p className='text-sm leading-relaxed text-slate-600'>
                  Review and edit data before final synchronization
                </p>
              </div>
            </div>

            {/* Step 4 */}
            <div className='relative'>
              <div className='flex flex-col items-center text-center'>
                <div className='relative z-10 mb-6 flex h-24 w-24 items-center justify-center rounded-2xl bg-gradient-to-br from-green-500 to-green-600 shadow-lg shadow-green-500/25'>
                  <CheckCircle className='h-10 w-10 text-white' />
                </div>
                <h3 className='mb-2 text-lg font-semibold text-slate-900'>Sync</h3>
                <p className='text-sm leading-relaxed text-slate-600'>
                  Push validated data to your target system safely
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Migration Form */}
        <div className='mx-auto max-w-4xl'>
          <MigrationStartForm />
        </div>

        {/* Help Section */}
        <div className='mx-auto mt-14 max-w-4xl'>
          <Card className='border-0 bg-gradient-to-br from-slate-50 to-white shadow-xl'>
            <CardHeader className='pb-8 text-center'>
              <CardTitle className='text-2xl text-slate-900'>Migration Guide</CardTitle>
              <CardDescription className='text-lg text-slate-600'>
                Best practices for successful data migration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='grid grid-cols-1 gap-8 md:grid-cols-2'>
                <div className='space-y-4'>
                  <div className='flex items-start space-x-3'>
                    <div className='mt-0.5 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-blue-100'>
                      <span className='text-sm font-semibold text-blue-600'>1</span>
                    </div>
                    <div>
                      <h3 className='mb-1 font-semibold text-slate-900'>Choose Your Object</h3>
                      <p className='mb-2 text-sm leading-relaxed text-slate-600'>
                        Start with simpler objects before migrating complex ones with relationships.
                      </p>
                      <div className='space-y-1 text-xs text-slate-500'>
                        <div>
                          • <strong>product:</strong> Great for testing, fewer dependencies
                        </div>
                        <div>
                          • <strong>Accounts:</strong> Core business data, moderate complexity
                        </div>
                        <div>
                          • <strong>Contacts:</strong> Related to Accounts, consider order
                        </div>
                        <div>
                          • <strong>Opportunities:</strong> Complex relationships, migrate last
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className='flex items-start space-x-3'>
                    <div className='mt-0.5 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-green-100'>
                      <span className='text-sm font-semibold text-green-600'>2</span>
                    </div>
                    <div>
                      <h3 className='mb-1 font-semibold text-slate-900'>Configure Settings</h3>
                      <p className='text-sm leading-relaxed text-slate-600'>
                        Adjust batch sizes and processing options based on your data volume and
                        system capacity.
                      </p>
                    </div>
                  </div>
                </div>

                <div className='space-y-4'>
                  <div className='flex items-start space-x-3'>
                    <div className='mt-0.5 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-purple-100'>
                      <span className='text-sm font-semibold text-purple-600'>3</span>
                    </div>
                    <div>
                      <h3 className='mb-1 font-semibold text-slate-900'>Batch Size Guidelines</h3>
                      <p className='mb-2 text-sm leading-relaxed text-slate-600'>
                        Optimize performance with the right batch size for your dataset.
                      </p>
                      <div className='space-y-1 text-xs text-slate-500'>
                        <div>
                          • <strong>Small (&lt;10K):</strong> 1000-2000 records
                        </div>
                        <div>
                          • <strong>Medium (10K-100K):</strong> 500-1000 records
                        </div>
                        <div>
                          • <strong>Large (&gt;100K):</strong> 200-500 records
                        </div>
                        <div>
                          • <strong>Complex objects:</strong> Start smaller
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className='flex items-start space-x-3'>
                    <div className='mt-0.5 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-orange-100'>
                      <span className='text-sm font-semibold text-orange-600'>4</span>
                    </div>
                    <div>
                      <h3 className='mb-1 font-semibold text-slate-900'>Monitor Progress</h3>
                      <p className='text-sm leading-relaxed text-slate-600'>
                        Track your migration in real-time and review staged data before final sync.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
