/**
 * Tests for Excel export functionality
 */

import { exportToExcel, generateExcelFilename } from '../excel-export';

// Mock DOM methods for testing
const mockCreateElement = jest.fn();
const mockAppendChild = jest.fn();
const mockRemoveChild = jest.fn();
const mockClick = jest.fn();
const mockCreateObjectURL = jest.fn();
const mockRevokeObjectURL = jest.fn();

// Setup DOM mocks
beforeAll(() => {
  Object.defineProperty(document, 'createElement', {
    value: mockCreateElement,
  });
  Object.defineProperty(document.body, 'appendChild', {
    value: mockAppendChild,
  });
  Object.defineProperty(document.body, 'removeChild', {
    value: mockRemoveChild,
  });
  Object.defineProperty(URL, 'createObjectURL', {
    value: mockCreateObjectURL,
  });
  Object.defineProperty(URL, 'revokeObjectURL', {
    value: mockRevokeObjectURL,
  });

  // Mock link element
  const mockLink = {
    download: '',
    setAttribute: jest.fn(),
    style: { visibility: '' },
    click: mockClick,
  };
  mockCreateElement.mockReturnValue(mockLink);
  mockCreateObjectURL.mockReturnValue('blob:mock-url');
});

beforeEach(() => {
  jest.clearAllMocks();
});

describe('generateExcelFilename', () => {
  it('should generate filename with object name and timestamp', () => {
    const objectName = 'Account';
    const filename = generateExcelFilename(objectName);
    
    expect(filename).toMatch(/^export_Account_\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}\.xlsx$/);
  });

  it('should use custom prefix', () => {
    const objectName = 'Contact';
    const prefix = 'salesforce_data';
    const filename = generateExcelFilename(objectName, prefix);
    
    expect(filename).toMatch(/^salesforce_data_Contact_\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}\.xlsx$/);
  });
});

describe('exportToExcel', () => {
  const sampleData = [
    { Name: 'John Doe', Email: '<EMAIL>', Age: 30 },
    { Name: 'Jane Smith', Email: '<EMAIL>', Age: 25 },
  ];
  const selectedFields = ['Name', 'Email', 'Age'];

  it('should export data with headers', () => {
    exportToExcel(sampleData, selectedFields, { filename: 'test.xlsx' });

    expect(mockCreateElement).toHaveBeenCalledWith('a');
    expect(mockCreateObjectURL).toHaveBeenCalled();
    expect(mockClick).toHaveBeenCalled();
  });

  it('should handle empty data', () => {
    expect(() => {
      exportToExcel([], selectedFields);
    }).toThrow('No data to export');
  });

  it('should handle null and undefined values', () => {
    const dataWithNulls = [
      { Name: 'John', Email: null, Age: undefined },
      { Name: 'Jane', Email: '<EMAIL>', Age: 25 },
    ];

    expect(() => {
      exportToExcel(dataWithNulls, selectedFields);
    }).not.toThrow();
  });

  it('should escape quotes in data', () => {
    const dataWithQuotes = [
      { Name: 'John "Johnny" Doe', Email: '<EMAIL>', Age: 30 },
    ];

    expect(() => {
      exportToExcel(dataWithQuotes, selectedFields);
    }).not.toThrow();
  });
});
