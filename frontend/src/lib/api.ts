/**
 * API client for the ETL Migration System backend
 */
import axios from 'axios';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';
console.log('API_BASE_URL', API_BASE_URL);

// Temporary Bearer token for testing (keep for bearer token endpoints)
const BEARER_TOKEN =
  '00D30000000jHpo!AQEAQGOAcqoXjC4W7qiwrtACl2YmY7V6P7p.DkY9unAGINovmYZTJkJkaR.IKaCVDpXfFWrchDNVrzEYBodgImd87UPPgc0W';

// Create axios instance with Bearer token for bearer endpoints
const apiClientWithBearer = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${BEARER_TOKEN}`, // Add Bearer token to all requests
  },
});

// Types for our API responses
export interface MigrationJob {
  id: string;
  object_name: string;
  job_name?: string;
  status:
    | 'pending'
    | 'extracting'
    | 'transforming'
    | 'staging'
    | 'syncing'
    | 'completed'
    | 'failed'
    | 'cancelled';
  progress_percentage: number;
  current_step?: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  updated_at: string;
  records_extracted: number;
  records_transformed: number;
  records_staged: number;
  records_synced: number;
  records_failed: number;
  error_message?: string;
  retry_count: number;
  max_retries: number;
}

export interface StagedData {
  id: string;
  migration_job_id: string;
  object_name: string;
  source_id: string;
  target_id?: string;
  transformed_data: Record<string, string | number | boolean | null>;
  user_modified: boolean;
  user_modifications?: Record<string, string | number | boolean | null>;
  modified_by?: string;
  modified_at?: string;
  sync_status: 'pending' | 'synced' | 'failed' | 'skipped';
  sync_error?: string;
  validation_status: 'valid' | 'invalid' | 'warning';
  validation_errors?: string[];
  created_at: string;
  synced_at?: string;
}

export interface StagedDataListResponse {
  items: StagedData[];
  total: number;
  page: number;
  limit: number;
  pages: number;
  has_next: boolean;
  has_prev: boolean;
}

export interface StartMigrationRequest {
  object_name: string;
  job_name?: string;
  source_config?: Record<string, string | number | boolean>;
  target_config?: Record<string, string | number | boolean>;
  transformation_config?: Record<string, string | number | boolean>;
}

export interface StartMigrationResponse {
  job_id: string;
  message: string;
  status: string;
}

// Create axios instance for OAuth endpoints
const api = axios.create({
  baseURL: API_BASE_URL, // Use the explicit API_BASE_URL
  headers: {
    'Content-Type': 'application/json',
  },
});

// Log the actual base URL being used
console.log('OAuth API Client Base URL:', api.defaults.baseURL);

// API functions
export const migrationApi = {
  // Start a new migration
  startMigration: async (data: StartMigrationRequest): Promise<StartMigrationResponse> => {
    const response = await api.post('/api/v1/migration/start-migration', data);
    return response.data;
  },

  // Get migration job status
  getMigrationStatus: async (jobId: string): Promise<MigrationJob> => {
    const response = await api.get(`/api/v1/migration/migration-status/${jobId}`);
    return response.data;
  },

  // Get migration job details
  getMigrationJob: async (jobId: string): Promise<MigrationJob> => {
    const response = await api.get(`/api/v1/migration/migration-job/${jobId}`);
    return response.data;
  },

  // List migration jobs
  listMigrationJobs: async (params?: {
    object_name?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<MigrationJob[]> => {
    const response = await api.get('/api/v1/migration/migration-jobs', { params });
    return response.data;
  },

  // Cancel migration job
  cancelMigrationJob: async (jobId: string): Promise<{ message: string }> => {
    const response = await api.post(`/api/v1/migration/migration-job/${jobId}/cancel`);
    return response.data;
  },
};

export const stagedDataApi = {
  // Get staged data with pagination
  getStagedData: async (
    objectName: string,
    params?: {
      page?: number;
      limit?: number;
      migration_job_id?: string;
      sync_status?: string;
      validation_status?: string;
      user_modified?: boolean;
    }
  ): Promise<StagedDataListResponse> => {
    const response = await api.get(`/data/staged/${objectName}`, { params });
    return response.data;
  },

  // Update staged record
  updateStagedRecord: async (
    objectName: string,
    recordId: string,
    data: {
      field_updates: Record<string, string | number | boolean | null>;
      user_id?: string;
    }
  ): Promise<StagedData> => {
    const response = await api.patch(`/data/staged/${objectName}/${recordId}`, data);
    return response.data;
  },

  // Get specific staged record
  getStagedRecord: async (objectName: string, recordId: string): Promise<StagedData> => {
    const response = await api.get(`/data/staged/${objectName}/${recordId}`);
    return response.data;
  },

  // Get staged data summary
  getStagedDataSummary: async (
    objectName: string,
    migrationJobId?: string
  ): Promise<{
    total_records: number;
    pending_sync: number;
    synced: number;
    failed: number;
    user_modified: number;
    validation_errors: number;
  }> => {
    const params = migrationJobId ? { migration_job_id: migrationJobId } : undefined;
    const response = await api.get(`/data/staged/${objectName}/summary`, { params });
    return response.data;
  },
};

// Salesforce OAuth API functions (using stored credentials)
export const salesforceOAuthApi = {
  testConnection: async (): Promise<{
    status: string;
    message: string;
    authenticated: boolean;
  }> => {
    console.log('OAuth API: Calling test connection...');
    console.log('OAuth API: Base URL:', api.defaults.baseURL);
    console.log(
      'OAuth API: Full URL:',
      `${api.defaults.baseURL}/migration/salesforce/test-connection`
    );
    const response = await api.get('/migration/salesforce/test-connection');
    return response.data;
  },

  getObjects: async (): Promise<{
    objects: Array<{
      name: string;
      label: string;
      labelPlural: string;
      queryable: boolean;
      createable: boolean;
      updateable: boolean;
      deletable: boolean;
      custom: boolean;
      keyPrefix: string;
    }>;
    message: string;
  }> => {
    const response = await api.get('/migration/salesforce/objects');
    return response.data;
  },

  getObjectFields: async (
    objectName: string
  ): Promise<{
    object_name: string;
    fields: Array<{
      name: string;
      label: string;
      type: string;
      required: boolean;
      custom: boolean;
      updateable: boolean;
      createable: boolean;
      length: number;
      picklistValues: string[];
    }>;
    total_fields: number;
    message: string;
  }> => {
    const response = await api.get(`/migration/salesforce/objects/${objectName}/fields`);
    return response.data;
  },

  extractSelectedData: async (
    objectName: string,
    fields: string[],
    limit: number = 100,
    createJob: boolean = false
  ): Promise<{
    object_name: string;
    fields: string[];
    records: Array<Record<string, string | number | boolean | null>>;
    total_count: number;
    message: string;
    migration_job_id?: string;
  }> => {
    const response = await api.post(`/migration/salesforce/data/${objectName}`, {
      fields,
      limit,
      create_job: createJob,
    });
    return response.data;
  },

  createStagedData: async (
    migrationJobId: string,
    objectName: string,
    transformedRecords: any[],
    fieldMappings?: any[],
    transformationConfig?: any
  ): Promise<{
    success: boolean;
    migration_job_id: string;
    staged_records_created: number;
    message: string;
  }> => {
    const response = await api.post('/migration/staged-data', {
      migration_job_id: migrationJobId,
      object_name: objectName,
      transformed_records: transformedRecords,
      field_mappings: fieldMappings,
      transformation_config: transformationConfig,
    });
    return response.data;
  },

  // Migration management
  getMigrations: async (): Promise<{
    migrations: Array<{
      id: string;
      job_name: string;
      object_name: string;
      status: string;
      progress_percentage: number;
      current_step: string;
      created_at: string;
      completed_at?: string;
      records_extracted: number;
      records_staged: number;
      records_synced: number;
      records_failed: number;
      error_message?: string;
      can_sync: boolean;
      has_errors: boolean;
    }>;
  }> => {
    const response = await api.get('/migration/migrations');
    return response.data;
  },

  getMigrationDetails: async (migrationId: string): Promise<{
    migration: {
      id: string;
      job_name: string;
      object_name: string;
      status: string;
      progress_percentage: number;
      current_step: string;
      created_at: string;
      started_at?: string;
      completed_at?: string;
      source_config: any;
      target_config: any;
      transformation_config: any;
      records_extracted: number;
      records_staged: number;
      raw_data_count: number;
      error_message?: string;
    };
    staged_data: Array<{
      id: string;
      source_id: string;
      target_id?: string;
      transformed_data: any;
      user_modified: boolean;
      sync_status: string;
      validation_status: string;
      created_at: string;
    }>;
  }> => {
    const response = await api.get(`/migration/migrations/${migrationId}`);
    return response.data;
  },

  deleteMigration: async (migrationId: string): Promise<{
    message: string;
    deleted_records: {
      migration_job: number;
      raw_data_records: number;
      staged_data_records: number;
    };
  }> => {
    const response = await api.delete(`/migration/migrations/${migrationId}`);
    return response.data;
  },

  syncMigration: async (migrationId: string): Promise<{
    message: string;
    migration_id: string;
    records_processed: number;
    records_synced: number;
    records_failed: number;
    errors: string[];
  }> => {
    const response = await api.post(`/load/sync-migration/${migrationId}`);
    return response.data;
  },

  getData: async (
    objectName: string,
    limit: number = 100
  ): Promise<{
    object_name: string;
    records: Array<Record<string, string | number | boolean | null>>;
    total_count: number;
    message: string;
  }> => {
    const response = await api.get(`/migration/salesforce/data/${objectName}?limit=${limit}`);
    return response.data;
  },
};

// Target Schemas API
export const targetSchemasApi = {
  getAll: async (): Promise<
    Array<{
      id: string;
      name: string;
      label: string;
      description: string;
      target_system: 'ops' | 'crm';
      fields: Array<{
        name: string;
        label: string;
        type: string;
        required: boolean;
        description?: string;
        defaultValue?: string;
      }>;
      created_at: string;
      updated_at: string;
    }>
  > => {
    const response = await api.get('/target-schemas/');
    return response.data;
  },

  getById: async (
    id: string
  ): Promise<{
    id: string;
    name: string;
    label: string;
    description: string;
    target_system: 'ops' | 'crm';
    fields: Array<{
      name: string;
      label: string;
      type: string;
      required: boolean;
      description?: string;
      defaultValue?: string;
    }>;
    created_at: string;
    updated_at: string;
  }> => {
    const response = await api.get(`/target-schemas/${id}`);
    return response.data;
  },

  create: async (schema: {
    id: string;
    name: string;
    label: string;
    description: string;
    target_system: 'ops' | 'crm';
    fields: Array<{
      name: string;
      label: string;
      type: string;
      required: boolean;
      description?: string;
      defaultValue?: string;
    }>;
  }): Promise<{
    id: string;
    name: string;
    label: string;
    description: string;
    target_system: 'ops' | 'crm';
    fields: Array<{
      name: string;
      label: string;
      type: string;
      required: boolean;
      description?: string;
      defaultValue?: string;
    }>;
    created_at: string;
    updated_at: string;
  }> => {
    const response = await api.post('/target-schemas/', schema);
    return response.data;
  },

  update: async (
    id: string,
    schema: {
      name?: string;
      label?: string;
      description?: string;
      target_system?: 'ops' | 'crm';
      fields?: Array<{
        name: string;
        label: string;
        type: string;
        required: boolean;
        description?: string;
        defaultValue?: string;
      }>;
    }
  ): Promise<{
    id: string;
    name: string;
    label: string;
    description: string;
    target_system: 'ops' | 'crm';
    fields: Array<{
      name: string;
      label: string;
      type: string;
      required: boolean;
      description?: string;
      defaultValue?: string;
    }>;
    created_at: string;
    updated_at: string;
  }> => {
    const response = await api.put(`/target-schemas/${id}`, schema);
    return response.data;
  },

  delete: async (id: string): Promise<{ message: string }> => {
    const response = await api.delete(`/target-schemas/${id}`);
    return response.data;
  },

  getFields: async (
    id: string
  ): Promise<{
    schema_id: string;
    schema_name: string;
    fields: Array<{
      name: string;
      label: string;
      type: string;
      required: boolean;
      description?: string;
      defaultValue?: string;
    }>;
  }> => {
    const response = await api.get(`/target-schemas/${id}/fields`);
    return response.data;
  },
};

// Salesforce Bearer Token API functions (temporary endpoints)
export const salesforceBearerApi = {
  testConnection: async (): Promise<{
    status: string;
    message: string;
    authenticated: boolean;
  }> => {
    const response = await apiClientWithBearer.get('/migration/salesforce-bearer/test-connection');
    return response.data;
  },

  getObjects: async (): Promise<{
    objects: Array<{
      name: string;
      label: string;
      labelPlural: string;
      queryable: boolean;
      createable: boolean;
      updateable: boolean;
      deletable: boolean;
      custom: boolean;
      keyPrefix: string;
    }>;
    message: string;
  }> => {
    const response = await apiClientWithBearer.get('/migration/salesforce-bearer/objects');
    return response.data;
  },

  getData: async (
    objectName: string,
    limit: number = 100
  ): Promise<{
    object_name: string;
    records: Array<Record<string, string | number | boolean | null>>;
    total_count: number;
    message: string;
  }> => {
    const response = await apiClientWithBearer.get(
      `/migration/salesforce-bearer/data/${objectName}?limit=${limit}`
    );
    return response.data;
  },
};

// Health check
export const healthApi = {
  checkHealth: async (): Promise<{
    status: string;
    database: string;
    version: string;
  }> => {
    const response = await api.get('/health');
    return response.data;
  },
};

// Load API
export const loadApi = {
  loadStagedData: async (
    migrationJobId: string,
    request: {
      target_system: string;
      object_type: string;
      operation?: string;
      config?: Record<string, any>;
    }
  ): Promise<{
    success: boolean;
    total_records: number;
    successful_records: number;
    failed_records: number;
    errors: string[];
    message: string;
  }> => {
    const response = await api.post(`/load/staged/${migrationJobId}`, {
      migration_job_id: migrationJobId,
      ...request,
    });
    return response.data;
  },

  bulkLoadData: async (request: {
    target_system: string;
    object_type: string;
    records: Array<Record<string, any>>;
    operation?: string;
    config?: Record<string, any>;
  }): Promise<{
    success: boolean;
    total_records: number;
    successful_records: number;
    failed_records: number;
    errors: string[];
    message: string;
  }> => {
    const response = await api.post('/load/bulk', request);
    return response.data;
  },

  testConnection: async (
    targetSystem: string,
    config?: Record<string, any>
  ): Promise<{
    success: boolean;
    message: string;
  }> => {
    const response = await api.post('/load/test-connection', config || {}, {
      params: { target_system: targetSystem },
    });
    return response.data;
  },
};

export default api;
