/**
 * ETL Pipeline API client
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api/v1';

export interface ETLPipeline {
  id: number;
  name: string;
  description?: string;
  source_type: string;
  source_object?: string;
  source_fields?: string[];
  destination_type: string;
  destination_table?: string;
  destination_fields?: Record<string, string>;
  transformation_config?: Record<string, any>;
  schedule_config?: Record<string, any>;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ETLPipelineRun {
  id: number;
  pipeline_id: number;
  status: string;
  started_at: string;
  completed_at?: string;
  records_extracted?: number;
  records_transformed?: number;
  records_loaded?: number;
  error_message?: string;
  logs?: Array<{
    timestamp: string;
    level: string;
    stage: string;
    message: string;
    details?: Record<string, any>;
  }>;
  raw_data_path?: string;
  processed_data_path?: string;
  duration_seconds?: number;
}

export interface ETLPipelineWithRuns extends ETLPipeline {
  recent_runs: ETLPipelineRun[];
  last_run?: ETLPipelineRun;
  total_runs: number;
}

export interface PipelineStats {
  total_pipelines: number;
  active_pipelines: number;
  total_runs: number;
  successful_runs: number;
  failed_runs: number;
  running_pipelines: number;
}

export interface Destination {
  id: number;
  country?: string;
  name?: string;
  created_at: string;
  updated_at: string;
}

export interface CreatePipelineRequest {
  name: string;
  description?: string;
  source_type: string;
  source_object?: string;
  source_fields?: string[];
  destination_type: string;
  destination_table?: string;
  destination_fields?: Record<string, string>;
  transformation_config?: Record<string, any>;
  schedule_config?: Record<string, any>;
  is_active?: boolean;
}

export interface UpdatePipelineRequest {
  name?: string;
  description?: string;
  source_type?: string;
  source_object?: string;
  source_fields?: string[];
  destination_type?: string;
  destination_table?: string;
  destination_fields?: Record<string, string>;
  transformation_config?: Record<string, any>;
  schedule_config?: Record<string, any>;
  is_active?: boolean;
}

class PipelineAPIClient {
  private baseUrl: string;

  constructor() {
    this.baseUrl = `${API_BASE_URL}/pipelines`;
  }

  async listPipelines(includeInactive = false): Promise<ETLPipelineWithRuns[]> {
    const params = new URLSearchParams();
    if (includeInactive) {
      params.append('include_inactive', 'true');
    }

    const response = await fetch(`${this.baseUrl}/?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch pipelines: ${response.statusText}`);
    }
    return response.json();
  }

  async getPipeline(id: number): Promise<ETLPipelineWithRuns> {
    const response = await fetch(`${this.baseUrl}/${id}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch pipeline: ${response.statusText}`);
    }
    return response.json();
  }

  async createPipeline(pipeline: CreatePipelineRequest): Promise<ETLPipeline> {
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(pipeline),
    });

    if (!response.ok) {
      throw new Error(`Failed to create pipeline: ${response.statusText}`);
    }
    return response.json();
  }

  async updatePipeline(id: number, updates: UpdatePipelineRequest): Promise<ETLPipeline> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    });

    if (!response.ok) {
      throw new Error(`Failed to update pipeline: ${response.statusText}`);
    }
    return response.json();
  }

  async deletePipeline(id: number): Promise<void> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      throw new Error(`Failed to delete pipeline: ${response.statusText}`);
    }
  }

  async executePipeline(id: number, forceRun = false): Promise<{ run_id: number; pipeline_id: number; status: string; message: string }> {
    const params = new URLSearchParams();
    if (forceRun) {
      params.append('force_run', 'true');
    }

    const response = await fetch(`${this.baseUrl}/${id}/execute?${params}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      let errorMessage = `Failed to execute pipeline: ${response.statusText}`;
      try {
        const errorData = await response.json();
        if (errorData.detail) {
          errorMessage = `Failed to execute pipeline: ${errorData.detail}`;
        }
      } catch {
        // If we can't parse the error response, use the default message
      }
      throw new Error(errorMessage);
    }
    return response.json();
  }

  async getPipelineRuns(id: number, skip = 0, limit = 50): Promise<ETLPipelineRun[]> {
    const params = new URLSearchParams({
      skip: skip.toString(),
      limit: limit.toString(),
    });

    const response = await fetch(`${this.baseUrl}/${id}/runs?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch pipeline runs: ${response.statusText}`);
    }
    return response.json();
  }

  async getStats(): Promise<PipelineStats> {
    const response = await fetch(`${this.baseUrl}/stats/overview`);
    if (!response.ok) {
      throw new Error(`Failed to fetch pipeline stats: ${response.statusText}`);
    }
    return response.json();
  }

  async getdestination(skip = 0, limit = 100): Promise<Destination[]> {
    const params = new URLSearchParams({
      skip: skip.toString(),
      limit: limit.toString(),
    });

    const response = await fetch(`${this.baseUrl}/destination/?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch destination: ${response.statusText}`);
    }
    return response.json();
  }

  async getRunDetails(pipelineId: number, runId: number): Promise<{
    raw_data: any[];
    processed_data: any[];
    destination_data: any[];
    run: ETLPipelineRun;
  }> {
    const response = await fetch(`${this.baseUrl}/${pipelineId}/runs/${runId}/details`);
    if (!response.ok) {
      throw new Error('Failed to fetch run details');
    }
    return response.json();
  }
}

export const pipelineApi = new PipelineAPIClient();
