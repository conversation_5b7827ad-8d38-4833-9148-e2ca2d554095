/**
 * Excel export utilities for data export functionality
 */

export interface ExcelExportOptions {
  filename?: string;
  sheetName?: string;
  includeHeaders?: boolean;
  format?: 'csv' | 'excel';
}

/**
 * Export data to CSV format (compatible with Excel)
 * Creates a proper CSV file that Excel can open without issues
 */
export function exportToExcel(
  data: Array<Record<string, any>>,
  selectedFields: string[],
  options: ExcelExportOptions = {}
) {
  const {
    filename = 'export.csv',
    includeHeaders = true,
    format = 'csv',
  } = options;

  if (!data || data.length === 0) {
    throw new Error('No data to export');
  }

  // Create CSV content
  let csvContent = '';

  // Add headers if requested
  if (includeHeaders) {
    csvContent += selectedFields.map(field => `"${field}"`).join(',') + '\n';
  }

  // Add data rows
  data.forEach(row => {
    const values = selectedFields.map(field => {
      const value = row[field];
      if (value === null || value === undefined) {
        return '""';
      }
      // Escape quotes and wrap in quotes
      const stringValue = String(value).replace(/"/g, '""');
      return `"${stringValue}"`;
    });
    csvContent += values.join(',') + '\n';
  });

  // Add BOM for proper UTF-8 encoding in Excel
  const BOM = '\uFEFF';
  const csvWithBOM = BOM + csvContent;

  // Create and trigger download
  const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
}

/**
 * Format filename with timestamp
 */
export function generateExcelFilename(objectName: string, prefix = 'export'): string {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');
  return `${prefix}_${objectName}_${timestamp}.csv`;
}

/**
 * Export data to actual Excel format using xlsx library
 */
export async function exportToActualExcel(
  data: Array<Record<string, any>>,
  selectedFields: string[],
  options: ExcelExportOptions = {}
) {
  const {
    filename = 'export.xlsx',
    sheetName = 'Data',
    includeHeaders = true,
  } = options;

  if (!data || data.length === 0) {
    throw new Error('No data to export');
  }

  try {
    // Import xlsx library
    const XLSX = await import('xlsx');
    console.log('XLSX library loaded successfully');

    // Prepare data for Excel - use simple array format first
    const worksheetData: any[][] = [];

    // Add headers if requested
    if (includeHeaders) {
      worksheetData.push(selectedFields);
    }

    // Add data rows - keep it simple for now
    data.forEach(row => {
      const rowData = selectedFields.map(field => {
        const value = row[field];
        // Keep values simple for debugging
        if (value === null || value === undefined) {
          return '';
        }
        return String(value);
      });
      worksheetData.push(rowData);
    });

    console.log('Worksheet data prepared:', worksheetData.length, 'rows');

    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

    console.log('Worksheet created');

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

    console.log('Worksheet added to workbook');

    // Generate Excel file buffer
    const excelBuffer = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array'
    });

    console.log('Excel buffer generated, size:', excelBuffer.byteLength);

    // Create blob and trigger download
    const blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });

    console.log('Blob created, size:', blob.size);

    const link = document.createElement('a');
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      console.log('Download triggered for:', filename);
    }

  } catch (error) {
    console.error('Excel export failed:', error);
    // Fallback to CSV export
    const csvFilename = filename.replace('.xlsx', '.csv');
    exportToExcel(data, selectedFields, { ...options, filename: csvFilename });
    throw new Error(`Excel export failed, exported as CSV instead: ${csvFilename}`);
  }
}

/**
 * Generate Excel filename with .xlsx extension
 */
export function generateActualExcelFilename(objectName: string, prefix = 'export'): string {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');
  return `${prefix}_${objectName}_${timestamp}.xlsx`;
}

/**
 * Simple Excel export using HTML table format
 * This creates an Excel-compatible file using HTML table markup
 */
export function exportToSimpleExcel(
  data: Array<Record<string, any>>,
  selectedFields: string[],
  options: ExcelExportOptions = {}
) {
  const {
    filename = 'export.xlsx',
    includeHeaders = true,
  } = options;

  if (!data || data.length === 0) {
    throw new Error('No data to export');
  }

  // Create HTML table content
  let htmlContent = `
    <html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
    <head>
      <meta charset="utf-8">
      <!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>Data</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]-->
    </head>
    <body>
      <table border="1">
  `;

  // Add headers
  if (includeHeaders) {
    htmlContent += '<tr>';
    selectedFields.forEach(field => {
      htmlContent += `<th>${field}</th>`;
    });
    htmlContent += '</tr>';
  }

  // Add data rows
  data.forEach(row => {
    htmlContent += '<tr>';
    selectedFields.forEach(field => {
      const value = row[field];
      let cellValue = '';

      if (value === null || value === undefined) {
        cellValue = '';
      } else if (typeof value === 'number') {
        cellValue = String(value);
      } else if (typeof value === 'boolean') {
        cellValue = value ? 'TRUE' : 'FALSE';
      } else {
        cellValue = String(value).replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
      }

      htmlContent += `<td>${cellValue}</td>`;
    });
    htmlContent += '</tr>';
  });

  htmlContent += `
      </table>
    </body>
    </html>
  `;

  // Create blob and download
  const blob = new Blob([htmlContent], {
    type: 'application/vnd.ms-excel'
  });

  const link = document.createElement('a');
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
}
