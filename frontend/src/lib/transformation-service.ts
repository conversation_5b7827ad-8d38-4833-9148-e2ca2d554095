/**
 * Transformation service for applying field mappings to data
 */

interface FieldMapping {
  sourceField: string;
  targetField: string;
  transformation?: {
    function: string;
    parameters?: Record<string, any>;
  };
  defaultValue?: string | number | boolean;
  required: boolean;
}

interface TransformationOptions {
  mappings: FieldMapping[];
  removeUnmappedFields: boolean;
}

class TransformationService {
  /**
   * Apply field mappings to transform data
   */
  transformData(data: Record<string, any>[], options: TransformationOptions): Record<string, any>[] {
    // Validate mappings first
    const validation = this.validateMappings(options.mappings);
    if (!validation.valid) {
      throw new Error(`Invalid field mappings: ${validation.errors.join(', ')}`);
    }

    // Transform each record
    const transformedData = data.map((record, index) => {
      try {
        return this.transformRecord(record, options);
      } catch (error) {
        throw new Error(`Error transforming record ${index + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    });

    return transformedData;
  }

  /**
   * Transform a single record
   */
  private transformRecord(record: Record<string, any>, options: TransformationOptions): Record<string, any> {
    const { mappings, removeUnmappedFields } = options;
    const transformedRecord: Record<string, any> = {};

    // Apply field mappings
    for (const mapping of mappings) {
      const sourceValue = record[mapping.sourceField];
      let transformedValue = sourceValue;

      // Apply transformation if specified
      if (mapping.transformation) {
        try {
          transformedValue = this.applyTransformation(sourceValue, mapping.transformation);
        } catch (error) {
          throw new Error(`Transformation failed for field ${mapping.sourceField}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      // Use default value if source value is null/undefined
      if (transformedValue == null && mapping.defaultValue != null) {
        transformedValue = mapping.defaultValue;
      }

      // Convert null values to appropriate defaults based on field type
      if (transformedValue == null) {
        // For string/text fields, use empty string instead of null
        // This prevents API validation errors that expect non-null strings
        transformedValue = '';
      }

      // Check required fields
      if (mapping.required && (transformedValue == null || transformedValue === '')) {
        throw new Error(`Required field ${mapping.targetField} is missing or empty`);
      }

      // Set the transformed value
      transformedRecord[mapping.targetField] = transformedValue;
    }

    // Include unmapped fields if removeUnmappedFields is false
    if (!removeUnmappedFields) {
      const mappedSourceFields = new Set(mappings.map(m => m.sourceField));
      for (const [key, value] of Object.entries(record)) {
        if (!mappedSourceFields.has(key)) {
          // Convert null values to empty strings for unmapped fields too
          transformedRecord[key] = value == null ? '' : value;
        }
      }
    }

    return transformedRecord;
  }

  /**
   * Apply a transformation function to a value
   */
  private applyTransformation(value: any, transformation: { function: string; parameters?: Record<string, any> }): any {
    if (value == null) return value;

    switch (transformation.function) {
      case 'uppercase':
        return String(value).toUpperCase();
      
      case 'lowercase':
        return String(value).toLowerCase();
      
      case 'strip':
        return String(value).trim();
      
      case 'to_string':
        return String(value);
      
      case 'to_int':
        return this.safeIntConvert(value);
      
      case 'to_float':
        return this.safeFloatConvert(value);
      
      case 'to_bool':
        return this.safeBoolConvert(value);
      
      case 'format_phone':
        return this.formatPhone(value);
      
      case 'format_email':
        return this.formatEmail(value);
      
      case 'parse_date':
        return this.parseDate(value);
      
      case 'format_currency':
        return this.formatCurrency(value);
      
      default:
        console.warn(`Unknown transformation function: ${transformation.function}`);
        return value;
    }
  }

  /**
   * Safely convert value to integer
   */
  private safeIntConvert(value: any): number | null {
    if (typeof value === 'number') return Math.floor(value);
    if (typeof value === 'string') {
      const parsed = parseInt(value, 10);
      return isNaN(parsed) ? null : parsed;
    }
    return null;
  }

  /**
   * Safely convert value to float
   */
  private safeFloatConvert(value: any): number | null {
    if (typeof value === 'number') return value;
    if (typeof value === 'string') {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? null : parsed;
    }
    return null;
  }

  /**
   * Safely convert value to boolean
   */
  private safeBoolConvert(value: any): boolean {
    if (typeof value === 'boolean') return value;
    if (typeof value === 'string') {
      const lower = value.toLowerCase();
      return lower === 'true' || lower === 'yes' || lower === '1';
    }
    if (typeof value === 'number') return value !== 0;
    return false;
  }

  /**
   * Format phone number
   */
  private formatPhone(value: any): string {
    const phone = String(value).replace(/\D/g, '');
    if (phone.length === 10) {
      return `(${phone.slice(0, 3)}) ${phone.slice(3, 6)}-${phone.slice(6)}`;
    }
    if (phone.length === 11 && phone.startsWith('1')) {
      return `+1 (${phone.slice(1, 4)}) ${phone.slice(4, 7)}-${phone.slice(7)}`;
    }
    return String(value); // Return original if can't format
  }

  /**
   * Format email (basic validation and lowercase)
   */
  private formatEmail(value: any): string {
    const email = String(value).toLowerCase().trim();
    // Basic email validation
    if (email.includes('@') && email.includes('.')) {
      return email;
    }
    return String(value); // Return original if invalid
  }

  /**
   * Parse date string
   */
  private parseDate(value: any): string | null {
    try {
      const date = new Date(value);
      if (isNaN(date.getTime())) return null;
      return date.toISOString().split('T')[0]; // Return YYYY-MM-DD format
    } catch {
      return null;
    }
  }

  /**
   * Format currency
   */
  private formatCurrency(value: any): string {
    const num = this.safeFloatConvert(value);
    if (num === null) return String(value);
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(num);
  }

  /**
   * Validate field mappings
   */
  validateMappings(mappings: FieldMapping[]): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    const targetFields = new Set<string>();

    for (const mapping of mappings) {
      // Check for duplicate target fields
      if (targetFields.has(mapping.targetField)) {
        errors.push(`Duplicate target field: ${mapping.targetField}`);
      }
      targetFields.add(mapping.targetField);

      // Check for empty source or target fields
      if (!mapping.sourceField.trim()) {
        errors.push('Source field cannot be empty');
      }
      if (!mapping.targetField.trim()) {
        errors.push('Target field cannot be empty');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Analyze unmapped fields
   */
  analyzeUnmappedFields(
    originalData: Record<string, any>[],
    mappings: FieldMapping[]
  ): {
    unmappedFields: string[];
    fieldUsage: Record<string, { count: number; sampleValues: any[] }>;
  } {
    if (originalData.length === 0) {
      return { unmappedFields: [], fieldUsage: {} };
    }

    const mappedSourceFields = new Set(mappings.map(m => m.sourceField));
    const allFields = Object.keys(originalData[0]);
    const unmappedFields = allFields.filter(field => !mappedSourceFields.has(field));

    // Analyze field usage
    const fieldUsage: Record<string, { count: number; sampleValues: any[] }> = {};

    for (const field of unmappedFields) {
      const values = originalData
        .map(record => record[field])
        .filter(value => value != null && value !== '');

      const uniqueValues = [...new Set(values)];

      fieldUsage[field] = {
        count: values.length,
        sampleValues: uniqueValues.slice(0, 3) // First 3 unique values as samples
      };
    }

    return { unmappedFields, fieldUsage };
  }

  /**
   * Get transformation summary
   */
  getTransformationSummary(
    originalData: Record<string, any>[],
    transformedData: Record<string, any>[],
    options: TransformationOptions
  ): {
    totalRecords: number;
    transformedRecords: number;
    fieldsMapped: number;
    fieldsRemoved: number;
    unmappedFieldsAnalysis: {
      unmappedFields: string[];
      fieldUsage: Record<string, { count: number; sampleValues: any[] }>;
    };
  } {
    const originalFieldCount = originalData.length > 0 ? Object.keys(originalData[0]).length : 0;
    const transformedFieldCount = transformedData.length > 0 ? Object.keys(transformedData[0]).length : 0;
    const unmappedAnalysis = this.analyzeUnmappedFields(originalData, options.mappings);

    return {
      totalRecords: originalData.length,
      transformedRecords: transformedData.length,
      fieldsMapped: options.mappings.length,
      fieldsRemoved: options.removeUnmappedFields ? originalFieldCount - transformedFieldCount : 0,
      unmappedFieldsAnalysis: unmappedAnalysis
    };
  }
}

export const transformationService = new TransformationService();
export type { FieldMapping, TransformationOptions };
