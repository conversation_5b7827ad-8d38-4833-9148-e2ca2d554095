'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Download, 
  Database, 
  Shuffle, 
  Upload, 
  CheckCircle, 
  XCircle, 
  Clock,
  RefreshCw
} from 'lucide-react';

export interface PipelineStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  icon: React.ReactNode;
  progress?: number;
  message?: string;
}

interface PipelineProgressIndicatorProps {
  pipelineName: string;
  steps: PipelineStep[];
  overallProgress: number;
  isVisible: boolean;
  onClose?: () => void;
}

export function PipelineProgressIndicator({
  pipelineName,
  steps,
  overallProgress,
  isVisible,
  onClose
}: PipelineProgressIndicatorProps) {
  if (!isVisible) return null;

  const getStepIcon = (step: PipelineStep) => {
    switch (step.status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'running':
        return <RefreshCw className="w-5 h-5 text-blue-500 animate-spin" />;
      default:
        return <Clock className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStepBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800">Completed</Badge>;
      case 'failed':
        return <Badge variant="destructive">Failed</Badge>;
      case 'running':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">Running</Badge>;
      default:
        return <Badge variant="secondary">Pending</Badge>;
    }
  };

  const isCompleted = steps.every(step => step.status === 'completed');
  const hasFailed = steps.some(step => step.status === 'failed');

  return (
    <Card className="fixed bottom-4 right-4 w-96 shadow-lg border-2 z-50 bg-white">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center">
            <Database className="w-5 h-5 mr-2 text-blue-500" />
            Pipeline Execution
          </CardTitle>
          {(isCompleted || hasFailed) && onClose && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-xl leading-none"
            >
              ×
            </button>
          )}
        </div>
        <p className="text-sm text-gray-600">{pipelineName}</p>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Overall Progress */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium">Overall Progress</span>
            <span className="text-sm text-gray-600">{Math.round(overallProgress)}%</span>
          </div>
          <Progress value={overallProgress} className="h-2" />
        </div>

        {/* Step Progress */}
        <div className="space-y-3">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-start space-x-3">
              <div className="flex-shrink-0 mt-0.5">
                {getStepIcon(step)}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <p className="text-sm font-medium text-gray-900">{step.name}</p>
                  {getStepBadge(step.status)}
                </div>
                
                <p className="text-xs text-gray-600 mb-2">{step.description}</p>
                
                {step.status === 'running' && step.progress !== undefined && (
                  <div className="mb-2">
                    <Progress value={step.progress} className="h-1" />
                  </div>
                )}
                
                {step.message && (
                  <p className="text-xs text-gray-500 italic">{step.message}</p>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Status Summary */}
        {isCompleted && (
          <div className="bg-green-50 border border-green-200 p-3 rounded-lg">
            <div className="flex items-center">
              <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
              <p className="text-sm font-medium text-green-800">Pipeline completed successfully!</p>
            </div>
          </div>
        )}

        {hasFailed && (
          <div className="bg-red-50 border border-red-200 p-3 rounded-lg">
            <div className="flex items-center">
              <XCircle className="w-4 h-4 text-red-600 mr-2" />
              <p className="text-sm font-medium text-red-800">Pipeline execution failed.</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Default pipeline steps for ETL process
export const createDefaultPipelineSteps = (): PipelineStep[] => [
  {
    id: 'extract',
    name: 'Extract Data',
    description: 'Extracting data from Salesforce',
    status: 'pending',
    icon: <Download className="w-5 h-5" />
  },
  {
    id: 'store-raw',
    name: 'Store Raw Data',
    description: 'Storing raw data in MinIO',
    status: 'pending',
    icon: <Database className="w-5 h-5" />
  },
  {
    id: 'transform',
    name: 'Transform Data',
    description: 'Applying transformation rules',
    status: 'pending',
    icon: <Shuffle className="w-5 h-5" />
  },
  {
    id: 'store-processed',
    name: 'Store Processed Data',
    description: 'Storing processed data in MinIO',
    status: 'pending',
    icon: <Database className="w-5 h-5" />
  },
  {
    id: 'load',
    name: 'Load to Database',
    description: 'Loading data into PostgreSQL',
    status: 'pending',
    icon: <Upload className="w-5 h-5" />
  }
];
