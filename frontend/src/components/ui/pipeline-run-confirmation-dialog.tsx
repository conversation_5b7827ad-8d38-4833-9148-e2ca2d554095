'use client';

import React from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Play, 
  Database, 
  ArrowRight, 
  Clock, 
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { ETLPipelineWithRuns } from '@/lib/pipeline-api';

interface PipelineRunConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  pipeline: ETLPipelineWithRuns | null;
  onConfirm: () => void;
  isExecuting: boolean;
}

export function PipelineRunConfirmationDialog({
  open,
  onOpenChange,
  pipeline,
  onConfirm,
  isExecuting
}: PipelineRunConfirmationDialogProps) {
  if (!pipeline) return null;

  const handleConfirm = () => {
    onConfirm();
    onOpenChange(false);
  };

  const getLastRunStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'running':
        return <Clock className="w-4 h-4 text-blue-500" />;
      default:
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
    }
  };

  const getLastRunStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800">Completed</Badge>;
      case 'failed':
        return <Badge variant="destructive">Failed</Badge>;
      case 'running':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">Running</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Play className="w-5 h-5 mr-2 text-blue-500" />
            Confirm Pipeline Execution
          </DialogTitle>
          <DialogDescription>
            Are you sure you want to run this pipeline? This will extract data from the source and load it into the destination.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Pipeline Details */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-semibold text-sm mb-3">Pipeline Details</h4>
            
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Name:</span>
                <span className="text-sm font-medium">{pipeline.name}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Description:</span>
                <span className="text-sm text-right max-w-[250px]">{pipeline.description}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Data Flow:</span>
                <div className="flex items-center text-sm">
                  <Database className="w-3 h-3 mr-1" />
                  <span>{pipeline.source_type}: {pipeline.source_object}</span>
                  <ArrowRight className="w-3 h-3 mx-2 text-gray-400" />
                  <Database className="w-3 h-3 mr-1" />
                  <span>{pipeline.destination_type}: {pipeline.destination_table}</span>
                </div>
              </div>
              
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Total Runs:</span>
                <span className="text-sm font-medium">{pipeline.total_runs}</span>
              </div>
            </div>
          </div>

          {/* Last Run Information */}
          {pipeline.last_run && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-semibold text-sm mb-3">Last Run Information</h4>
              
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Status:</span>
                  <div className="flex items-center">
                    {getLastRunStatusIcon(pipeline.last_run.status)}
                    <span className="ml-1">{getLastRunStatusBadge(pipeline.last_run.status)}</span>
                  </div>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Started:</span>
                  <span className="text-sm">{new Date(pipeline.last_run.started_at).toLocaleString()}</span>
                </div>
                
                {pipeline.last_run.records_extracted !== null && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Records Processed:</span>
                    <span className="text-sm">
                      {pipeline.last_run.records_extracted || 0} extracted → {pipeline.last_run.records_transformed || 0} transformed → {pipeline.last_run.records_loaded || 0} loaded
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Warning for failed last run */}
          {pipeline.last_run?.status === 'failed' && (
            <div className="bg-yellow-50 border border-yellow-200 p-3 rounded-lg">
              <div className="flex items-start">
                <AlertTriangle className="w-4 h-4 text-yellow-600 mt-0.5 mr-2" />
                <div>
                  <p className="text-sm font-medium text-yellow-800">Previous Run Failed</p>
                  <p className="text-sm text-yellow-700 mt-1">
                    The last execution of this pipeline failed. Please review the error details before running again.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isExecuting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={isExecuting}
          >
            {isExecuting ? (
              <>
                <Clock className="w-4 h-4 mr-2 animate-spin" />
                Starting...
              </>
            ) : (
              <>
                <Play className="w-4 h-4 mr-2" />
                Run Pipeline
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
