'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';
import { Check } from 'lucide-react';

interface StepperProps {
  steps: Array<{
    id: string;
    title: string;
    description?: string;
  }>;
  currentStep: string;
  className?: string;
}

export function Stepper({ steps, currentStep, className }: StepperProps) {
  const currentIndex = steps.findIndex(step => step.id === currentStep);

  return (
    <div className={cn('w-full', className)}>
      <nav aria-label="Progress">
        <ol className="flex items-center">
          {steps.map((step, index) => {
            const isCompleted = index < currentIndex;
            const isCurrent = index === currentIndex;
            const isUpcoming = index > currentIndex;

            return (
              <li key={step.id} className={cn('relative', index !== steps.length - 1 && 'flex-1')}>
                <div className="flex items-center">
                  {/* Step Circle */}
                  <div
                    className={cn(
                      'flex h-10 w-10 items-center justify-center rounded-full border-2 transition-colors',
                      {
                        'border-blue-600 bg-blue-600 text-white': isCompleted,
                        'border-blue-600 bg-white text-blue-600': isCurrent,
                        'border-slate-300 bg-white text-slate-400': isUpcoming,
                      }
                    )}
                  >
                    {isCompleted ? (
                      <Check className="h-5 w-5" />
                    ) : (
                      <span className="text-sm font-medium">{index + 1}</span>
                    )}
                  </div>

                  {/* Step Content */}
                  <div className="ml-4 min-w-0 flex-1">
                    <p
                      className={cn('text-sm font-medium', {
                        'text-blue-600': isCurrent,
                        'text-slate-900': isCompleted,
                        'text-slate-500': isUpcoming,
                      })}
                    >
                      {step.title}
                    </p>
                    {step.description && (
                      <p
                        className={cn('text-sm', {
                          'text-blue-500': isCurrent,
                          'text-slate-500': isCompleted,
                          'text-slate-400': isUpcoming,
                        })}
                      >
                        {step.description}
                      </p>
                    )}
                  </div>
                </div>

                {/* Connector Line */}
                {index !== steps.length - 1 && (
                  <div
                    className={cn(
                      'absolute top-5 left-10 h-0.5 w-full transition-colors',
                      {
                        'bg-blue-600': index < currentIndex,
                        'bg-slate-300': index >= currentIndex,
                      }
                    )}
                  />
                )}
              </li>
            );
          })}
        </ol>
      </nav>
    </div>
  );
}
