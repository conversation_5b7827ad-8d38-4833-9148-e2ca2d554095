'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ArrowRight,
  Plus,
  Trash2,
  Settings,
  Target,
  Database,
  AlertTriangle,
  CheckCircle,
  X,
} from 'lucide-react';

interface SalesforceField {
  name: string;
  label: string;
  type: string;
  required: boolean;
  custom: boolean;
}

interface TargetField {
  name: string;
  label: string;
  type: string;
  required: boolean;
  description?: string;
}

interface TargetSchema {
  id: string;
  name: string;
  label: string;
  target_system: 'ops' | 'crm';
  fields: TargetField[];
}

interface FieldMapping {
  sourceField: string;
  targetField: string;
  transformation?: {
    function: string;
    parameters?: Record<string, any>;
  };
  defaultValue?: string | number | boolean;
  required: boolean;
}

interface FieldMappingInterfaceProps {
  salesforceFields: SalesforceField[];
  targetSchemas: TargetSchema[];
  selectedTargetSchema: string | null;
  onTargetSchemaChange: (schemaId: string) => void;
  mappings: FieldMapping[];
  onMappingsChange: (mappings: FieldMapping[]) => void;
  removeUnmappedFields: boolean;
  onRemoveUnmappedFieldsChange: (remove: boolean) => void;
}

const TRANSFORMATION_FUNCTIONS = [
  { value: 'none', label: 'No Transformation' },
  { value: 'uppercase', label: 'Convert to Uppercase' },
  { value: 'lowercase', label: 'Convert to Lowercase' },
  { value: 'strip', label: 'Trim Whitespace' },
  { value: 'to_string', label: 'Convert to String' },
  { value: 'to_int', label: 'Convert to Integer' },
  { value: 'to_float', label: 'Convert to Float' },
  { value: 'to_bool', label: 'Convert to Boolean' },
  { value: 'format_phone', label: 'Format Phone Number' },
  { value: 'format_email', label: 'Format Email' },
  { value: 'parse_date', label: 'Parse Date' },
  { value: 'format_currency', label: 'Format Currency' },
];

export function FieldMappingInterface({
  salesforceFields,
  targetSchemas,
  selectedTargetSchema,
  onTargetSchemaChange,
  mappings,
  onMappingsChange,
  removeUnmappedFields,
  onRemoveUnmappedFieldsChange,
}: FieldMappingInterfaceProps) {
  const [newMapping, setNewMapping] = useState<Partial<FieldMapping>>({});
  const [showAddMapping, setShowAddMapping] = useState(false);

  const selectedSchema = targetSchemas.find(s => s.id === selectedTargetSchema);
  const mappedSourceFields = new Set(mappings.map(m => m.sourceField));
  const mappedTargetFields = new Set(mappings.map(m => m.targetField));
  const unmappedSourceFields = salesforceFields.filter(f => !mappedSourceFields.has(f.name));
  const unmappedTargetFields = selectedSchema?.fields.filter(f => !mappedTargetFields.has(f.name)) || [];

  const addMapping = () => {
    if (newMapping.sourceField && newMapping.targetField) {
      const mapping: FieldMapping = {
        sourceField: newMapping.sourceField,
        targetField: newMapping.targetField,
        transformation: newMapping.transformation?.function !== 'none' ? newMapping.transformation : undefined,
        defaultValue: newMapping.defaultValue,
        required: newMapping.required || false,
      };
      
      onMappingsChange([...mappings, mapping]);
      setNewMapping({});
      setShowAddMapping(false);
    }
  };

  const removeMapping = (index: number) => {
    const updatedMappings = mappings.filter((_, i) => i !== index);
    onMappingsChange(updatedMappings);
  };

  const updateMapping = (index: number, updates: Partial<FieldMapping>) => {
    const updatedMappings = mappings.map((mapping, i) => 
      i === index ? { ...mapping, ...updates } : mapping
    );
    onMappingsChange(updatedMappings);
  };

  const getFieldTypeColor = (type: string) => {
    const colors = {
      string: 'bg-blue-100 text-blue-800',
      number: 'bg-green-100 text-green-800',
      boolean: 'bg-purple-100 text-purple-800',
      date: 'bg-orange-100 text-orange-800',
      email: 'bg-pink-100 text-pink-800',
      url: 'bg-indigo-100 text-indigo-800',
      text: 'bg-gray-100 text-gray-800',
      json: 'bg-yellow-100 text-yellow-800',
    };
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      {/* Target Schema Selection */}
      <Card className="border-0 bg-white/80 shadow-xl backdrop-blur-sm">
        <CardHeader className="pb-6">
          <CardTitle className="flex items-center space-x-3 text-xl">
            <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-green-500 to-green-600">
              <Target className="h-5 w-5 text-white" />
            </div>
            <span className="text-slate-900">Target Schema Selection</span>
          </CardTitle>
          <CardDescription className="text-base text-slate-600">
            Choose the target schema for your data transformation
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <Label htmlFor="target-schema">Target Schema</Label>
              <Select value={selectedTargetSchema || ''} onValueChange={onTargetSchemaChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a target schema" />
                </SelectTrigger>
                <SelectContent>
                  {targetSchemas.map(schema => (
                    <SelectItem key={schema.id} value={schema.id}>
                      <div className="flex items-center space-x-2">
                        <span>{schema.label}</span>
                        <Badge variant="secondary" className={
                          schema.target_system === 'ops' 
                            ? 'bg-blue-100 text-blue-800' 
                            : 'bg-green-100 text-green-800'
                        }>
                          {schema.target_system === 'ops' ? 'Ops' : 'CRM'}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {selectedSchema && (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Selected:</strong> {selectedSchema.label} ({selectedSchema.fields.length} fields available)
                </AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Field Mappings */}
      {selectedSchema && (
        <Card className="border-0 bg-white/80 shadow-xl backdrop-blur-sm">
          <CardHeader className="pb-6">
            <CardTitle className="flex items-center space-x-3 text-xl">
              <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-purple-500 to-purple-600">
                <ArrowRight className="h-5 w-5 text-white" />
              </div>
              <span className="text-slate-900">Field Mappings</span>
            </CardTitle>
            <CardDescription className="text-base text-slate-600">
              Map Salesforce fields to target schema fields
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Existing Mappings */}
            {mappings.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-slate-900">Current Mappings</h3>
                {mappings.map((mapping, index) => (
                  <div key={index} className="rounded-lg border border-slate-200 bg-slate-50 p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                          <Badge className="bg-blue-100 text-blue-800">
                            {mapping.sourceField}
                          </Badge>
                          <ArrowRight className="h-4 w-4 text-slate-400" />
                          <Badge className="bg-green-100 text-green-800">
                            {mapping.targetField}
                          </Badge>
                        </div>
                        {mapping.transformation && (
                          <Badge variant="outline" className="text-purple-600">
                            {TRANSFORMATION_FUNCTIONS.find(f => f.value === mapping.transformation?.function)?.label}
                          </Badge>
                        )}
                        {mapping.required && (
                          <Badge variant="destructive">Required</Badge>
                        )}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeMapping(index)}
                        className="text-red-500 hover:bg-red-50 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Add New Mapping */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-slate-900">Add New Mapping</h3>
                <Button
                  onClick={() => setShowAddMapping(!showAddMapping)}
                  variant="outline"
                  size="sm"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Add Mapping
                </Button>
              </div>

              {showAddMapping && (
                <div className="rounded-lg border border-slate-200 bg-slate-50 p-4 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="source-field">Source Field (Salesforce)</Label>
                      <Select
                        value={newMapping.sourceField || ''}
                        onValueChange={(value) => setNewMapping({ ...newMapping, sourceField: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select source field" />
                        </SelectTrigger>
                        <SelectContent>
                          {unmappedSourceFields.map(field => (
                            <SelectItem key={field.name} value={field.name}>
                              <div className="flex items-center space-x-2">
                                <span>{field.label}</span>
                                <Badge variant="outline" className={getFieldTypeColor(field.type)}>
                                  {field.type}
                                </Badge>
                                {field.required && <Badge variant="destructive" className="text-xs">Required</Badge>}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="target-field">Target Field</Label>
                      <Select
                        value={newMapping.targetField || ''}
                        onValueChange={(value) => setNewMapping({ ...newMapping, targetField: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select target field" />
                        </SelectTrigger>
                        <SelectContent>
                          {unmappedTargetFields.map(field => (
                            <SelectItem key={field.name} value={field.name}>
                              <div className="flex items-center space-x-2">
                                <span>{field.label}</span>
                                <Badge variant="outline" className={getFieldTypeColor(field.type)}>
                                  {field.type}
                                </Badge>
                                {field.required && <Badge variant="destructive" className="text-xs">Required</Badge>}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="transformation">Transformation</Label>
                      <Select
                        value={newMapping.transformation?.function || 'none'}
                        onValueChange={(value) => 
                          setNewMapping({ 
                            ...newMapping, 
                            transformation: value === 'none' ? undefined : { function: value }
                          })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {TRANSFORMATION_FUNCTIONS.map(func => (
                            <SelectItem key={func.value} value={func.value}>
                              {func.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="default-value">Default Value (Optional)</Label>
                      <Input
                        id="default-value"
                        placeholder="Enter default value"
                        value={newMapping.defaultValue?.toString() || ''}
                        onChange={(e) => setNewMapping({ ...newMapping, defaultValue: e.target.value })}
                      />
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="required"
                        checked={newMapping.required || false}
                        onChange={(e) => setNewMapping({ ...newMapping, required: e.target.checked })}
                        className="rounded border-slate-300"
                      />
                      <Label htmlFor="required">Required mapping</Label>
                    </div>

                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        onClick={() => {
                          setNewMapping({});
                          setShowAddMapping(false);
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={addMapping}
                        disabled={!newMapping.sourceField || !newMapping.targetField}
                        className="bg-purple-600 hover:bg-purple-700"
                      >
                        Add Mapping
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Unmapped Fields Options */}
            <div className="rounded-lg border border-slate-200 bg-slate-50 p-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="remove-unmapped"
                  checked={removeUnmappedFields}
                  onChange={(e) => onRemoveUnmappedFieldsChange(e.target.checked)}
                  className="rounded border-slate-300"
                />
                <Label htmlFor="remove-unmapped">Remove unmapped fields from output</Label>
              </div>
              <p className="mt-2 text-sm text-slate-600">
                When enabled, fields without mappings will be excluded from the transformed data.
              </p>
            </div>

            {/* Summary */}
            <Alert>
              <Settings className="h-4 w-4" />
              <AlertDescription>
                <strong>Mapping Summary:</strong> {mappings.length} field mappings configured. 
                {unmappedSourceFields.length > 0 && (
                  <span className="text-orange-600">
                    {' '}{unmappedSourceFields.length} source fields remain unmapped.
                  </span>
                )}
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
