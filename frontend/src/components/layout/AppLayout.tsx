'use client';

import { ReactNode } from 'react';
import Sidebar from './Sidebar';

interface AppLayoutProps {
  children: ReactNode;
  sidebarCollapsed?: boolean;
}

export default function AppLayout({ children, sidebarCollapsed = false }: AppLayoutProps) {
  return (
    <div className='flex h-screen bg-slate-50'>
      {/* Sidebar */}
      <Sidebar className='flex-shrink-0' defaultCollapsed={sidebarCollapsed} />

      {/* Main Content */}
      <div className='flex flex-1 flex-col overflow-hidden'>
        {/* Content Area */}
        <main className='flex-1 overflow-auto'>{children}</main>
      </div>
    </div>
  );
}
