'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Database,
  Target,
  Home,
  Settings,
  _Menu,
  _X,
  ChevronLeft,
  ChevronRight,
  Layers,
  Briefcase,
  Wand2,
  GitBranch,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SidebarProps {
  className?: string;
  defaultCollapsed?: boolean;
}

export default function Sidebar({ className, defaultCollapsed = false }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);
  const pathname = usePathname();

  const navigationItems = [
    // {
    //   href: '/',
    //   label: 'Dashboard',
    //   icon: Home,
    //   description: 'Overview and stats',
    // },
    {
      href: '/wizard',
      label: 'Data Wizard',
      icon: Wand2,
      description: 'Extract data with wizard',
    },
    {
      href: '/pipelines',
      label: 'Data Pipeline',
      icon: GitBranch,
      description: 'ETL pipeline management',
    },
    {
      href: '/migration',
      label: 'Migration',
      icon: Database,
      description: 'Data migration workflow',
    },
    {
      href: '/migrations',
      label: 'Migrations',
      icon: Briefcase,
      description: 'View and manage migration jobs',
    },
    {
      href: '/target-schemas',
      label: 'Target Schemas',
      icon: Target,
      description: 'Configure destination schemas',
    },
  ];

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <div
      className={cn(
        'flex flex-col border-r border-slate-200 bg-white transition-all duration-500 ease-in-out',
        isCollapsed ? 'w-16' : 'w-64',
        className
      )}
    >
      {/* Header */}
      <div className='flex items-center justify-between border-b border-slate-200 p-4'>
        <div className='flex flex-1 items-center space-x-3'>
          <Image
            src='/flinkkLogo.png'
            alt='Flinkk Logo'
            width={40}
            height={40}
            className='h-10 w-10'
          />
          <div
            className={cn(
              'overflow-hidden transition-all duration-500 ease-in-out',
              isCollapsed ? 'w-0 opacity-0' : 'w-auto opacity-100'
            )}
          >
            <h1 className='text-lg font-bold whitespace-nowrap text-slate-900'>Flinkk</h1>
            <p className='text-xs whitespace-nowrap text-slate-500'>Transfer Hub</p>
          </div>
        </div>

        <Button
          variant='ghost'
          size='sm'
          onClick={toggleSidebar}
          className='h-8 w-8 flex-shrink-0 p-0'
        >
          {isCollapsed ? <ChevronRight className='h-4 w-4' /> : <ChevronLeft className='h-4 w-4' />}
        </Button>
      </div>

      {/* Navigation */}
      <nav className='flex-1 space-y-2 p-4'>
        {navigationItems.map(item => {
          const Icon = item.icon;
          const isActive = pathname === item.href;

          return (
            <Link key={item.href} href={item.href}>
              <div
                className={cn(
                  'flex items-center rounded-lg px-3 py-2 transition-all duration-300 ease-in-out',
                  isActive
                    ? 'border border-blue-200 bg-blue-100 text-blue-700 shadow-sm'
                    : 'text-slate-600 hover:bg-slate-100 hover:text-slate-900',
                  isCollapsed ? 'justify-center' : 'space-x-3'
                )}
              >
                <Icon className='h-5 w-5 flex-shrink-0' />
                <div
                  className={cn(
                    'min-w-0 flex-1 overflow-hidden transition-all duration-500 ease-in-out',
                    isCollapsed ? 'w-0 opacity-0' : 'w-auto opacity-100'
                  )}
                >
                  <p className='truncate text-sm font-medium whitespace-nowrap'>{item.label}</p>
                  <p className='truncate text-xs whitespace-nowrap text-slate-500'>
                    {item.description}
                  </p>
                </div>
              </div>
            </Link>
          );
        })}
      </nav>

      {/* Footer */}
      {/* <div className='border-t border-slate-200 p-4'>
        <Link href='/settings'>
          <div
            className={cn(
              'flex items-center rounded-lg px-3 py-2 text-slate-600 transition-all duration-300 ease-in-out hover:bg-slate-100 hover:text-slate-900',
              isCollapsed ? 'justify-center' : 'space-x-3'
            )}
          >
            <Settings className='h-5 w-5 flex-shrink-0' />
            <div
              className={cn(
                'min-w-0 flex-1 overflow-hidden transition-all duration-500 ease-in-out',
                isCollapsed ? 'w-0 opacity-0' : 'w-auto opacity-100'
              )}
            >
              <p className='truncate text-sm font-medium whitespace-nowrap'>Settings</p>
              <p className='truncate text-xs whitespace-nowrap text-slate-500'>App configuration</p>
            </div>
          </div>
        </Link>
      </div> */}
    </div>
  );
}
