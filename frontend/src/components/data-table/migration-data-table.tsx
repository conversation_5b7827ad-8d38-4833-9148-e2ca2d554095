'use client';

import { useState, useMemo } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
  getFilteredRowModel,
  flexRender,
  type ColumnDef,
  type ColumnFiltersState,
} from '@tanstack/react-table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Edit,
  Save,
  X,
  Loader2,
} from 'lucide-react';

interface MigrationDataTableProps {
  data: Record<string, any>[];
  total: number;
  page: number;
  limit: number;
  onPageChange: (page: number) => void;
  onLimitChange: (limit: number) => void;
  onCellEdit?: (rowIndex: number, field: string, value: any) => Promise<void>;
  isLoading?: boolean;
  title?: string;
  description?: string;
  editableFields?: string[];
  modifiedCells?: Set<string>; // Set of "rowIndex-field" strings
}

interface EditingCell {
  rowIndex: number;
  field: string;
  value: any;
}

export function MigrationDataTable({
  data,
  total,
  page,
  limit,
  onPageChange,
  onLimitChange,
  onCellEdit,
  isLoading = false,
  title = "Data Table",
  description = "Review and edit your data",
  editableFields,
  modifiedCells = new Set(),
}: MigrationDataTableProps) {
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [editingCell, setEditingCell] = useState<EditingCell | null>(null);
  const [savingCell, setSavingCell] = useState<string | null>(null); // Track which cell is being saved

  // Get field names from the first record
  const fieldNames = useMemo(() => {
    if (data.length === 0) return [];
    return Object.keys(data[0]);
  }, [data]);

  // Create columns dynamically based on data structure
  const columns = useMemo<ColumnDef<Record<string, any>>[]>(() => {
    return fieldNames.map((fieldName) => ({
      id: fieldName,
      accessorKey: fieldName,
      header: () => (
        <div className="font-medium text-slate-900">
          {fieldName.replace('__c', '').replace('_', ' ')}
        </div>
      ),
      cell: ({ row }) => {
        const rowIndex = row.index;
        const value = row.getValue(fieldName);
        const cellKey = `${rowIndex}-${fieldName}`;
        const isModified = modifiedCells.has(cellKey);
        const isEditable = !editableFields || editableFields.includes(fieldName);
        const isEditing = editingCell?.rowIndex === rowIndex && editingCell?.field === fieldName;

        if (isEditing) {
          return (
            <EditableCell
              value={editingCell.value}
              onChange={(newValue) => setEditingCell({ ...editingCell, value: newValue })}
              onSave={async () => {
                const cellKey = `${rowIndex}-${fieldName}`;
                setSavingCell(cellKey);
                try {
                  if (onCellEdit) {
                    await onCellEdit(rowIndex, fieldName, editingCell.value);
                  }
                  setEditingCell(null);
                } catch (error) {
                  console.error('Failed to save cell:', error);
                } finally {
                  setSavingCell(null);
                }
              }}
              onCancel={() => setEditingCell(null)}
              isSaving={savingCell === `${rowIndex}-${fieldName}`}
            />
          );
        }

        return (
          <div
            className={`group flex cursor-pointer items-center justify-between rounded p-2 transition-colors hover:bg-slate-50 ${
              isModified ? 'border-l-2 border-blue-400 bg-blue-50' : ''
            } ${!isEditable ? 'cursor-default' : ''}`}
            onClick={() => {
              if (isEditable && onCellEdit) {
                setEditingCell({ rowIndex, field: fieldName, value });
              }
            }}
          >
            <span className={`flex-1 text-sm ${isModified ? 'font-medium text-blue-700' : ''}`}>
              {String(value || '')}
            </span>
            <div className="flex items-center space-x-1">
              {isModified && (
                <div className="h-2 w-2 rounded-full bg-blue-500" title="Modified" />
              )}
              {isEditable && onCellEdit && (
                <Edit className="h-3 w-3 text-slate-400 opacity-0 group-hover:opacity-50" />
              )}
            </div>
          </div>
        );
      },
    }));
  }, [fieldNames, editingCell, modifiedCells, editableFields, onCellEdit]);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnFiltersChange: setColumnFilters,
    state: {
      columnFilters,
    },
    manualPagination: true,
    pageCount: Math.ceil(total / limit),
  });

  const totalPages = Math.ceil(total / limit);

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        {/* Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow key={row.id} data-state={row.getIsSelected() && 'selected'}>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    {isLoading ? 'Loading...' : 'No data available.'}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between space-x-2 py-4">
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium">Rows per page</p>
            <select
              value={limit}
              onChange={(e) => onLimitChange(Number(e.target.value))}
              className="h-8 w-[70px] rounded border border-input bg-background px-3 py-1 text-sm"
            >
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
          </div>

          <div className="flex items-center space-x-6 lg:space-x-8">
            <div className="flex w-[100px] items-center justify-center text-sm font-medium">
              Page {page} of {totalPages}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => onPageChange(1)}
                disabled={page <= 1}
              >
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => onPageChange(page - 1)}
                disabled={page <= 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => onPageChange(page + 1)}
                disabled={page >= totalPages}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => onPageChange(totalPages)}
                disabled={page >= totalPages}
              >
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Editable cell component
function EditableCell({
  value,
  onChange,
  onSave,
  onCancel,
  isSaving = false,
}: {
  value: any;
  onChange: (value: any) => void;
  onSave: () => void;
  onCancel: () => void;
  isSaving?: boolean;
}) {
  return (
    <div className="relative">
      <Input
        value={String(value || '')}
        onChange={(e) => onChange(e.target.value)}
        className="h-8 w-full border-purple-300 pr-16 text-sm focus:border-purple-500 focus:ring-purple-500"
        autoFocus
        onKeyDown={(e) => {
          if (e.key === 'Enter') {
            onSave();
          } else if (e.key === 'Escape') {
            onCancel();
          }
        }}
      />
      <div className="absolute top-1/2 right-1 flex -translate-y-1/2 transform items-center space-x-1">
        <Button
          size="sm"
          variant="ghost"
          onClick={onSave}
          disabled={isSaving}
          className="h-6 w-6 rounded-sm p-0 text-green-600 hover:bg-green-50 hover:text-green-700 disabled:opacity-50"
        >
          {isSaving ? (
            <Loader2 className="h-3 w-3 animate-spin" />
          ) : (
            <Save className="h-3 w-3" />
          )}
        </Button>
        <Button
          size="sm"
          variant="ghost"
          onClick={onCancel}
          className="h-6 w-6 rounded-sm p-0 text-red-600 hover:bg-red-50 hover:text-red-700"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>
    </div>
  );
}
