'use client';

import { useState, useMemo } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  flexRender,
  type ColumnDef,
  type SortingState,
  type ColumnFiltersState,
} from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Edit,
  Save,
  X,
  AlertTriangle,
  CheckCircle,
  Clock,
} from 'lucide-react';
import { type StagedData } from '@/lib/api';
import { useUpdateStagedRecord } from '@/hooks/use-staged-data';

interface StagedDataTableProps {
  data: StagedData[];
  total: number;
  page: number;
  limit: number;
  onPageChange: (page: number) => void;
  onLimitChange: (limit: number) => void;
  isLoading?: boolean;
}

interface EditingCell {
  rowId: string;
  columnId: string;
  value: string | number | boolean | null;
}

export function StagedDataTable({
  data,
  total,
  page,
  limit,
  onPageChange,
  onLimitChange,
  isLoading = false,
}: StagedDataTableProps) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [editingCell, setEditingCell] = useState<EditingCell | null>(null);

  const updateMutation = useUpdateStagedRecord();

  // Get all unique field names from the data to create dynamic columns
  const fieldNames = useMemo(() => {
    const fields = new Set<string>();
    data.forEach(record => {
      Object.keys(record.transformed_data).forEach(key => fields.add(key));
      if (record.user_modifications) {
        Object.keys(record.user_modifications).forEach(key => fields.add(key));
      }
    });
    return Array.from(fields).sort();
  }, [data]);

  const handleSave = async (
    record: StagedData,
    fieldName: string,
    newValue: string | number | boolean | null
  ) => {
    try {
      await updateMutation.mutateAsync({
        objectName: record.object_name,
        recordId: record.id,
        fieldUpdates: { [fieldName]: newValue },
        userId: 'current-user', // TODO: Get from auth context
      });
      setEditingCell(null);
    } catch (error) {
      console.error('Failed to update record:', error);
      // TODO: Show error toast
    }
  };

  const columns = useMemo<ColumnDef<StagedData>[]>(
    () => [
      // Status column
      {
        accessorKey: 'sync_status',
        header: 'Status',
        cell: ({ row }) => {
          const status = row.original.sync_status;
          const validation = row.original.validation_status;

          return (
            <div className='flex items-center space-x-2'>
              <Badge
                variant={
                  status === 'synced'
                    ? 'default'
                    : status === 'failed'
                      ? 'destructive'
                      : 'secondary'
                }
                className='flex items-center space-x-1'
              >
                {status === 'synced' && <CheckCircle className='h-3 w-3' />}
                {status === 'failed' && <AlertTriangle className='h-3 w-3' />}
                {status === 'pending' && <Clock className='h-3 w-3' />}
                <span className='capitalize'>{status}</span>
              </Badge>
              {validation === 'invalid' && (
                <Badge variant='destructive' className='text-xs'>
                  Invalid
                </Badge>
              )}
              {row.original.user_modified && (
                <Badge variant='outline' className='text-xs'>
                  Modified
                </Badge>
              )}
            </div>
          );
        },
      },
      // Source ID column
      {
        accessorKey: 'source_id',
        header: 'Source ID',
        cell: ({ row }) => (
          <code className='rounded bg-gray-100 px-1 py-0.5 text-xs'>{row.original.source_id}</code>
        ),
      },
      // Dynamic data columns
      ...fieldNames.map(
        (fieldName): ColumnDef<StagedData> => ({
          id: fieldName,
          header: fieldName,
          cell: ({ row }) => {
            const record = row.original;
            const isEditing =
              editingCell?.rowId === record.id && editingCell?.columnId === fieldName;

            // Get current value (user modification takes precedence)
            const currentValue =
              record.user_modifications?.[fieldName] ?? record.transformed_data[fieldName];

            if (isEditing) {
              return (
                <EditableCell
                  value={editingCell.value}
                  onChange={value => setEditingCell({ ...editingCell, value })}
                  onSave={() => handleSave(record, fieldName, editingCell.value)}
                  onCancel={() => setEditingCell(null)}
                />
              );
            }

            return (
              <div
                className='group flex cursor-pointer items-center justify-between rounded p-1 hover:bg-gray-50'
                onClick={() =>
                  setEditingCell({
                    rowId: record.id,
                    columnId: fieldName,
                    value: currentValue,
                  })
                }
              >
                <span
                  className={
                    record.user_modifications?.[fieldName] ? 'font-medium text-blue-600' : ''
                  }
                >
                  {formatCellValue(currentValue)}
                </span>
                <Edit className='h-3 w-3 opacity-0 group-hover:opacity-50' />
              </div>
            );
          },
        })
      ),
    ],
    [fieldNames, editingCell, data, handleSave]
  );

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    state: {
      sorting,
      columnFilters,
    },
    manualPagination: true,
    pageCount: Math.ceil(total / limit),
  });

  const totalPages = Math.ceil(total / limit);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Staged Data</CardTitle>
        <CardDescription>
          Review and edit data before final synchronization. Click any cell to edit.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Filters */}
        <div className='mb-4 flex items-center space-x-2'>
          <Input
            placeholder='Filter by source ID...'
            value={(table.getColumn('source_id')?.getFilterValue() as string) ?? ''}
            onChange={event => table.getColumn('source_id')?.setFilterValue(event.target.value)}
            className='max-w-sm'
          />
        </div>

        {/* Table */}
        <div className='rounded-md border'>
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map(headerGroup => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map(header => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map(row => (
                  <TableRow key={row.id} data-state={row.getIsSelected() && 'selected'}>
                    {row.getVisibleCells().map(cell => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className='h-24 text-center'>
                    {isLoading ? 'Loading...' : 'No results.'}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        <div className='flex items-center justify-between space-x-2 py-4'>
          <div className='text-muted-foreground text-sm'>
            Showing {(page - 1) * limit + 1} to {Math.min(page * limit, total)} of {total} results
          </div>
          <div className='flex items-center space-x-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={() => onPageChange(1)}
              disabled={page === 1}
            >
              <ChevronsLeft className='h-4 w-4' />
            </Button>
            <Button
              variant='outline'
              size='sm'
              onClick={() => onPageChange(page - 1)}
              disabled={page === 1}
            >
              <ChevronLeft className='h-4 w-4' />
            </Button>
            <span className='text-sm'>
              Page {page} of {totalPages}
            </span>
            <Button
              variant='outline'
              size='sm'
              onClick={() => onPageChange(page + 1)}
              disabled={page === totalPages}
            >
              <ChevronRight className='h-4 w-4' />
            </Button>
            <Button
              variant='outline'
              size='sm'
              onClick={() => onPageChange(totalPages)}
              disabled={page === totalPages}
            >
              <ChevronsRight className='h-4 w-4' />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Editable cell component
interface EditableCellProps {
  value: string | number | boolean | null;
  onChange: (value: string | number | boolean | null) => void;
  onSave: () => void;
  onCancel: () => void;
}

function EditableCell({ value, onChange, onSave, onCancel }: EditableCellProps) {
  return (
    <div className='flex items-center space-x-1'>
      <Input
        value={String(value || '')}
        onChange={e => onChange(e.target.value)}
        className='h-8 text-sm'
        autoFocus
        onKeyDown={e => {
          if (e.key === 'Enter') {
            onSave();
          } else if (e.key === 'Escape') {
            onCancel();
          }
        }}
      />
      <Button size='sm' variant='ghost' onClick={onSave}>
        <Save className='h-3 w-3' />
      </Button>
      <Button size='sm' variant='ghost' onClick={onCancel}>
        <X className='h-3 w-3' />
      </Button>
    </div>
  );
}

// Utility function to format cell values
function formatCellValue(value: string | number | boolean | null | object): string {
  if (value === null || value === undefined) {
    return '';
  }
  if (typeof value === 'object') {
    return JSON.stringify(value);
  }
  return String(value);
}
