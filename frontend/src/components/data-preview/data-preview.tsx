'use client';

import { useState, useMemo } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  flexRender,
  type ColumnDef,
  type SortingState,
  type ColumnFiltersState,
} from '@tanstack/react-table';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Download,
  ArrowLeft,
  FileSpreadsheet,
  ArrowUpDown,
  Search,
  Database,
  Filter,
  X,
  Plus,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface FieldFilter {
  field: string;
  operator: 'equals' | 'contains' | 'starts_with' | 'ends_with' | 'not_equals' | 'greater_than' | 'less_than' | 'is_empty' | 'is_not_empty' | 'before' | 'after' | 'on_or_before' | 'on_or_after' | 'is_true' | 'is_false';
  value: string;
  fieldType?: 'string' | 'number' | 'boolean' | 'date' | 'datetime';
}

interface SalesforceField {
  name: string;
  label: string;
  type: string;
  required: boolean;
  custom: boolean;
  updateable: boolean;
  createable: boolean;
  length: number;
  picklistValues: string[];
}

interface DataPreviewProps {
  objectName: string;
  data: Array<Record<string, any>>;
  totalRecords: number;
  selectedFields: string[];
  fieldMetadata: SalesforceField[];
  onBack: () => void;
  onExportExcel: (filteredData?: Array<Record<string, any>>, filters?: FieldFilter[], format?: 'csv' | 'excel') => void;
  isExporting?: boolean;
}

export function DataPreview({
  objectName,
  data,
  totalRecords,
  selectedFields,
  fieldMetadata,
  onBack,
  onExportExcel,
  isExporting = false,
}: DataPreviewProps) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [fieldFilters, setFieldFilters] = useState<FieldFilter[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [exportFormat, setExportFormat] = useState<'csv' | 'excel'>('excel');

  // Filter data based on field filters
  const filteredData = useMemo(() => {
    if (fieldFilters.length === 0) return data;

    return data.filter(row => {
      return fieldFilters.every(filter => {
        const fieldValue = row[filter.field];
        const filterValue = filter.value.toLowerCase();
        const rowValue = String(fieldValue || '').toLowerCase();

        switch (filter.operator) {
          case 'equals':
            if (filter.fieldType === 'date' || filter.fieldType === 'datetime') {
              const fieldDate = new Date(fieldValue);
              const filterDate = new Date(filter.value);
              return fieldDate.toDateString() === filterDate.toDateString();
            }
            return rowValue === filterValue;
          case 'contains':
            return rowValue.includes(filterValue);
          case 'starts_with':
            return rowValue.startsWith(filterValue);
          case 'ends_with':
            return rowValue.endsWith(filterValue);
          case 'not_equals':
            if (filter.fieldType === 'date' || filter.fieldType === 'datetime') {
              const fieldDate = new Date(fieldValue);
              const filterDate = new Date(filter.value);
              return fieldDate.toDateString() !== filterDate.toDateString();
            }
            return rowValue !== filterValue;
          case 'greater_than':
            return Number(fieldValue) > Number(filter.value);
          case 'less_than':
            return Number(fieldValue) < Number(filter.value);
          case 'before':
            return new Date(fieldValue) < new Date(filter.value);
          case 'after':
            return new Date(fieldValue) > new Date(filter.value);
          case 'on_or_before':
            return new Date(fieldValue) <= new Date(filter.value);
          case 'on_or_after':
            return new Date(fieldValue) >= new Date(filter.value);
          case 'is_true':
            return fieldValue === true || fieldValue === 'true' || fieldValue === '1';
          case 'is_false':
            return fieldValue === false || fieldValue === 'false' || fieldValue === '0' || fieldValue === null;
          case 'is_empty':
            return !fieldValue || fieldValue === '';
          case 'is_not_empty':
            return fieldValue && fieldValue !== '';
          default:
            return true;
        }
      });
    });
  }, [data, fieldFilters]);

  // Create columns based on selected fields
  const columns = useMemo<ColumnDef<Record<string, any>>[]>(() => {
    return selectedFields.map((fieldName) => ({
      accessorKey: fieldName,
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-auto p-0 font-semibold text-left justify-start hover:bg-transparent"
          >
            {fieldName}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ getValue }) => {
        const value = getValue();
        if (value === null || value === undefined) {
          return <span className="text-slate-400 italic">null</span>;
        }
        if (typeof value === 'boolean') {
          return (
            <span className={cn('font-medium', value ? 'text-green-600' : 'text-red-600')}>
              {value.toString()}
            </span>
          );
        }
        if (typeof value === 'string' && value.length > 50) {
          return (
            <span className="block truncate max-w-xs" title={value}>
              {value}
            </span>
          );
        }
        return <span>{String(value)}</span>;
      },
    }));
  }, [selectedFields]);

  const table = useReactTable({
    data: filteredData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    state: {
      sorting,
      columnFilters,
      globalFilter,
    },
    initialState: {
      pagination: {
        pageSize: 50,
      },
    },
  });

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const addFilter = () => {
    const firstField = selectedFields[0];
    const fieldType = getFieldType(firstField);
    const defaultOperator = fieldType === 'boolean' ? 'is_true' :
                           fieldType === 'date' || fieldType === 'datetime' ? 'equals' :
                           fieldType === 'number' ? 'equals' : 'contains';

    setFieldFilters([...fieldFilters, {
      field: firstField,
      operator: defaultOperator as FieldFilter['operator'],
      value: '',
      fieldType
    }]);
  };

  const updateFilter = (index: number, updates: Partial<FieldFilter>) => {
    const newFilters = [...fieldFilters];
    const currentFilter = newFilters[index];

    // If field is changing, update field type and reset operator
    if (updates.field && updates.field !== currentFilter.field) {
      const newFieldType = getFieldType(updates.field);
      const defaultOperator = newFieldType === 'boolean' ? 'is_true' :
                             newFieldType === 'date' || newFieldType === 'datetime' ? 'equals' :
                             newFieldType === 'number' ? 'equals' : 'contains';

      newFilters[index] = {
        ...currentFilter,
        ...updates,
        fieldType: newFieldType,
        operator: defaultOperator as FieldFilter['operator'],
        value: '' // Reset value when field changes
      };
    } else {
      newFilters[index] = { ...currentFilter, ...updates };
    }

    setFieldFilters(newFilters);
  };

  const removeFilter = (index: number) => {
    setFieldFilters(fieldFilters.filter((_, i) => i !== index));
  };

  const clearAllFilters = () => {
    setFieldFilters([]);
  };

  const getFieldType = (fieldName: string): 'string' | 'number' | 'boolean' | 'date' | 'datetime' => {
    const field = fieldMetadata.find(f => f.name === fieldName);
    if (!field) return 'string';

    const type = field.type.toLowerCase();
    if (type.includes('boolean')) return 'boolean';
    if (type.includes('date') && type.includes('time')) return 'datetime';
    if (type.includes('date')) return 'date';
    if (type.includes('int') || type.includes('double') || type.includes('currency') || type.includes('percent')) return 'number';
    return 'string';
  };

  const getOperatorsForFieldType = (fieldType: string) => {
    switch (fieldType) {
      case 'boolean':
        return [
          { value: 'is_true', label: 'Is True' },
          { value: 'is_false', label: 'Is False' },
          { value: 'is_empty', label: 'Is Empty' },
          { value: 'is_not_empty', label: 'Is Not Empty' },
        ];
      case 'date':
      case 'datetime':
        return [
          { value: 'equals', label: 'On Date' },
          { value: 'before', label: 'Before' },
          { value: 'after', label: 'After' },
          { value: 'on_or_before', label: 'On or Before' },
          { value: 'on_or_after', label: 'On or After' },
          { value: 'is_empty', label: 'Is Empty' },
          { value: 'is_not_empty', label: 'Is Not Empty' },
        ];
      case 'number':
        return [
          { value: 'equals', label: 'Equals' },
          { value: 'not_equals', label: 'Not Equals' },
          { value: 'greater_than', label: 'Greater Than' },
          { value: 'less_than', label: 'Less Than' },
          { value: 'is_empty', label: 'Is Empty' },
          { value: 'is_not_empty', label: 'Is Not Empty' },
        ];
      default: // string
        return [
          { value: 'contains', label: 'Contains' },
          { value: 'equals', label: 'Equals' },
          { value: 'starts_with', label: 'Starts With' },
          { value: 'ends_with', label: 'Ends With' },
          { value: 'not_equals', label: 'Not Equals' },
          { value: 'is_empty', label: 'Is Empty' },
          { value: 'is_not_empty', label: 'Is Not Empty' },
        ];
    }
  };

  const getOperatorLabel = (operator: string) => {
    const labels = {
      equals: 'Equals',
      contains: 'Contains',
      starts_with: 'Starts with',
      ends_with: 'Ends with',
      not_equals: 'Not equals',
      greater_than: 'Greater than',
      less_than: 'Less than',
      before: 'Before',
      after: 'After',
      on_or_before: 'On or before',
      on_or_after: 'On or after',
      is_true: 'Is true',
      is_false: 'Is false',
      is_empty: 'Is empty',
      is_not_empty: 'Is not empty',
    };
    return labels[operator as keyof typeof labels] || operator;
  };

  // Handle empty data state
  if (data.length === 0) {
    return (
      <Card className="border-0 bg-white/80 shadow-xl backdrop-blur-sm">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-3 text-xl">
                <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-purple-500 to-purple-600">
                  <FileSpreadsheet className="h-5 w-5 text-white" />
                </div>
                <span className="text-slate-900">No Data Found</span>
              </CardTitle>
              <CardDescription className="text-base text-slate-600 mt-2">
                No records were found for {objectName} with the selected fields.
              </CardDescription>
            </div>
            <Button onClick={onBack} variant="outline" className="border-slate-300">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
          </div>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center py-12 space-y-4">
          <Database className="h-16 w-16 text-slate-400" />
          <div className="text-center">
            <h3 className="text-lg font-semibold text-slate-900 mb-2">No Records Available</h3>
            <p className="text-slate-600 mb-4">
              The selected object doesn't contain any records matching your criteria.
            </p>
            <Button onClick={onBack} variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Select Different Fields
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-0 bg-white/80 shadow-xl backdrop-blur-sm">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-3 text-xl">
              <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-purple-500 to-purple-600">
                <FileSpreadsheet className="h-5 w-5 text-white" />
              </div>
              <span className="text-slate-900">Data Preview</span>
            </CardTitle>
            <CardDescription className="text-base text-slate-600 mt-2">
              Preview extracted data from {objectName}. Showing {formatNumber(filteredData.length)} of {formatNumber(totalRecords)} records
              {fieldFilters.length > 0 && ` (${formatNumber(data.length)} before filtering)`}.
            </CardDescription>
          </div>
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <Select value={exportFormat} onValueChange={(value: 'csv' | 'excel') => setExportFormat(value)}>
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="excel">Excel</SelectItem>
                  <SelectItem value="csv">CSV</SelectItem>
                </SelectContent>
              </Select>
              <Button
                onClick={() => onExportExcel(filteredData, fieldFilters, exportFormat)}
                disabled={isExporting || filteredData.length === 0}
                className="bg-green-600 hover:bg-green-700"
              >
                {isExporting ? (
                  <>
                    <Download className="mr-2 h-4 w-4 animate-spin" />
                    Exporting...
                  </>
                ) : (
                  <>
                    <Download className="mr-2 h-4 w-4" />
                    Export {formatNumber(filteredData.length)} records
                  </>
                )}
              </Button>
            </div>
            <Button onClick={onBack} variant="outline" className="border-slate-300">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Global Search and Filter Controls */}
        <div className="space-y-4">
          <div className="flex items-center space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-slate-400" />
              <Input
                placeholder="Search across all fields..."
                value={globalFilter}
                onChange={(e) => setGlobalFilter(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className={cn(showFilters && 'bg-blue-50 border-blue-300')}
            >
              <Filter className="mr-2 h-4 w-4" />
              Filters
              {fieldFilters.length > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {fieldFilters.length}
                </Badge>
              )}
            </Button>
          </div>

          {/* Filter Interface */}
          {showFilters && (
            <div className="rounded-lg border border-slate-200 bg-slate-50 p-4 space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-slate-900">Field Filters</h3>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" onClick={addFilter}>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Filter
                  </Button>
                  {fieldFilters.length > 0 && (
                    <Button variant="outline" size="sm" onClick={clearAllFilters}>
                      Clear All
                    </Button>
                  )}
                </div>
              </div>

              {fieldFilters.length === 0 ? (
                <div className="text-center py-6">
                  <Filter className="h-8 w-8 text-slate-400 mx-auto mb-2" />
                  <p className="text-sm text-slate-600">
                    No filters applied. Click "Add Filter" to filter records by field values.
                  </p>
                  <p className="text-xs text-slate-500 mt-1">
                    Supports text, number, date, and boolean field filtering
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {fieldFilters.map((filter, index) => (
                    <div key={index} className="flex items-center space-x-3 bg-white p-3 rounded-lg border">
                      <Select
                        value={filter.field}
                        onValueChange={(value) => updateFilter(index, { field: value })}
                      >
                        <SelectTrigger className="w-40">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {selectedFields.map(field => {
                            const fieldType = getFieldType(field);
                            const fieldMeta = fieldMetadata.find(f => f.name === field);
                            return (
                              <SelectItem key={field} value={field}>
                                <div className="flex items-center space-x-2">
                                  <span>{field}</span>
                                  <Badge variant="outline" className="text-xs">
                                    {fieldMeta?.type || fieldType}
                                  </Badge>
                                </div>
                              </SelectItem>
                            );
                          })}
                        </SelectContent>
                      </Select>

                      <Select
                        value={filter.operator}
                        onValueChange={(value) => updateFilter(index, { operator: value as FieldFilter['operator'] })}
                      >
                        <SelectTrigger className="w-36">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {getOperatorsForFieldType(filter.fieldType || 'string').map(op => (
                            <SelectItem key={op.value} value={op.value}>
                              {op.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>

                      {!['is_empty', 'is_not_empty', 'is_true', 'is_false'].includes(filter.operator) && (
                        <>
                          {(filter.fieldType === 'date' || filter.fieldType === 'datetime') ? (
                            <Input
                              type={filter.fieldType === 'datetime' ? 'datetime-local' : 'date'}
                              value={filter.value}
                              onChange={(e) => updateFilter(index, { value: e.target.value })}
                              className="flex-1"
                            />
                          ) : filter.fieldType === 'number' ? (
                            <Input
                              type="number"
                              placeholder="Enter number..."
                              value={filter.value}
                              onChange={(e) => updateFilter(index, { value: e.target.value })}
                              className="flex-1"
                            />
                          ) : (
                            <Input
                              type="text"
                              placeholder="Filter value..."
                              value={filter.value}
                              onChange={(e) => updateFilter(index, { value: e.target.value })}
                              className="flex-1"
                            />
                          )}
                        </>
                      )}

                      {filter.fieldType === 'boolean' && ['is_true', 'is_false'].includes(filter.operator) && (
                        <div className="flex-1 flex items-center px-3 py-2 text-sm text-slate-600 bg-slate-50 rounded border">
                          {filter.operator === 'is_true' ? 'True values' : 'False/null values'}
                        </div>
                      )}

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeFilter(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}

              {fieldFilters.length > 0 && (
                <div className="text-sm text-slate-600">
                  Showing {formatNumber(filteredData.length)} of {formatNumber(data.length)} records after filtering
                </div>
              )}
            </div>
          )}
        </div>

        {/* Data Table */}
        <div className="rounded-lg border border-slate-200 bg-white">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id} className="bg-slate-50">
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id} className="font-semibold text-slate-900">
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    className="hover:bg-slate-50 transition-colors"
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id} className="py-3">
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    No results found.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between">
          <div className="text-sm text-slate-600">
            Showing {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1} to{' '}
            {Math.min(
              (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
              table.getFilteredRowModel().rows.length
            )}{' '}
            of {formatNumber(table.getFilteredRowModel().rows.length)} filtered records
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm text-slate-600">
              Page {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
