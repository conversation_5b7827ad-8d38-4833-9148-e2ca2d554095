'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useStartMigration } from '@/hooks/use-migrations';
import {
  Play,
  Database,
  Settings,
  AlertTriangle,
  CheckCircle,
  Loader2,
  Upload,
} from 'lucide-react';

// Form validation schema
const migrationFormSchema = z.object({
  objectName: z.string().min(1, 'Please select an object type'),
  jobName: z.string().optional(),
  description: z.string().optional(),
  sourceConfig: z.object({
    batchSize: z.number().min(1).max(10000).default(1000),
    includeDeleted: z.boolean().default(false),
  }),
  targetConfig: z.object({
    createMode: z.enum(['create', 'upsert', 'update']).default('upsert'),
    batchSize: z.number().min(1).max(1000).default(100),
  }),
  transformationConfig: z.object({
    validateData: z.boolean().default(true),
    skipInvalidRecords: z.boolean().default(false),
  }),
});

type MigrationFormData = z.infer<typeof migrationFormSchema>;

// Salesforce object options
const SALESFORCE_OBJECTS = [
  { value: 'product', label: 'Product', description: 'Product catalog and pricing information' },
  { value: 'account', label: 'Account', description: 'Customer accounts and company information' },
  { value: 'contact', label: 'Contact', description: 'Individual contact records' },
  { value: 'opportunity', label: 'Opportunity', description: 'Sales opportunities and deals' },
  { value: 'lead', label: 'Lead', description: 'Potential customer leads' },
  { value: 'case', label: 'Case', description: 'Customer support cases' },
];

export function MigrationStartForm() {
  const router = useRouter();
  const startMigration = useStartMigration();
  const [selectedObject, setSelectedObject] = useState<string>('');

  const form = useForm<MigrationFormData>({
    resolver: zodResolver(migrationFormSchema),
    defaultValues: {
      objectName: '',
      jobName: '',
      description: '',
      sourceConfig: {
        batchSize: 1000,
        includeDeleted: false,
      },
      targetConfig: {
        createMode: 'upsert',
        batchSize: 100,
      },
      transformationConfig: {
        validateData: true,
        skipInvalidRecords: false,
      },
    },
  });

  const onSubmit = async (data: MigrationFormData) => {
    try {
      const result = await startMigration.mutateAsync({
        object_name: data.objectName,
        job_name: data.jobName || `${data.objectName} Migration - ${new Date().toLocaleString()}`,
        source_config: data.sourceConfig,
        target_config: data.targetConfig,
        transformation_config: data.transformationConfig,
      });

      // Redirect to job monitoring page
      router.push(`/migrations/${result.job_id}`);
    } catch (error) {
      console.error('Failed to start migration:', error);
    }
  };

  const selectedObjectInfo = SALESFORCE_OBJECTS.find(obj => obj.value === selectedObject);

  return (
    <div className='space-y-8'>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-8'>
          {/* Object Selection */}
          <Card className='border-0 bg-white/80 shadow-xl backdrop-blur-sm'>
            <CardHeader className='pb-6'>
              <CardTitle className='flex items-center space-x-3 text-xl'>
                <div className='flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-blue-600'>
                  <Database className='h-5 w-5 text-white' />
                </div>
                <span className='text-slate-900'>Source Configuration</span>
              </CardTitle>
              <CardDescription className='text-base text-slate-600'>
                Select the Salesforce object you want to migrate and configure extraction settings
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <FormField
                control={form.control}
                name='objectName'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='text-base font-medium text-slate-900'>
                      Salesforce Object
                    </FormLabel>
                    <Select
                      onValueChange={value => {
                        field.onChange(value);
                        setSelectedObject(value);
                      }}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className='h-12 border-slate-200 focus:border-blue-500 focus:ring-blue-500'>
                          <SelectValue placeholder='Select an object to migrate' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {SALESFORCE_OBJECTS.map(obj => (
                          <SelectItem key={obj.value} value={obj.value} className='py-3'>
                            <div className='flex flex-col'>
                              <span className='font-medium text-slate-900'>{obj.label}</span>
                              <span className='text-sm text-slate-500'>{obj.description}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {selectedObjectInfo && (
                <Alert className='border-green-200 bg-green-50'>
                  <CheckCircle className='h-4 w-4 text-green-600' />
                  <AlertDescription className='text-green-800'>
                    <strong>{selectedObjectInfo.label}</strong>: {selectedObjectInfo.description}
                  </AlertDescription>
                </Alert>
              )}

              <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
                <FormField
                  control={form.control}
                  name='sourceConfig.batchSize'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-base font-medium text-slate-900'>
                        Batch Size
                      </FormLabel>
                      <FormControl>
                        <Input
                          type='number'
                          className='h-12 border-slate-200 focus:border-blue-500 focus:ring-blue-500'
                          {...field}
                          onChange={e => field.onChange(parseInt(e.target.value))}
                        />
                      </FormControl>
                      <FormDescription className='text-slate-600'>
                        Number of records to process per batch (1-10,000)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className='flex items-center space-x-3 pt-8'>
                  <FormField
                    control={form.control}
                    name='sourceConfig.includeDeleted'
                    render={({ field }) => (
                      <FormItem className='flex flex-row items-center space-y-0 space-x-3'>
                        <FormControl>
                          <input
                            type='checkbox'
                            checked={field.value}
                            onChange={field.onChange}
                            className='h-4 w-4 rounded border-slate-300 text-blue-600 focus:ring-blue-500'
                          />
                        </FormControl>
                        <div className='space-y-1 leading-none'>
                          <FormLabel className='text-base font-medium text-slate-900'>
                            Include Deleted Records
                          </FormLabel>
                          <FormDescription className='text-slate-600'>
                            Include soft-deleted records from Salesforce
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Job Details */}
          <Card className='border-0 bg-white/80 shadow-xl backdrop-blur-sm'>
            <CardHeader className='pb-6'>
              <CardTitle className='flex items-center space-x-3 text-xl'>
                <div className='flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-purple-500 to-purple-600'>
                  <Settings className='h-5 w-5 text-white' />
                </div>
                <span className='text-slate-900'>Job Configuration</span>
              </CardTitle>
              <CardDescription className='text-base text-slate-600'>
                Configure job settings and provide descriptive information
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <FormField
                control={form.control}
                name='jobName'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='text-base font-medium text-slate-900'>
                      Job Name (Optional)
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder='e.g., Product Migration Q4 2024'
                        className='h-12 border-slate-200 focus:border-purple-500 focus:ring-purple-500'
                        {...field}
                      />
                    </FormControl>
                    <FormDescription className='text-slate-600'>
                      A descriptive name for this migration job
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='description'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='text-base font-medium text-slate-900'>
                      Description (Optional)
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder='Describe the purpose and scope of this migration...'
                        className='min-h-[100px] resize-none border-slate-200 focus:border-purple-500 focus:ring-purple-500'
                        {...field}
                      />
                    </FormControl>
                    <FormDescription className='text-slate-600'>
                      Additional details about this migration
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Target Configuration */}
          <Card className='border-0 bg-white/80 shadow-xl backdrop-blur-sm'>
            <CardHeader className='pb-6'>
              <CardTitle className='flex items-center space-x-3 text-xl'>
                <div className='flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-green-500 to-green-600'>
                  <Upload className='h-5 w-5 text-white' />
                </div>
                <span className='text-slate-900'>Target Configuration</span>
              </CardTitle>
              <CardDescription className='text-base text-slate-600'>
                Configure how data will be synced to the target system
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
                <FormField
                  control={form.control}
                  name='targetConfig.createMode'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-base font-medium text-slate-900'>
                        Sync Mode
                      </FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className='h-12 border-slate-200 focus:border-green-500 focus:ring-green-500'>
                            <SelectValue placeholder='Select sync mode' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value='create'>Create Only</SelectItem>
                          <SelectItem value='upsert'>Upsert (Create or Update)</SelectItem>
                          <SelectItem value='update'>Update Only</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription className='text-slate-600'>
                        How to handle existing records in the target system
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='targetConfig.batchSize'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-base font-medium text-slate-900'>
                        Target Batch Size
                      </FormLabel>
                      <FormControl>
                        <Input
                          type='number'
                          className='h-12 border-slate-200 focus:border-green-500 focus:ring-green-500'
                          {...field}
                          onChange={e => field.onChange(parseInt(e.target.value))}
                        />
                      </FormControl>
                      <FormDescription className='text-slate-600'>
                        Number of records to sync per batch (1-1,000)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Transformation Configuration */}
          <Card className='border-0 bg-white/80 shadow-xl backdrop-blur-sm'>
            <CardHeader className='pb-6'>
              <CardTitle className='flex items-center space-x-3 text-xl'>
                <div className='flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-orange-500 to-orange-600'>
                  <Settings className='h-5 w-5 text-white' />
                </div>
                <span className='text-slate-900'>Data Processing Options</span>
              </CardTitle>
              <CardDescription className='text-base text-slate-600'>
                Configure data validation and transformation settings
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='space-y-6'>
                <FormField
                  control={form.control}
                  name='transformationConfig.validateData'
                  render={({ field }) => (
                    <FormItem className='flex flex-row items-start space-y-0 space-x-4 rounded-lg border border-slate-200 p-4'>
                      <FormControl>
                        <input
                          type='checkbox'
                          checked={field.value}
                          onChange={field.onChange}
                          className='mt-1 h-4 w-4 rounded border-slate-300 text-orange-600 focus:ring-orange-500'
                        />
                      </FormControl>
                      <div className='space-y-1 leading-none'>
                        <FormLabel className='text-base font-medium text-slate-900'>
                          Validate Data
                        </FormLabel>
                        <FormDescription className='text-slate-600'>
                          Perform comprehensive data validation before staging to catch errors early
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='transformationConfig.skipInvalidRecords'
                  render={({ field }) => (
                    <FormItem className='flex flex-row items-start space-y-0 space-x-4 rounded-lg border border-slate-200 p-4'>
                      <FormControl>
                        <input
                          type='checkbox'
                          checked={field.value}
                          onChange={field.onChange}
                          className='mt-1 h-4 w-4 rounded border-slate-300 text-orange-600 focus:ring-orange-500'
                        />
                      </FormControl>
                      <div className='space-y-1 leading-none'>
                        <FormLabel className='text-base font-medium text-slate-900'>
                          Skip Invalid Records
                        </FormLabel>
                        <FormDescription className='text-slate-600'>
                          Continue processing even if some records fail validation (recommended for
                          large datasets)
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Error Display */}
          {startMigration.error && (
            <Alert variant='destructive' className='border-red-200 bg-red-50'>
              <AlertTriangle className='h-4 w-4 text-red-600' />
              <AlertDescription className='text-red-800'>
                Failed to start migration: {startMigration.error.message}
              </AlertDescription>
            </Alert>
          )}

          {/* Submit Button */}
          <Card className='border-0 bg-gradient-to-br from-blue-50 to-purple-50 shadow-xl'>
            <CardContent className='pt-8 pb-8'>
              <div className='flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0'>
                <div className='text-center md:text-left'>
                  <h3 className='mb-1 text-lg font-semibold text-slate-900'>Ready to Start?</h3>
                  <p className='text-slate-600'>
                    This will begin extracting data from Salesforce and start the migration process.
                  </p>
                </div>
                <Button
                  type='submit'
                  disabled={startMigration.isPending}
                  className='h-12 min-w-[180px] bg-gradient-to-r from-blue-600 to-purple-600 font-medium text-white shadow-lg hover:from-blue-700 hover:to-purple-700'
                >
                  {startMigration.isPending ? (
                    <>
                      <Loader2 className='mr-2 h-5 w-5 animate-spin' />
                      Starting Migration...
                    </>
                  ) : (
                    <>
                      <Play className='mr-2 h-5 w-5' />
                      Start Migration
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </form>
      </Form>
    </div>
  );
}
