'use client';

import { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Loader2, Search, CheckSquare, Square, ArrowLeft, Download } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SalesforceField {
  name: string;
  label: string;
  type: string;
  required: boolean;
  custom: boolean;
  updateable: boolean;
  createable: boolean;
  length: number;
  picklistValues: string[];
}

interface FieldSelectionProps {
  objectName: string;
  fields: SalesforceField[];
  selectedFields: string[];
  onFieldsChange: (fields: string[]) => void;
  onExtract: () => void;
  onBack: () => void;
  isLoading?: boolean;
  isExtracting?: boolean;
}

export function FieldSelection({
  objectName,
  fields,
  selectedFields,
  onFieldsChange,
  onExtract,
  onBack,
  isLoading = false,
  isExtracting = false,
}: FieldSelectionProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [showOnlySelected, setShowOnlySelected] = useState(false);

  // Filter fields based on search term and selection filter
  const filteredFields = useMemo(() => {
    let filtered = fields;

    if (searchTerm) {
      filtered = filtered.filter(
        field =>
          field.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          field.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
          field.type.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (showOnlySelected) {
      filtered = filtered.filter(field => selectedFields.includes(field.name));
    }

    return filtered;
  }, [fields, searchTerm, showOnlySelected, selectedFields]);

  const handleSelectAll = () => {
    if (selectedFields.length === fields.length) {
      onFieldsChange([]);
    } else {
      onFieldsChange(fields.map(field => field.name));
    }
  };

  const handleFieldToggle = (fieldName: string) => {
    if (selectedFields.includes(fieldName)) {
      onFieldsChange(selectedFields.filter(name => name !== fieldName));
    } else {
      onFieldsChange([...selectedFields, fieldName]);
    }
  };

  const getFieldTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'string':
      case 'textarea':
        return 'bg-blue-100 text-blue-800';
      case 'int':
      case 'double':
      case 'currency':
        return 'bg-green-100 text-green-800';
      case 'boolean':
        return 'bg-purple-100 text-purple-800';
      case 'date':
      case 'datetime':
        return 'bg-orange-100 text-orange-800';
      case 'reference':
        return 'bg-pink-100 text-pink-800';
      case 'picklist':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-slate-100 text-slate-800';
    }
  };

  if (isLoading) {
    return (
      <Card className="border-0 bg-white/80 shadow-xl backdrop-blur-sm">
        <CardContent className="flex items-center justify-center py-12">
          <div className="flex items-center space-x-3">
            <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
            <span className="text-lg text-slate-600">Loading fields for {objectName}...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-0 bg-white/80 shadow-xl backdrop-blur-sm">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-3 text-xl">
              <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-green-500 to-green-600">
                <CheckSquare className="h-5 w-5 text-white" />
              </div>
              <span className="text-slate-900">Select Fields</span>
            </CardTitle>
            <CardDescription className="text-base text-slate-600 mt-2">
              Choose which fields to extract from {objectName}. Selected {selectedFields.length} of {fields.length} fields.
            </CardDescription>
          </div>
          <Button onClick={onBack} variant="outline" className="border-slate-300">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Search and Controls */}
        <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0 sm:space-x-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-slate-400" />
            <Input
              placeholder="Search fields by name, label, or type..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowOnlySelected(!showOnlySelected)}
              className={cn(showOnlySelected && 'bg-blue-50 border-blue-300')}
            >
              {showOnlySelected ? 'Show All' : 'Show Selected'}
            </Button>
            <Button variant="outline" size="sm" onClick={handleSelectAll}>
              {selectedFields.length === fields.length ? (
                <>
                  <Square className="mr-2 h-4 w-4" />
                  Deselect All
                </>
              ) : (
                <>
                  <CheckSquare className="mr-2 h-4 w-4" />
                  Select All
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Fields List */}
        <div className="max-h-96 overflow-y-auto rounded-lg border border-slate-200">
          {filteredFields.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <Search className="h-12 w-12 text-slate-400 mb-4" />
              <h3 className="text-lg font-semibold text-slate-900 mb-2">No fields found</h3>
              <p className="text-slate-600">
                {searchTerm ? 'Try adjusting your search criteria' : 'No fields available for this object'}
              </p>
            </div>
          ) : (
            <div className="grid gap-2 p-4">
              {filteredFields.map((field) => (
              <div
                key={field.name}
                className={cn(
                  'flex items-center space-x-3 rounded-lg border p-3 transition-colors hover:bg-slate-50',
                  selectedFields.includes(field.name) ? 'border-blue-300 bg-blue-50' : 'border-slate-200'
                )}
              >
                <Checkbox
                  checked={selectedFields.includes(field.name)}
                  onCheckedChange={() => handleFieldToggle(field.name)}
                />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-slate-900">{field.label}</span>
                    <code className="text-sm text-slate-500">({field.name})</code>
                    {field.required && (
                      <Badge variant="destructive" className="text-xs">
                        Required
                      </Badge>
                    )}
                    {field.custom && (
                      <Badge variant="outline" className="text-xs border-purple-300 text-purple-600">
                        Custom
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center space-x-2 mt-1">
                    <Badge className={cn('text-xs', getFieldTypeColor(field.type))}>
                      {field.type}
                    </Badge>
                    {field.length > 0 && (
                      <span className="text-xs text-slate-500">Max: {field.length}</span>
                    )}
                    {field.picklistValues.length > 0 && (
                      <span className="text-xs text-slate-500">
                        {field.picklistValues.length} options
                      </span>
                    )}
                  </div>
                </div>
              </div>
              ))}
            </div>
          )}
        </div>

        {/* Extract Button */}
        <div className="flex justify-end">
          <Button
            onClick={onExtract}
            disabled={selectedFields.length === 0 || isExtracting}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isExtracting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Extracting Data...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Extract Data ({selectedFields.length} fields)
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
