import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  stagedDataApi,
  type StagedDataListResponse as _StagedDataListResponse,
  type StagedData as _StagedData,
} from '@/lib/api';

interface UseStagedDataParams {
  objectName: string;
  page?: number;
  limit?: number;
  migrationJobId?: string;
  syncStatus?: string;
  validationStatus?: string;
  userModified?: boolean;
}

export function useStagedData(params: UseStagedDataParams) {
  return useQuery({
    queryKey: ['staged-data', params],
    queryFn: () =>
      stagedDataApi.getStagedData(params.objectName, {
        page: params.page,
        limit: params.limit,
        migration_job_id: params.migrationJobId,
        sync_status: params.syncStatus,
        validation_status: params.validationStatus,
        user_modified: params.userModified,
      }),
    enabled: !!params.objectName,
  });
}

export function useStagedDataSummary(objectName: string, migrationJobId?: string) {
  return useQuery({
    queryKey: ['staged-data-summary', objectName, migrationJobId],
    queryFn: () => stagedDataApi.getStagedDataSummary(objectName, migrationJobId),
    enabled: !!objectName,
  });
}

export function useUpdateStagedRecord() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      objectName,
      recordId,
      fieldUpdates,
      userId,
    }: {
      objectName: string;
      recordId: string;
      fieldUpdates: Record<string, string | number | boolean | null>;
      userId?: string;
    }) =>
      stagedDataApi.updateStagedRecord(objectName, recordId, {
        field_updates: fieldUpdates,
        user_id: userId,
      }),
    onSuccess: (_data, variables) => {
      // Invalidate and refetch staged data queries
      queryClient.invalidateQueries({
        queryKey: ['staged-data', { objectName: variables.objectName }],
      });
      queryClient.invalidateQueries({
        queryKey: ['staged-data-summary', variables.objectName],
      });
    },
  });
}
