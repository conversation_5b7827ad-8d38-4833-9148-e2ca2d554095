import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { migrationApi, type StartMigrationRequest, type MigrationJob } from '@/lib/api';

export function useStartMigration() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: StartMigrationRequest) => migrationApi.startMigration(data),
    onSuccess: data => {
      // Invalidate migration jobs list
      queryClient.invalidateQueries({
        queryKey: ['migration-jobs'],
      });

      // Add the new job to the cache if we have the job details
      queryClient.setQueryData(['migration-job', data.job_id], {
        id: data.job_id,
        status: 'pending',
        progress_percentage: 0,
        // ... other default values
      });
    },
  });
}

export function useMigrationJob(jobId: string, enabled = true) {
  return useQuery({
    queryKey: ['migration-job', jobId],
    queryFn: () => migrationApi.getMigrationJob(jobId),
    enabled: enabled && !!jobId,
    refetchInterval: data => {
      // Poll every 2 seconds if job is active
      const isActive =
        data?.status &&
        ['pending', 'extracting', 'transforming', 'staging', 'syncing'].includes(data.status);
      return isActive ? 2000 : false;
    },
  });
}

export function useMigrationStatus(jobId: string, enabled = true) {
  return useQuery({
    queryKey: ['migration-status', jobId],
    queryFn: () => migrationApi.getMigrationStatus(jobId),
    enabled: enabled && !!jobId,
    refetchInterval: data => {
      // Poll every 1 second for status updates
      const isActive =
        data?.status &&
        ['pending', 'extracting', 'transforming', 'staging', 'syncing'].includes(data.status);
      return isActive ? 1000 : false;
    },
  });
}

export function useMigrationJobs(params?: {
  objectName?: string;
  status?: string;
  limit?: number;
  offset?: number;
}) {
  return useQuery({
    queryKey: ['migration-jobs', params],
    queryFn: () => migrationApi.listMigrationJobs(params),
  });
}

export function useCancelMigrationJob() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (jobId: string) => migrationApi.cancelMigrationJob(jobId),
    onSuccess: (data, jobId) => {
      // Update the job status in cache
      queryClient.setQueryData(['migration-job', jobId], (old: MigrationJob | undefined) =>
        old ? { ...old, status: 'cancelled' as const } : old
      );

      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: ['migration-jobs'],
      });
      queryClient.invalidateQueries({
        queryKey: ['migration-status', jobId],
      });
    },
  });
}
