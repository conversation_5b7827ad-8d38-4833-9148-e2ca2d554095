'use client';

import { useEffect, useRef, useState, useCallback } from 'react';

export interface WebSocketMessage {
  type: string;
  pipeline_id?: number;
  data?: any;
  client_id?: string;
}

export interface PipelineUpdate {
  step: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress?: number;
  message?: string;
  timestamp: number;
}

interface UseWebSocketOptions {
  url: string;
  onMessage?: (message: WebSocketMessage) => void;
  onPipelineUpdate?: (pipelineId: number, update: PipelineUpdate) => void;
  onPipelineCompletion?: (pipelineId: number, status: string, runData: any) => void;
  onStatsUpdate?: (stats: any) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: Event) => void;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
}

export function useWebSocket({
  url,
  onMessage,
  onPipelineUpdate,
  onPipelineCompletion,
  onStatsUpdate,
  onConnect,
  onDisconnect,
  onError,
  reconnectInterval = 3000,
  maxReconnectAttempts = 5
}: UseWebSocketOptions) {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const clientIdRef = useRef<string | null>(null);
  const reconnectAttemptsRef = useRef(0);

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    setConnectionStatus('connecting');

    try {
      const ws = new WebSocket(url);
      wsRef.current = ws;

      ws.onopen = () => {
        setIsConnected(true);
        setConnectionStatus('connected');
        reconnectAttemptsRef.current = 0;
        setReconnectAttempts(0);
        onConnect?.();
      };

      ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          
          // Handle different message types
          switch (message.type) {
            case 'connection_established':
              clientIdRef.current = message.client_id || null;
              break;
              
            case 'pipeline_update':
              if (message.pipeline_id && message.data && onPipelineUpdate) {
                onPipelineUpdate(message.pipeline_id, message.data);
              }
              break;
              
            case 'stats_update':
              if (message.data && onStatsUpdate) {
                onStatsUpdate(message.data);
              }
              break;
              
            case 'pong':
              // Handle ping/pong for connection health
              break;
              
            default:
              // Handle completion messages
              if (message.data?.type === 'completion' && message.pipeline_id && onPipelineCompletion) {
                onPipelineCompletion(message.pipeline_id, message.data.status, message.data.run_data);
              }
              break;
          }
          
          onMessage?.(message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      ws.onclose = () => {
        setIsConnected(false);
        setConnectionStatus('disconnected');
        onDisconnect?.();

        // Attempt to reconnect if we haven't exceeded max attempts
        if (reconnectAttemptsRef.current < maxReconnectAttempts) {
          reconnectAttemptsRef.current += 1;
          setReconnectAttempts(reconnectAttemptsRef.current);
          setConnectionStatus('reconnecting');
          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, reconnectInterval);
        } else {
          setConnectionStatus('error');
        }
      };

      ws.onerror = (error) => {
        setConnectionStatus('error');
        onError?.(error);
      };

    } catch (error) {
      setConnectionStatus('error');
      console.error('WebSocket connection error:', error);
    }
  }, [url, onMessage, onPipelineUpdate, onPipelineCompletion, onStatsUpdate, onConnect, onDisconnect, onError, reconnectInterval, maxReconnectAttempts]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }

    setIsConnected(false);
    setConnectionStatus('disconnected');
    reconnectAttemptsRef.current = 0;
    setReconnectAttempts(0);
  }, []);

  const sendMessage = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
      return true;
    }
    return false;
  }, []);

  const subscribeToPipeline = useCallback((pipelineId: number) => {
    return sendMessage({
      type: 'subscribe',
      pipeline_id: pipelineId
    });
  }, [sendMessage]);

  const unsubscribeFromPipeline = useCallback((pipelineId: number) => {
    return sendMessage({
      type: 'unsubscribe',
      pipeline_id: pipelineId
    });
  }, [sendMessage]);

  const ping = useCallback(() => {
    return sendMessage({ type: 'ping' });
  }, [sendMessage]);


  return {
    isConnected,
    connectionStatus,
    reconnectAttempts,
    clientId: clientIdRef.current,
    connect,
    disconnect,
    sendMessage,
    subscribeToPipeline,
    unsubscribeFromPipeline,
    ping
  };
}
