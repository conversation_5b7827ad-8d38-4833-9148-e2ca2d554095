# Azure Storage Setup for ETL Pipeline

This document explains how the ETL pipeline automatically provisions and uses Azure Blob Storage for production deployments.

## 🚀 **Automatic Setup via CI/CD**

The system automatically provisions Azure Blob Storage when you deploy to production. No manual setup required!

### **What Gets Created Automatically:**

1. **Azure Storage Account** - Secure blob storage for ETL data
2. **Blob Container** - `etl-pipeline-data` container for organized data storage
3. **App Service Configuration** - Environment variables automatically configured

### **Required GitHub Secrets:**

Add these secrets to your GitHub repository for automatic deployment:

```bash
# Azure Authentication
AZURE_CREDENTIALS={"clientId":"xxx","clientSecret":"xxx","subscriptionId":"xxx","tenantId":"xxx"}
AZURE_SUBSCRIPTION_ID=your-azure-subscription-id
AZURE_RESOURCE_GROUP=your-resource-group-name

# Storage Account (optional - will use default if not provided)
AZURE_STORAGE_ACCOUNT_NAME=flinkkstorageaccount

# App Service
AZURE_WEBAPP_PUBLISH_PROFILE=<your-app-service-publish-profile>
```

## 🏗️ **Architecture**

### **Local Development (MinIO)**
```
ETL Pipeline → MinIO (localhost:9002) → Local Storage
```

### **Production (Azure Blob Storage)**
```
ETL Pipeline → Azure Blob Storage → Cloud Storage
```

### **Unified Interface**
The system automatically detects the environment and uses the appropriate storage:
- **Local**: `STORAGE_TYPE=minio` (default)
- **Production**: `STORAGE_TYPE=azure_blob` (set by CI/CD)

## 📁 **Data Organization**

Both MinIO and Azure Blob Storage use the same folder structure:

```
etl-pipeline-data/
├── raw/
│   └── {pipeline_name}/
│       └── {run_id}/
│           └── {timestamp}_raw_data.json
└── processed/
    └── {pipeline_name}/
        └── {run_id}/
            └── {timestamp}_processed_data.json
```

## 🔧 **Manual Setup (if needed)**

If you need to set up Azure Storage manually:

### 1. Create Storage Account
```bash
az storage account create \
  --name flinkkstorageaccount \
  --resource-group your-resource-group \
  --location eastus \
  --sku Standard_LRS \
  --kind StorageV2
```

### 2. Create Container
```bash
az storage container create \
  --name etl-pipeline-data \
  --account-name flinkkstorageaccount
```

### 3. Get Storage Key
```bash
az storage account keys list \
  --resource-group your-resource-group \
  --account-name flinkkstorageaccount \
  --query '[0].value' \
  --output tsv
```

### 4. Configure App Service
```bash
az webapp config appsettings set \
  --resource-group your-resource-group \
  --name flinkk-transfer-hub \
  --settings \
    STORAGE_TYPE=azure_blob \
    AZURE_STORAGE_ACCOUNT_NAME=flinkkstorageaccount \
    AZURE_STORAGE_ACCOUNT_KEY=your-storage-key \
    AZURE_STORAGE_CONTAINER_NAME=etl-pipeline-data
```

## 🔍 **Monitoring & Access**

### **Azure Portal**
- Navigate to your Storage Account in Azure Portal
- View containers and blob data
- Monitor storage usage and costs

### **Storage Explorer**
- Use Azure Storage Explorer for GUI access
- Browse and manage blob data
- Download/upload files manually

### **Azure CLI**
```bash
# List blobs in container
az storage blob list \
  --container-name etl-pipeline-data \
  --account-name flinkkstorageaccount

# Download a specific blob
az storage blob download \
  --container-name etl-pipeline-data \
  --name raw/Resort_Pipeline/1/20250107_120000_raw_data.json \
  --file local_file.json \
  --account-name flinkkstorageaccount
```

## 🔒 **Security Features**

- **HTTPS Only**: All traffic encrypted in transit
- **Private Access**: No public blob access allowed
- **Access Keys**: Secure key-based authentication
- **Retention Policies**: 30-day soft delete for data recovery

## 💰 **Cost Optimization**

- **Hot Tier**: Optimized for frequently accessed ETL data
- **Standard LRS**: Locally redundant storage for cost efficiency
- **Lifecycle Policies**: Can be added to automatically archive old data

## 🚨 **Troubleshooting**

### **Storage Connection Issues**
1. Check App Service environment variables
2. Verify storage account key is correct
3. Ensure container exists

### **Permission Issues**
1. Verify Azure credentials in GitHub secrets
2. Check resource group permissions
3. Ensure storage account allows shared key access

### **Data Not Found**
1. Check blob container name matches configuration
2. Verify data path structure
3. Check storage account region

## 📊 **Monitoring**

The application logs will show:
- ✅ `Azure Blob Storage service initialized` - Successful connection
- ✅ `Stored raw data at: raw/...` - Data successfully stored
- ✅ `Retrieved X records from ...` - Data successfully retrieved

## 🔄 **Migration from MinIO**

If you have existing MinIO data to migrate:

1. Export data from MinIO
2. Upload to Azure Blob Storage using Azure CLI or Storage Explorer
3. Update environment variables to use Azure Blob Storage
4. Test pipeline runs to ensure data accessibility

The unified storage interface ensures seamless migration without code changes!
