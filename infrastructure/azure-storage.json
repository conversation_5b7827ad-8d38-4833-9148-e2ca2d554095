{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"storageAccountName": {"type": "string", "defaultValue": "flinkkstorageaccount", "metadata": {"description": "Name of the storage account for ETL pipeline data"}}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]", "metadata": {"description": "Location for the storage account"}}, "containerName": {"type": "string", "defaultValue": "etl-pipeline-data", "metadata": {"description": "Name of the blob container"}}}, "variables": {"storageAccountName": "[toLower(parameters('storageAccountName'))]"}, "resources": [{"type": "Microsoft.Storage/storageAccounts", "apiVersion": "2023-01-01", "name": "[variables('storageAccountName')]", "location": "[parameters('location')]", "sku": {"name": "Standard_LRS"}, "kind": "StorageV2", "properties": {"accessTier": "Hot", "allowBlobPublicAccess": false, "allowSharedKeyAccess": true, "encryption": {"services": {"blob": {"enabled": true}}, "keySource": "Microsoft.Storage"}, "networkAcls": {"defaultAction": "Allow"}, "supportsHttpsTrafficOnly": true, "minimumTlsVersion": "TLS1_2"}}, {"type": "Microsoft.Storage/storageAccounts/blobServices", "apiVersion": "2023-01-01", "name": "[concat(variables('storageAccountName'), '/default')]", "dependsOn": ["[resourceId('Microsoft.Storage/storageAccounts', variables('storageAccountName'))]"], "properties": {"deleteRetentionPolicy": {"enabled": true, "days": 30}, "containerDeleteRetentionPolicy": {"enabled": true, "days": 30}}}, {"type": "Microsoft.Storage/storageAccounts/blobServices/containers", "apiVersion": "2023-01-01", "name": "[concat(variables('storageAccountName'), '/default/', parameters('containerName'))]", "dependsOn": ["[resourceId('Microsoft.Storage/storageAccounts/blobServices', variables('storageAccountName'), 'default')]"], "properties": {"publicAccess": "None"}}], "outputs": {"storageAccountName": {"type": "string", "value": "[variables('storageAccountName')]"}, "storageAccountKey": {"type": "string", "value": "[listKeys(resourceId('Microsoft.Storage/storageAccounts', variables('storageAccountName')), '2023-01-01').keys[0].value]"}, "containerName": {"type": "string", "value": "[parameters('containerName')]"}, "storageAccountEndpoint": {"type": "string", "value": "[reference(resourceId('Microsoft.Storage/storageAccounts', variables('storageAccountName'))).primaryEndpoints.blob]"}}}