{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"containerAppName": {"type": "string", "defaultValue": "flinkk-transfer-hub", "metadata": {"description": "Name of the container app"}}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]", "metadata": {"description": "Location for the container app"}}, "containerImage": {"type": "string", "defaultValue": "ghcr.io/your-username/flinkk-transfer-hub/backend:latest", "metadata": {"description": "Container image to deploy"}}, "environmentName": {"type": "string", "defaultValue": "flinkk-transfer-hub-env", "metadata": {"description": "Name of the container app environment"}}}, "variables": {"containerAppName": "[toLower(parameters('containerAppName'))]", "environmentName": "[toLower(parameters('environmentName'))]"}, "resources": [{"type": "Microsoft.App/managedEnvironments", "apiVersion": "2023-05-01", "name": "[variables('environmentName')]", "location": "[parameters('location')]", "properties": {"appLogsConfiguration": {"destination": "log-analytics"}}}, {"type": "Microsoft.App/containerApps", "apiVersion": "2023-05-01", "name": "[variables('containerAppName')]", "location": "[parameters('location')]", "dependsOn": ["[resourceId('Microsoft.App/managedEnvironments', variables('environmentName'))]"], "properties": {"managedEnvironmentId": "[resourceId('Microsoft.App/managedEnvironments', variables('environmentName'))]", "configuration": {"ingress": {"external": true, "targetPort": 8000, "allowInsecure": false, "traffic": [{"weight": 100, "latestRevision": true}]}, "secrets": [{"name": "database-url", "value": "[parameters('databaseUrl')]"}, {"name": "secret-key", "value": "[parameters('secretKey')]"}]}, "template": {"containers": [{"name": "backend", "image": "[parameters('containerImage')]", "env": [{"name": "DATABASE_URL", "secretRef": "database-url"}, {"name": "STORAGE_TYPE", "value": "azure_blob"}, {"name": "SECRET_KEY", "secretRef": "secret-key"}, {"name": "ENVIRONMENT", "value": "production"}, {"name": "CORS_ORIGINS", "value": "https://transferhub.flinkk.io"}], "resources": {"cpu": 0.5, "memory": "1Gi"}}], "scale": {"minReplicas": 1, "maxReplicas": 3}}}}], "outputs": {"containerAppFQDN": {"type": "string", "value": "[reference(resourceId('Microsoft.App/containerApps', variables('containerAppName'))).configuration.ingress.fqdn]"}}}