#!/bin/bash

# Development Environment Setup Script
# This script sets up the complete development environment using Docker Compose

set -e

echo "🚀 Setting up Flinkk Transfer Hub Development Environment..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f "backend/.env" ]; then
    echo "📝 Creating backend/.env file from template..."
    cp backend/.env.example backend/.env
    echo "⚠️  Please edit backend/.env with your actual configuration values"
fi

# Build and start services
echo "🔨 Building and starting services..."
docker-compose up -d --build

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 30

# Check service health
echo "🔍 Checking service health..."

# Check MinIO
if curl -f http://localhost:9002/minio/health/live &> /dev/null; then
    echo "✅ MinIO is running at http://localhost:9002"
    echo "   Console: http://localhost:9003 (admin/minioadmin123)"
else
    echo "❌ MinIO health check failed"
fi

# Check PostgreSQL
if docker-compose exec -T postgres pg_isready -U postgres &> /dev/null; then
    echo "✅ PostgreSQL is running"
else
    echo "❌ PostgreSQL health check failed"
fi

# Check Redis
if docker-compose exec -T redis redis-cli ping &> /dev/null; then
    echo "✅ Redis is running"
else
    echo "❌ Redis health check failed"
fi

# Check Backend API
if curl -f http://localhost:8000/health &> /dev/null; then
    echo "✅ Backend API is running at http://localhost:8000"
    echo "   API Docs: http://localhost:8000/docs"
else
    echo "❌ Backend API health check failed"
fi

echo ""
echo "🎉 Development environment setup complete!"
echo ""
echo "📋 Quick Start:"
echo "   • Backend API: http://localhost:8000"
echo "   • API Documentation: http://localhost:8000/docs"
echo "   • MinIO Console: http://localhost:9003"
echo "   • Database: localhost:5432 (postgres/postgres)"
echo ""
echo "🔧 Useful Commands:"
echo "   • View logs: docker-compose logs -f"
echo "   • Stop services: docker-compose down"
echo "   • Restart services: docker-compose restart"
echo "   • Rebuild: docker-compose up -d --build"
echo ""
echo "⚠️  Don't forget to:"
echo "   1. Edit backend/.env with your actual credentials"
echo "   2. Set up your Salesforce Connected App"
echo "   3. Configure your Azure services for production"
