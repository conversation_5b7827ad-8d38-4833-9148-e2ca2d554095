#!/usr/bin/env python3
"""
Test Room Pipeline fixes for status management, dependency chain, and record counting
"""
import sys
import os
import time
import requests
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append('backend')

from app.core.database import SessionLocal
from app.models.etl_pipeline import ETLPipelineRun, ETLPipeline, Destination, Hotel, Product, ProductVariants

def test_room_pipeline_fixes():
    """Test all Room Pipeline fixes"""
    print("🧪 TESTING ROOM PIPELINE FIXES")
    print("=" * 60)
    
    # Test 1: Verify dependency chain logic
    print("\n1️⃣ Testing Dependency Chain Logic")
    print("-" * 40)
    
    db = SessionLocal()
    try:
        # Check current data state
        destination_count = db.query(Destination).count()
        hotel_count = db.query(Hotel).count()
        product_count = db.query(Product).count()
        variant_count = db.query(ProductVariants).count()
        
        print(f"📊 Current Data State:")
        print(f"   destination: {destination_count}")
        print(f"   hotel: {hotel_count}")
        print(f"   product: {product_count}")
        print(f"   Product Variants: {variant_count}")
        
        # Check if dependency chain should trigger
        dependencies_needed = []
        if destination_count == 0:
            dependencies_needed.append("Resort Pipeline")
        if hotel_count == 0:
            dependencies_needed.append("Hotel Pipeline")
        if product_count == 0:
            dependencies_needed.append("Room Type Pipeline")
            
        if dependencies_needed:
            print(f"🔗 Dependencies needed: {', '.join(dependencies_needed)}")
        else:
            print("✅ All dependencies satisfied - Room Pipeline can run directly")
            
    finally:
        db.close()
    
    # Test 2: Execute Room Pipeline via API
    print("\n2️⃣ Testing Room Pipeline Execution")
    print("-" * 40)
    
    try:
        # Get initial variant count
        db = SessionLocal()
        try:
            initial_variants = db.query(ProductVariants).count()
            print(f"📊 Initial product variants: {initial_variants}")
        finally:
            db.close()
        
        # Execute Room Pipeline
        print("🚀 Executing Room Pipeline...")
        response = requests.post("http://localhost:8000/api/v1/etl-pipelines/4/execute")
        
        if response.status_code == 200:
            run_data = response.json()
            run_id = run_data.get('id')
            print(f"✅ Pipeline execution started - Run ID: {run_id}")
            
            # Monitor execution
            print("⏳ Monitoring execution progress...")
            monitor_execution(run_id)
            
        else:
            print(f"❌ Failed to start pipeline: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error executing pipeline: {e}")
        return False
    
    # Test 3: Verify final results
    print("\n3️⃣ Verifying Final Results")
    print("-" * 40)
    
    db = SessionLocal()
    try:
        # Check final variant count
        final_variants = db.query(ProductVariants).count()
        print(f"📊 Final product variants: {final_variants}")
        
        # Check latest run
        latest_run = db.query(ETLPipelineRun).filter(
            ETLPipelineRun.pipeline_id == 4
        ).order_by(ETLPipelineRun.started_at.desc()).first()
        
        if latest_run:
            print(f"📋 Latest Run Results:")
            print(f"   Status: {latest_run.status}")
            print(f"   Extracted: {latest_run.records_extracted}")
            print(f"   Transformed: {latest_run.records_transformed}")
            print(f"   Loaded: {latest_run.records_loaded}")
            
            # Verify fixes
            fixes_verified = []
            
            # Fix 1: Status Management
            if latest_run.status in ['completed', 'failed']:
                fixes_verified.append("✅ Status Management: Pipeline properly transitioned from 'running'")
            else:
                fixes_verified.append("❌ Status Management: Pipeline still stuck in 'running'")
            
            # Fix 2: Record Counting
            if (latest_run.records_extracted is not None and 
                latest_run.records_transformed is not None and 
                latest_run.records_loaded is not None):
                fixes_verified.append("✅ Record Counting: Statistics properly updated")
            else:
                fixes_verified.append("❌ Record Counting: Statistics still showing None")
            
            # Fix 3: Dependency Chain (implicit - if pipeline ran successfully)
            if latest_run.status == 'completed':
                fixes_verified.append("✅ Dependency Chain: Pipeline executed successfully")
            else:
                fixes_verified.append("⚠️ Dependency Chain: Check if dependencies were properly executed")
            
            print(f"\n🔍 Fix Verification:")
            for fix in fixes_verified:
                print(f"   {fix}")
                
            return latest_run.status == 'completed'
        else:
            print("❌ No pipeline runs found")
            return False
            
    finally:
        db.close()

def monitor_execution(run_id, max_wait_seconds=300):
    """Monitor pipeline execution progress"""
    start_time = time.time()
    check_interval = 5  # Check every 5 seconds
    
    while time.time() - start_time < max_wait_seconds:
        try:
            # Get pipeline status
            response = requests.get(f"http://localhost:8000/api/v1/etl-pipelines/4/runs")
            
            if response.status_code == 200:
                runs = response.json()
                if runs:
                    latest_run = runs[0]
                    status = latest_run.get('status')
                    extracted = latest_run.get('records_extracted')
                    transformed = latest_run.get('records_transformed')
                    loaded = latest_run.get('records_loaded')
                    
                    elapsed = time.time() - start_time
                    print(f"   [{elapsed:3.0f}s] {status} | E:{extracted} T:{transformed} L:{loaded}")
                    
                    if status == 'completed':
                        print(f"✅ Pipeline completed successfully in {elapsed:.1f} seconds!")
                        return True
                    elif status == 'failed':
                        error = latest_run.get('error_message', 'Unknown error')
                        print(f"❌ Pipeline failed: {error}")
                        return False
                        
        except Exception as e:
            print(f"⚠️ Monitoring error: {e}")
            
        time.sleep(check_interval)
    
    print(f"⏰ Pipeline execution timeout after {max_wait_seconds} seconds")
    return False

if __name__ == "__main__":
    success = test_room_pipeline_fixes()
    if success:
        print("\n🎉 ALL ROOM PIPELINE FIXES VERIFIED SUCCESSFULLY!")
    else:
        print("\n❌ Some fixes may need additional work")
