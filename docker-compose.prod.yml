version: '3.8'

services:
  # Backend API Service (Production)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: transfer-hub-backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - STORAGE_TYPE=${STORAGE_TYPE}
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=${ENVIRONMENT}
      - CORS_ORIGINS=${CORS_ORIGINS}
      - SALESFORCE_CLIENT_ID=${SALESFORCE_CLIENT_ID}
      - SALESFORCE_CLIENT_SECRET=${SALESFORCE_CLIENT_SECRET}
      - SALESFORCE_USERNAME=${SALESFORCE_USERNAME}
      - SALESFORCE_PASSWORD=${SALESFORCE_PASSWORD}
      - SALESFORCE_SECURITY_TOKEN=${SALESFORCE_SECURITY_TOKEN}
      - OPS_URL=${OPS_URL}
      - OPS_API_KEY=${OPS_API_KEY}
      - AZURE_STORAGE_ACCOUNT_NAME=${AZURE_STORAGE_ACCOUNT_NAME}
      - AZURE_STORAGE_ACCOUNT_KEY=${AZURE_STORAGE_ACCOUNT_KEY}
      - AZURE_STORAGE_CONTAINER_NAME=${AZURE_STORAGE_CONTAINER_NAME}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for Celery (Background Tasks)
  redis:
    image: redis:7-alpine
    container_name: transfer-hub-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  redis_data:
