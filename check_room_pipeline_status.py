#!/usr/bin/env python3
"""
Check Room Pipeline status and clean up stuck runs
"""
import sys
import os

# Add the backend directory to the Python path
sys.path.append('backend')

from app.database import SessionLocal
from app.models.etl_pipeline import ETLPipelineRun
from datetime import datetime

def check_room_pipeline_status():
    """Check Room Pipeline status and clean up stuck runs"""
    print("🔍 CHECKING ROOM PIPELINE STATUS")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        # Check for stuck Room Pipeline runs
        stuck_runs = db.query(ETLPipelineRun).filter(
            ETLPipelineRun.pipeline_id == 4,
            ETLPipelineRun.status == 'running'
        ).all()
        
        print(f"Found {len(stuck_runs)} stuck Room Pipeline runs:")
        for run in stuck_runs:
            runtime = datetime.utcnow() - run.started_at if run.started_at else None
            print(f"  Run {run.id}: {run.status} | E:{run.records_extracted} T:{run.records_transformed} L:{run.records_loaded} | Runtime: {runtime}")
            
        # Check recent runs
        recent_runs = db.query(ETLPipelineRun).filter(
            ETLPipelineRun.pipeline_id == 4
        ).order_by(ETLPipelineRun.started_at.desc()).limit(5).all()
        
        print(f"\nRecent Room Pipeline runs:")
        for run in recent_runs:
            runtime = datetime.utcnow() - run.started_at if run.started_at else None
            print(f"  Run {run.id}: {run.status} | E:{run.records_extracted} T:{run.records_transformed} L:{run.records_loaded} | Runtime: {runtime}")
            
        # Clean up stuck runs
        if stuck_runs:
            print(f"\n🛑 Cleaning up {len(stuck_runs)} stuck runs...")
            for run in stuck_runs:
                run.status = 'failed'
                run.completed_at = datetime.utcnow()
                run.error_message = "Cleaned up stuck run - status management fix"
            db.commit()
            print("✅ Stuck runs cleaned up")
            
        return len(stuck_runs) == 0
        
    except Exception as e:
        print(f"❌ Error checking Room Pipeline status: {e}")
        return False
    finally:
        db.close()

if __name__ == "__main__":
    check_room_pipeline_status()
