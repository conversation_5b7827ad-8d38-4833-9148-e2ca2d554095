# GitHub Actions Workflows

This directory contains automated workflows for the Flinkk Transfer Hub project.

## Workflows

### 🚀 Frontend Deployment (`deploy-frontend.yml`)

Automatically deploys the frontend to Vercel when changes are pushed to the `frontend/` directory.

**Triggers:**
- Push to `main` branch with changes in `frontend/**`
- Pull requests to `main` branch with changes in `frontend/**`
- Manual trigger via GitHub Actions UI

**Steps:**
1. **Code Quality Checks:**
   - TypeScript type checking
   - ESLint linting
   - Prettier formatting check
   - Next.js build verification

2. **Deployment:**
   - Triggers Vercel deployment via webhook (main branch only)
   - Provides deployment status and summary

**Vercel Integration:**
- Uses Vercel Deploy Hook: `https://api.vercel.com/v1/integrations/deploy/prj_pXCQR5SJ7jKybgCYSNjutrXq0XnE/1mKC2PlGiW`
- Deployment is triggered only on successful builds to `main` branch
- Pull requests run all checks but don't trigger deployment

### 🔧 Backend Deployment (`deploy-backend.yml`)

Deploys the backend to Azure App Service when changes are pushed to the `backend/` directory.

**Triggers:**
- Push to `main` branch with changes in `backend/**`
- Pull requests to `main` branch with changes in `backend/**`
- Manual trigger via GitHub Actions UI

## Usage

### For Frontend Changes:
1. Make changes in the `frontend/` directory
2. Push to a feature branch and create a PR
3. The workflow will run quality checks
4. After merging to `main`, deployment to Vercel will be triggered automatically

### For Backend Changes:
1. Make changes in the `backend/` directory
2. Push to a feature branch and create a PR
3. The workflow will run tests and build checks
4. After merging to `main`, deployment to Azure will be triggered automatically

## Monitoring

- Check the **Actions** tab in GitHub to monitor workflow runs
- Vercel deployments can be monitored in the Vercel dashboard
- Azure deployments can be monitored in the Azure portal

## Troubleshooting

### Frontend Deployment Issues:
- Verify the Vercel deploy hook URL is correct
- Check that the build passes locally with `npm run build`
- Ensure all TypeScript errors are resolved

### Backend Deployment Issues:
- Verify Azure publish profile is correctly configured in secrets
- Check that tests pass locally
- Ensure all Python dependencies are in `requirements.txt`
