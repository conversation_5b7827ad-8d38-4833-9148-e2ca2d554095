name: Deploy Azure Infrastructure

on:
  push:
    branches: [ main ]
    paths: [ 'infrastructure/**' ]
  workflow_dispatch:

jobs:
  deploy-infrastructure:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Azure Login
      uses: azure/login@v1
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}
        
    - name: Deploy Azure Storage Account
      uses: azure/arm-deploy@v1
      with:
        subscriptionId: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
        resourceGroupName: ${{ secrets.AZURE_RESOURCE_GROUP }}
        template: ./infrastructure/azure-storage.json
        parameters: |
          storageAccountName=${{ secrets.AZURE_STORAGE_ACCOUNT_NAME || 'flinkkstorageaccount' }}
          containerName=etl-pipeline-data
        deploymentName: 'flinkk-storage-deployment'
        
    - name: Get Storage Account Key
      id: storage-key
      run: |
        STORAGE_KEY=$(az storage account keys list \
          --resource-group ${{ secrets.AZURE_RESOURCE_GROUP }} \
          --account-name ${{ secrets.AZURE_STORAGE_ACCOUNT_NAME || 'flinkkstorageaccount' }} \
          --query '[0].value' \
          --output tsv)
        echo "::add-mask::$STORAGE_KEY"
        echo "storage-key=$STORAGE_KEY" >> $GITHUB_OUTPUT
        
    - name: Update App Service Configuration
      run: |
        az webapp config appsettings set \
          --resource-group ${{ secrets.AZURE_RESOURCE_GROUP }} \
          --name flinkk-transfer-hub \
          --settings \
            STORAGE_TYPE=azure_blob \
            AZURE_STORAGE_ACCOUNT_NAME=${{ secrets.AZURE_STORAGE_ACCOUNT_NAME || 'flinkkstorageaccount' }} \
            AZURE_STORAGE_ACCOUNT_KEY=${{ steps.storage-key.outputs.storage-key }} \
            AZURE_STORAGE_CONTAINER_NAME=etl-pipeline-data

    - name: Update GitHub Secret with Storage Key
      run: |
        # Update the AZURE_STORAGE_ACCOUNT_KEY secret in GitHub
        curl -X PUT \
          -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
          -H "Accept: application/vnd.github.v3+json" \
          https://api.github.com/repos/${{ github.repository }}/actions/secrets/AZURE_STORAGE_ACCOUNT_KEY \
          -d "{\"encrypted_value\":\"$(echo -n '${{ steps.storage-key.outputs.storage-key }}' | base64)\",\"key_id\":\"${{ secrets.GITHUB_SECRET_KEY_ID }}\"}"
            
    - name: Deployment Summary
      run: |
        echo "✅ Azure Storage Account deployed successfully!"
        echo "📦 Storage Account: ${{ secrets.AZURE_STORAGE_ACCOUNT_NAME || 'flinkkstorageaccount' }}"
        echo "🗂️ Container: etl-pipeline-data"
        echo "🔧 App Service configuration updated"
