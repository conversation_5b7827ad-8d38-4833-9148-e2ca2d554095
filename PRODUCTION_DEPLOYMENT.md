# Production Deployment Guide

This guide covers deploying the Flinkk Transfer Hub to production environments.

## Prerequisites

### Required Services
- **Database**: Azure PostgreSQL or compatible PostgreSQL service
- **Object Storage**: Azure Blob Storage or compatible S3-like service
- **Container Runtime**: Docker or Kubernetes
- **Redis**: For background task processing (optional but recommended)

### Required Credentials
- Salesforce Connected App credentials
- Database connection string
- Azure Blob Storage credentials
- Application secret key

## Environment Configuration

### 1. Create Production Environment File

Copy the example environment file and configure for production:

```bash
cp backend/.env.example backend/.env
```

### 2. Configure Required Variables

Edit `backend/.env` with your production values:

```env
# Database (Required)
DATABASE_URL=**************************************************/transfer_hub

# Application (Required)
SECRET_KEY=your-super-secure-secret-key-generate-a-new-one
ENVIRONMENT=production
CORS_ORIGINS=https://your-frontend-domain.com

# Salesforce (Required)
SALESFORCE_CLIENT_ID=your_salesforce_client_id
SALESFORCE_CLIENT_SECRET=your_salesforce_client_secret
SALESFORCE_USERNAME=your_salesforce_username
SALESFORCE_PASSWORD=your_salesforce_password
SALESFORCE_SECURITY_TOKEN=your_salesforce_security_token

# Storage (Required)
STORAGE_TYPE=azure_blob
AZURE_STORAGE_ACCOUNT_NAME=your_storage_account
AZURE_STORAGE_ACCOUNT_KEY=your_storage_key
AZURE_STORAGE_CONTAINER_NAME=etl-pipeline-data

# OPS Integration (Optional)
OPS_URL=https://your-ops-server.com
OPS_API_KEY=your_ops_api_key
```

## Deployment Options

### Option 1: Docker Compose (Recommended for single server)

1. **Build and deploy**:
```bash
# Production deployment (uses external database and storage)
docker-compose up -d

# Or specify the production compose file explicitly
docker-compose -f docker-compose.prod.yml up -d
```

2. **Run database migrations**:
```bash
docker-compose exec backend alembic upgrade head
```

### Option 2: Kubernetes (Recommended for scalable deployments)

1. **Create Kubernetes secrets**:
```bash
kubectl create secret generic transfer-hub-secrets \
  --from-literal=database-url="your-database-url" \
  --from-literal=secret-key="your-secret-key" \
  --from-literal=salesforce-client-id="your-client-id" \
  --from-literal=salesforce-client-secret="your-client-secret" \
  # ... add other secrets
```

2. **Deploy using Kubernetes manifests** (create these based on your infrastructure)

### Option 3: Azure Container Instances

Deploy directly to Azure using the production Docker image.

## Security Considerations

### 1. Environment Variables
- Never commit `.env` files to version control
- Use secure secret management (Azure Key Vault, Kubernetes secrets, etc.)
- Rotate secrets regularly

### 2. Network Security
- Use HTTPS/TLS for all external communications
- Restrict database access to application servers only
- Use VPN or private networks where possible

### 3. Database Security
- Enable SSL/TLS for database connections
- Use strong passwords and consider certificate-based authentication
- Regular security updates and patches

## Monitoring and Logging

### 1. Application Logs
- Logs are written to stdout/stderr for container environments
- Configure log aggregation (Azure Monitor, ELK stack, etc.)

### 2. Health Checks
- Health endpoint available at `/health`
- Monitor database connectivity and external service availability

### 3. Metrics
- Consider adding application metrics (Prometheus, Azure Monitor)
- Monitor ETL pipeline success rates and performance

## Backup and Recovery

### 1. Database Backups
- Configure automated database backups
- Test restore procedures regularly

### 2. Object Storage
- Enable versioning on your blob storage
- Consider cross-region replication for critical data

## Scaling Considerations

### 1. Horizontal Scaling
- The application is stateless and can be scaled horizontally
- Use load balancers for multiple instances

### 2. Background Tasks
- Redis is used for background task queuing
- Consider using managed Redis service for production

### 3. Database Performance
- Monitor database performance and optimize queries
- Consider read replicas for heavy read workloads

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Verify connection string format
   - Check network connectivity and firewall rules
   - Ensure database server is running

2. **Salesforce Authentication Failures**
   - Verify credentials are correct
   - Check if security token is required
   - Ensure Connected App permissions are properly configured

3. **Storage Access Issues**
   - Verify Azure Blob Storage credentials
   - Check container permissions
   - Ensure storage account is accessible from application network

### Debug Mode

For troubleshooting, you can temporarily enable debug logging:

```env
LOG_LEVEL=DEBUG
```

**Important**: Disable debug logging in production as it may expose sensitive information.

## Maintenance

### 1. Regular Updates
- Keep Docker images updated
- Apply security patches promptly
- Update dependencies regularly

### 2. Database Maintenance
- Run database maintenance tasks (VACUUM, ANALYZE for PostgreSQL)
- Monitor database size and performance

### 3. Storage Cleanup
- Implement retention policies for old ETL data
- Monitor storage usage and costs

## Support

For production support issues:
1. Check application logs first
2. Verify all external service connectivity
3. Review this deployment guide
4. Contact your system administrator or DevOps team
