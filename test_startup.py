#!/usr/bin/env python3
"""
Test script to diagnose application startup issues.
"""
import sys
import traceback

def test_imports():
    """Test imports step by step to identify where the hang occurs."""
    print("🔍 Testing application imports step by step...")
    
    try:
        print("1. Testing basic FastAPI imports...")
        from fastapi import FastAPI, HTTPException
        from fastapi.middleware.cors import CORSMiddleware
        from contextlib import asynccontextmanager
        print("1. ✅ Basic FastAPI imports successful")
        
        print("2. Testing settings import...")
        from app.config.settings import settings
        print("2. ✅ Settings imported successfully")
        
        print("3. Testing database imports...")
        from app.core.database import check_db_connection, create_tables
        print("3. ✅ Database imports successful")
        
        print("4. Testing API router import...")
        from app.api.api_v1.api import api_router
        print("4. ✅ API router imported successfully")
        
        print("5. Testing models import...")
        from app.models import MigrationJob, RawData, StagedData, TargetSchema, FieldMapping
        print("5. ✅ Models imported successfully")
        
        print("6. Testing main app import...")
        from app.main import app
        print("6. ✅ Main app imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during import: {e}")
        print("📋 Full traceback:")
        traceback.print_exc()
        return False

def test_simple_server():
    """Test starting a simple FastAPI server."""
    print("\n🚀 Testing simple server startup...")
    
    try:
        from fastapi import FastAPI
        import uvicorn
        
        # Create a minimal app
        simple_app = FastAPI(title="Test Server")
        
        @simple_app.get("/")
        def root():
            return {"message": "Test server is running!", "status": "ok"}
        
        @simple_app.get("/health")
        def health():
            return {"status": "healthy"}
        
        print("✅ Simple app created successfully")
        print("🚀 Starting server on http://localhost:8000")
        print("   Press Ctrl+C to stop the server")
        
        uvicorn.run(simple_app, host="127.0.0.1", port=8000, log_level="info")
        
    except Exception as e:
        print(f"❌ Error starting simple server: {e}")
        traceback.print_exc()

def main():
    """Main test function."""
    print("=" * 60)
    print("🧪 Application Startup Diagnostic Tool")
    print("=" * 60)
    
    # Test imports first
    if test_imports():
        print("\n✅ All imports successful!")
        
        # Ask user if they want to test the server
        try:
            response = input("\n🤔 Would you like to test the simple server? (y/n): ").lower().strip()
            if response in ['y', 'yes']:
                test_simple_server()
            else:
                print("✅ Diagnostic complete. Your application should be ready to run!")
        except KeyboardInterrupt:
            print("\n👋 Diagnostic cancelled by user")
    else:
        print("\n❌ Import test failed. Please fix the import issues before running the application.")

if __name__ == "__main__":
    main()
