# Resort Pipeline Field Mappings

This document describes the extended field mappings for the Resort Pipeline, which extracts data from Salesforce Resort__c objects and loads it into the PostgreSQL destination table.

## Overview

The Resort Pipeline has been extended to include additional fields from the Salesforce Resort__c object, providing a more comprehensive data extraction and transformation process.

## Field Mappings

### Source to Destination Mapping

| Salesforce Field | Destination Field | Data Type | Transformation | Description |
|------------------|-------------------|-----------|----------------|-------------|
| `IsDeleted` | `is_active` | Boolean | Invert boolean | Active status (IsDeleted=false → is_active=true) |
| `Name` | `name` | String | Trim whitespace | Resort name |
| `CurrencyIsoCode` | `currency` | String | Trim whitespace | ISO currency code |
| `CreatedDate` | `created_at` | DateTime | Direct mapping | Record creation timestamp |
| `LastModifiedDate` | `updated_at` | DateTime | Direct mapping | Last modification timestamp |
| `Country__c` | `country` | String | Trim whitespace | Country name |
| `Description__c` | `description` | Text | Trim whitespace | Resort description |

## Database Schema

### destination Table Structure

```sql
CREATE TABLE destination (
    id SERIAL PRIMARY KEY,
    country VARCHAR(255),
    name VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    currency VARCHAR(10),
    description TEXT,
    pipeline_run_id INTEGER REFERENCES etl_pipeline_runs(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_destination_country ON destination(country);
CREATE INDEX idx_destination_is_active ON destination(is_active);
CREATE INDEX idx_destination_currency ON destination(currency);
```

## Transformation Logic

### Boolean Inversion (IsDeleted → is_active)

The `IsDeleted` field from Salesforce is inverted to create the `is_active` field:

- `IsDeleted = true` → `is_active = false` (deleted/inactive resort)
- `IsDeleted = false` → `is_active = true` (active resort)
- `IsDeleted = null` → `is_active = null` (unknown status)

### String Transformations

All string fields undergo the following transformations:
- Trim leading and trailing whitespace
- Handle null values appropriately
- Preserve empty strings as null if `clean_nulls` is enabled

### DateTime Fields

DateTime fields (`CreatedDate`, `LastModifiedDate`) are mapped directly without transformation, preserving the original Salesforce timestamps.

## Pipeline Configuration

### Source Configuration

```json
{
  "source_type": "salesforce",
  "source_object": "Resort__c",
  "source_fields": [
    "IsDeleted",
    "Name",
    "CurrencyIsoCode", 
    "CreatedDate",
    "LastModifiedDate",
    "Country__c",
    "Description__c"
  ]
}
```

### Destination Configuration

```json
{
  "destination_type": "postgresql",
  "destination_table": "destination",
  "destination_fields": {
    "is_active": "IsDeleted",
    "name": "Name",
    "currency": "CurrencyIsoCode",
    "created_at": "CreatedDate", 
    "updated_at": "LastModifiedDate",
    "country": "Country__c",
    "description": "Description__c"
  }
}
```

### Transformation Configuration

```json
{
  "clean_nulls": false,
  "trim_whitespace": true,
  "field_transformations": {
    "is_active": {
      "type": "invert_boolean",
      "source_field": "IsDeleted",
      "description": "Convert IsDeleted to is_active by inverting the boolean value"
    }
  }
}
```

## Setup Instructions

### 1. Run Database Migration

```bash
cd backend
alembic upgrade head
```

### 2. Update Pipeline Configuration

```bash
cd backend
python update_resort_pipeline_fields.py
```

### 3. Complete Setup (All-in-One)

```bash
cd backend
python setup_extended_resort_pipeline.py
```

## Verification

After setup, verify the configuration:

1. **Database Schema**: Check that new columns exist in the destination table
2. **Pipeline Config**: Verify the pipeline has the correct field mappings
3. **Test Execution**: Run the pipeline and check the transformation results

### Sample Query

```sql
-- Check the destination table structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'destination' 
ORDER BY ordinal_position;

-- Sample data query
SELECT 
    name,
    country,
    is_active,
    currency,
    description,
    created_at,
    updated_at
FROM destination 
LIMIT 10;
```

## Troubleshooting

### Common Issues

1. **Missing Columns**: Run the database migration if new columns don't exist
2. **Transformation Errors**: Check the pipeline logs for boolean conversion issues
3. **Null Values**: Verify the `clean_nulls` setting matches your requirements

### Debug Queries

```sql
-- Check for records with transformation issues
SELECT * FROM destination WHERE is_active IS NULL AND name IS NOT NULL;

-- Verify boolean inversion is working
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_resorts,
    COUNT(CASE WHEN is_active = false THEN 1 END) as inactive_resorts
FROM destination;
```

## Performance Considerations

- Indexes are created on frequently queried fields (`country`, `is_active`, `currency`)
- The pipeline processes records in batches for optimal performance
- Raw and processed data are stored in object storage for audit and replay capabilities

## Data Quality

The pipeline includes validation and error handling:
- Invalid boolean values are converted to null
- String fields are trimmed and normalized
- Transformation errors are logged with detailed information
- Failed records are skipped but logged for review
