version: '3.8'

services:
  # Backend API Service (Development)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: transfer-hub-backend-dev
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=${DATABASE_URL:-********************************************/transfer_hub}
      - STORAGE_TYPE=${STORAGE_TYPE:-minio}
      - MINIO_ENDPOINT=${MINIO_ENDPOINT:-minio:9000}
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY:-minioadmin}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY:-minioadmin123}
      - MINIO_BUCKET_NAME=${MINIO_BUCKET_NAME:-etl-pipeline-data}
      - MINIO_SECURE=${MINIO_SECURE:-false}
      - SECRET_KEY=${SECRET_KEY:-dev-secret-key-change-in-production}
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - CORS_ORIGINS=${CORS_ORIGINS:-http://localhost:3000,http://127.0.0.1:3000}
      - SALESFORCE_CLIENT_ID=${SALESFORCE_CLIENT_ID}
      - SALESFORCE_CLIENT_SECRET=${SALESFORCE_CLIENT_SECRET}
      - SALESFORCE_USERNAME=${SALESFORCE_USERNAME}
      - SALESFORCE_PASSWORD=${SALESFORCE_PASSWORD}
      - SALESFORCE_SECURITY_TOKEN=${SALESFORCE_SECURITY_TOKEN}
      - OPS_URL=${OPS_URL:-http://localhost:9000}
      - OPS_API_KEY=${OPS_API_KEY}
    volumes:
      - ./backend:/app
    depends_on:
      - minio
      - postgres
    restart: unless-stopped

  # MinIO Object Storage (Local Development)
  minio:
    image: minio/minio:latest
    container_name: transfer-hub-minio-dev
    ports:
      - "9002:9000"
      - "9003:9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ACCESS_KEY:-minioadmin}
      MINIO_ROOT_PASSWORD: ${MINIO_SECRET_KEY:-minioadmin123}
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # PostgreSQL Database (Local Development)
  postgres:
    image: postgres:15
    container_name: transfer-hub-postgres-dev
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-transfer_hub}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for Celery (Background Tasks)
  redis:
    image: redis:7-alpine
    container_name: transfer-hub-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  minio_data:
  postgres_data:
  redis_data:
