# Flinkk Transfer Hub - ETL Migration System

A scalable ETL migration system that extracts data from Salesforce, allows preview and manual edits via a web interface, and syncs the data to Medusa (or other CRM systems).

## Architecture

- **Frontend**: Next.js (React) with TypeScript - Interactive UI with paginated preview and editable tables
- **Backend**: Python with FastAPI - APIs, extraction, transformation, staging of data
- **Database**: PostgreSQL - Store raw and staged data
- **Background Processing**: FastAPI BackgroundTasks (Phase 1) → Celery + Redis (Phase 2)
- **Target-Agnostic Design**: Support for multiple CRM systems via adapter pattern

## Project Structure

```
flinkk-transfer-hub/
├── backend/                 # FastAPI application
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── config/         # Configuration management
│   │   ├── models/         # SQLAlchemy models
│   │   ├── schemas/        # Pydantic models
│   │   ├── etl/            # ETL (Extract, Transform, Load) module
│   │   │   ├── extractors/ # Data extraction (Salesforce, APIs)
│   │   │   ├── transformers/ # Data transformation logic
│   │   │   ├── loaders/    # Target adapters (Medusa, etc.)
│   │   │   ├── pipelines/  # Pipeline orchestration
│   │   │   └── utils/      # ETL utilities
│   │   └── services/       # Business logic
│   ├── scripts/           # Standalone ETL scripts
│   ├── alembic/           # Database migrations
│   └── requirements.txt
├── frontend/              # Next.js application
│   ├── src/
│   │   ├── components/    # React components
│   │   ├── pages/         # Next.js pages
│   │   ├── hooks/         # Custom React hooks
│   │   ├── services/      # API client
│   │   └── types/         # TypeScript types
│   └── package.json
├── docker-compose.yml     # Local development setup
└── README.md
```

## API Endpoints

### Backend (FastAPI)

- `POST /start-migration` - Start ETL migration for an object
- `GET /staged/{object_name}` - Get paginated staged data
- `PATCH /staged/{object_name}/{record_id}` - Update staged record
- `GET /migration-status/{job_id}` - Check migration job status

## Development Setup

### Prerequisites

- Python 3.9+
- Node.js 18+
- Access to Azure PostgreSQL database

### Backend Setup

```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### Frontend Setup

```bash
cd frontend
npm install
npm run dev
```

### Database Setup

This project connects to your Azure PostgreSQL database. No local database setup required!

## Environment Variables

Create `.env` files in both `backend/` and `frontend/` directories:

### Backend `.env`

Copy the example file and configure for your environment:

```bash
cp backend/.env.example backend/.env
```

See `backend/.env.example` for all available configuration options.

**Required for Production:**
- `DATABASE_URL`: PostgreSQL connection string
- `SECRET_KEY`: Secure random key for JWT tokens
- `ENVIRONMENT`: Set to "production"
- `STORAGE_TYPE`: Set to "azure_blob" for production
- Salesforce credentials
- Azure Blob Storage credentials

### Frontend `.env.local`
```
NEXT_PUBLIC_API_URL=http://localhost:8000
```

## Deployment

### Development
```bash
# Use development compose file with local services
docker-compose -f docker-compose.dev.yml up -d
```

### Production
```bash
# Use production compose file (requires external database and storage)
docker-compose up -d
# or
docker-compose -f docker-compose.prod.yml up -d
```

See [PRODUCTION_DEPLOYMENT.md](PRODUCTION_DEPLOYMENT.md) for detailed production deployment guide.

## Storage Architecture

### Local Development
- **MinIO**: Object storage running on `localhost:9002`
- **Console**: Available at `http://localhost:9003`
- **Credentials**: Configurable via environment variables

### Production
- **Azure Blob Storage**: Cloud object storage
- **Container**: Configurable via `AZURE_STORAGE_CONTAINER_NAME`
- **Security**: HTTPS-only, private access, encrypted storage

The system automatically detects the environment and uses the appropriate storage backend.

## Phase 1 vs Phase 2

### Phase 1 (Current)
- FastAPI BackgroundTasks for job processing
- Basic job status tracking
- Single-instance deployment
- Unified storage interface (MinIO + Azure Blob)

### Phase 2 (Future)
- Celery + Redis for distributed task processing
- Advanced job monitoring and retry logic
- Multi-instance scalability
- Real-time progress updates via WebSockets

## Contributing

1. Follow the established folder structure
2. Use type hints in Python code
3. Use TypeScript for frontend code
4. Write tests for new functionality
5. Update documentation as needed

## License

MIT License
